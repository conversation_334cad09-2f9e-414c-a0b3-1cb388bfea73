-- Create scheduled_posts table
CREATE TABLE IF NOT EXISTS scheduled_posts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  platform VARCHAR(50) NOT NULL DEFAULT 'twitter',
  scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'posted', 'failed', 'cancelled')),
  platform_post_id VARCHAR(255),
  error_message TEXT,
  posted_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create posted_content table
CREATE TABLE IF NOT EXISTS posted_content (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  platform VARCHAR(50) NOT NULL DEFAULT 'twitter',
  platform_post_id VARCHAR(255) NOT NULL,
  scheduled_post_id UUID REFERENCES scheduled_posts(id) ON DELETE SET NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'posted' CHECK (status IN ('posted', 'deleted', 'failed')),
  posted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_scheduled_posts_user_id ON scheduled_posts(user_id);
CREATE INDEX IF NOT EXISTS idx_scheduled_posts_scheduled_time ON scheduled_posts(scheduled_time);
CREATE INDEX IF NOT EXISTS idx_scheduled_posts_status ON scheduled_posts(status);
CREATE INDEX IF NOT EXISTS idx_posted_content_user_id ON posted_content(user_id);
CREATE INDEX IF NOT EXISTS idx_posted_content_platform_post_id ON posted_content(platform_post_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_scheduled_posts_updated_at
  BEFORE UPDATE ON scheduled_posts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE scheduled_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE posted_content ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for scheduled_posts
CREATE POLICY "Users can view their own scheduled posts" ON scheduled_posts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own scheduled posts" ON scheduled_posts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own scheduled posts" ON scheduled_posts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own scheduled posts" ON scheduled_posts
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for posted_content
CREATE POLICY "Users can view their own posted content" ON posted_content
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own posted content" ON posted_content
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Allow service role to access all data (for cron jobs)
CREATE POLICY "Service role can access all scheduled posts" ON scheduled_posts
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can access all posted content" ON posted_content
  FOR ALL USING (auth.role() = 'service_role');

-- Create X account connection tables
CREATE TABLE IF NOT EXISTS x_accounts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  x_user_id VARCHAR(255) NOT NULL,
  username VARCHAR(255) NOT NULL,
  name VARCHAR(255),
  profile_image_url TEXT,
  access_token TEXT NOT NULL,
  access_secret TEXT NOT NULL,
  is_active BOOLEAN DEFAULT true,
  connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  disconnected_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, x_user_id)
);

-- Create temporary OAuth tokens table
CREATE TABLE IF NOT EXISTS x_oauth_tokens (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  oauth_token VARCHAR(255) NOT NULL,
  oauth_token_secret VARCHAR(255) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(oauth_token)
);

-- Create indexes for X account tables
CREATE INDEX IF NOT EXISTS idx_x_accounts_user_id ON x_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_x_accounts_x_user_id ON x_accounts(x_user_id);
CREATE INDEX IF NOT EXISTS idx_x_accounts_is_active ON x_accounts(is_active);
CREATE INDEX IF NOT EXISTS idx_x_oauth_tokens_user_id ON x_oauth_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_x_oauth_tokens_oauth_token ON x_oauth_tokens(oauth_token);

-- Create triggers for updated_at on X accounts
CREATE TRIGGER update_x_accounts_updated_at
  BEFORE UPDATE ON x_accounts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security for X account tables
ALTER TABLE x_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE x_oauth_tokens ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for x_accounts
CREATE POLICY "Users can view their own X accounts" ON x_accounts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own X accounts" ON x_accounts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own X accounts" ON x_accounts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own X accounts" ON x_accounts
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for x_oauth_tokens
CREATE POLICY "Users can view their own OAuth tokens" ON x_oauth_tokens
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own OAuth tokens" ON x_oauth_tokens
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own OAuth tokens" ON x_oauth_tokens
  FOR DELETE USING (auth.uid() = user_id);

-- Allow service role to access all X account data
CREATE POLICY "Service role can access all X accounts" ON x_accounts
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can access all OAuth tokens" ON x_oauth_tokens
  FOR ALL USING (auth.role() = 'service_role');
