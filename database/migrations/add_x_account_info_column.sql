-- Add x_account_info column to user_profiles table if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND column_name = 'x_account_info'
    ) THEN
        ALTER TABLE user_profiles ADD COLUMN x_account_info JSONB;
        RAISE NOTICE 'Added x_account_info column to user_profiles table';
    ELSE
        RAISE NOTICE 'x_account_info column already exists in user_profiles table';
    END IF;
END $$;

-- Create index on x_account_info for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_x_account_info ON user_profiles USING GIN (x_account_info);

-- Add comment to document the column
COMMENT ON COLUMN user_profiles.x_account_info IS 'JSON object containing X (Twitter) account connection information including access tokens, username, profile data, and connection status';
