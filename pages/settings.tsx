// pages/settings.tsx
import React, { useState, useEffect } from 'react';
import SidebarLayout from '../components/SidebarLayoutSimple';
import { Save, Plus, Trash2, Bot, Key, Twitter, User, Bell, Shield, Zap, Edit3, Crown } from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import { supabase } from '../lib/supabase';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const SettingsPage: NextPageWithLayout = () => {
  const { user, profile, updateProfile } = useUser();
  const [activeTab, setActiveTab] = useState('agent-e');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Agent E Settings
  const [projectName, setProjectName] = useState('My AI Project');
  const [projectDescription, setProjectDescription] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [brandVoice, setBrandVoice] = useState('professional');
  const [customPrompts, setCustomPrompts] = useState([
    { id: 1, name: 'Product Launch', prompt: 'Create engaging content for product launches with excitement and clear benefits' },
    { id: 2, name: 'Educational', prompt: 'Write informative content that teaches and provides value to the audience' }
  ]);

  // Profile Settings
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [avatarUrl, setAvatarUrl] = useState('');

  // X Integration Settings
  const [xAccountConnected, setXAccountConnected] = useState(false);
  const [xAccountInfo, setXAccountInfo] = useState<{
    username?: string;
    name?: string;
    profile_image_url?: string;
  } | null>(null);
  const [connectingX, setConnectingX] = useState(false);

  // Load settings from Supabase
  useEffect(() => {
    if (user) {
      loadSettings();
      loadXAccountStatus();
      setFullName(profile?.full_name || user.user_metadata?.full_name || '');
      setEmail(user.email || '');
      setAvatarUrl(profile?.avatar_url || '');
    }
  }, [user, profile]);

  // Handle URL parameters for success/error messages
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const error = urlParams.get('error');

    if (success === 'connected') {
      alert('X account connected successfully!');
      loadXAccountStatus(); // Refresh the status
      // Clean up URL
      window.history.replaceState({}, '', '/settings?tab=integrations');
    } else if (error) {
      const errorMessages = {
        'store_failed': 'Failed to save X account connection. Please try again.',
        'auth_failed': 'X authorization failed. Please try again.',
        'invalid_token': 'Invalid authorization token. Please try again.'
      };
      alert(errorMessages[error] || 'An error occurred. Please try again.');
      // Clean up URL
      window.history.replaceState({}, '', '/settings?tab=integrations');
    }
  }, []);

  const loadSettings = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('agent_e_settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error loading settings:', error);
        return;
      }

      if (data) {
        setProjectName(data.project_name || 'My AI Project');
        setProjectDescription(data.project_description || '');
        setTargetAudience(data.target_audience || '');
        setBrandVoice(data.brand_voice || 'professional');
        setCustomPrompts(data.custom_prompts || []);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveAgentESettings = async () => {
    if (!user) return;

    setSaving(true);
    try {
      const { error } = await supabase
        .from('agent_e_settings')
        .upsert({
          user_id: user.id,
          project_name: projectName,
          project_description: projectDescription,
          target_audience: targetAudience,
          brand_voice: brandVoice,
          custom_prompts: customPrompts
        });

      if (error) {
        console.error('Error saving settings:', error);
        alert('Error saving settings. Please try again.');
      } else {
        alert('Settings saved successfully!');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Error saving settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const saveProfileSettings = async () => {
    if (!user) {
      alert('You must be logged in to update your profile.');
      return;
    }

    setSaving(true);
    try {
      // Try to update the profile
      const { error } = await updateProfile({
        full_name: fullName,
        avatar_url: avatarUrl || null
      });

      if (error) {
        console.error('Error updating profile:', error);

        // If the table doesn't exist, try to create it first
        if (error.code === '42P01' || error.message?.includes('relation "user_profiles" does not exist')) {
          alert('Database setup required. Please run the database schema first, then try again.');
        } else {
          alert(`Error updating profile: ${error.message || 'Please try again.'}`);
        }
      } else {
        alert('Profile updated successfully!');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Error updating profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const loadXAccountStatus = async () => {
    if (!user) return;

    try {
      const response = await fetch(`/api/x/account-status?userId=${user.id}`);
      const data = await response.json();



      if (response.ok && data.connected) {
        setXAccountConnected(true);
        setXAccountInfo(data.accountInfo);
      } else {
        setXAccountConnected(false);
        setXAccountInfo(null);
      }
    } catch (error) {
      console.error('Error loading X account status:', error);
      setXAccountConnected(false);
      setXAccountInfo(null);
    }
  };

  const handleConnectX = async () => {
    if (!user) {
      alert('Please log in first');
      return;
    }

    setConnectingX(true);
    try {
      const response = await fetch('/api/x/auth-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id }),
      });

      const data = await response.json();

      if (response.ok) {
        // Redirect directly to X authorization
        window.location.href = data.authUrl;
      } else {
        alert(data.error || 'Failed to initiate X connection');
        setConnectingX(false);
      }
    } catch (error) {
      console.error('Error connecting to X:', error);
      alert('Failed to connect to X. Please try again.');
      setConnectingX(false);
    }
  };



  const handleDisconnectX = async () => {
    if (!user) return;

    try {
      const response = await fetch('/api/x/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id }),
      });

      if (response.ok) {
        setXAccountConnected(false);
        setXAccountInfo(null);
        alert('X account disconnected successfully');
      } else {
        const data = await response.json();
        alert(data.error || 'Failed to disconnect X account');
      }
    } catch (error) {
      console.error('Error disconnecting X:', error);
      alert('Failed to disconnect X account. Please try again.');
    }
  };

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    surface: '#FFFFFF',
    background: '#FFF8F3',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    border: '#F5E6D3'
  };

  const tabs = [
    { id: 'agent-e', label: 'Agent E', icon: Bot },
    { id: 'account', label: 'Account', icon: User },
    { id: 'integrations', label: 'Integrations', icon: Zap },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield }
  ];

  const handleSavePrompt = (id: number, newPrompt: string) => {
    setCustomPrompts(prev => prev.map(p => p.id === id ? { ...p, prompt: newPrompt } : p));
  };

  const addNewPrompt = () => {
    const newId = Math.max(...customPrompts.map(p => p.id)) + 1;
    setCustomPrompts(prev => [...prev, { id: newId, name: 'New Prompt', prompt: '' }]);
  };

  const deletePrompt = (id: number) => {
    setCustomPrompts(prev => prev.filter(p => p.id !== id));
  };

  return (
    <div style={{
      padding: '40px 60px',
      height: '100vh',
      overflow: 'auto',
      background: colors.background
    }}>
      {/* Header */}
      <div style={{
        marginBottom: '40px'
      }}>
        <h1 style={{
          color: colors.text.primary,
          margin: 0,
          fontSize: '32px',
          fontWeight: '300',
          letterSpacing: '-1px',
          marginBottom: '8px',
          fontFamily: 'Georgia, serif'
        }}>
          Settings
        </h1>
        <p style={{
          color: colors.text.secondary,
          margin: 0,
          fontSize: '16px'
        }}>
          Configure Agent E and manage your account preferences
        </p>
      </div>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        gap: '8px',
        marginBottom: '40px',
        borderBottom: `1px solid ${colors.border}`,
        paddingBottom: '0'
      }}>
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 20px',
              background: activeTab === tab.id ? colors.surface : 'transparent',
              border: activeTab === tab.id ? `1px solid ${colors.border}` : '1px solid transparent',
              borderBottom: activeTab === tab.id ? `1px solid ${colors.surface}` : '1px solid transparent',
              borderRadius: '8px 8px 0 0',
              fontSize: '14px',
              fontWeight: '500',
              color: activeTab === tab.id ? colors.text.primary : colors.text.secondary,
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              marginBottom: '-1px'
            }}
          >
            <tab.icon size={16} />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div style={{
        background: colors.surface,
        borderRadius: '12px',
        padding: '32px',
        border: `1px solid ${colors.border}`,
        minHeight: '500px'
      }}>
        {activeTab === 'agent-e' && (
          <div>
            <h2 style={{
              color: colors.text.primary,
              fontSize: '24px',
              fontWeight: '600',
              marginBottom: '24px',
              display: 'flex',
              alignItems: 'center',
              gap: '12px'
            }}>
              <Bot size={24} color={colors.primary} />
              Agent E Configuration
            </h2>

            {/* Project Information */}
            <div style={{ marginBottom: '32px' }}>
              <h3 style={{
                color: colors.text.primary,
                fontSize: '18px',
                fontWeight: '600',
                marginBottom: '16px'
              }}>
                Project Information
              </h3>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    color: colors.text.secondary,
                    fontSize: '14px',
                    fontWeight: '500',
                    marginBottom: '8px'
                  }}>
                    Project Name
                  </label>
                  <input
                    type="text"
                    value={projectName}
                    onChange={(e) => setProjectName(e.target.value)}
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '8px',
                      fontSize: '14px',
                      background: colors.background,
                      outline: 'none'
                    }}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    color: colors.text.secondary,
                    fontSize: '14px',
                    fontWeight: '500',
                    marginBottom: '8px'
                  }}>
                    Brand Voice
                  </label>
                  <select
                    value={brandVoice}
                    onChange={(e) => setBrandVoice(e.target.value)}
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '8px',
                      fontSize: '14px',
                      background: colors.background,
                      outline: 'none'
                    }}
                  >
                    <option value="professional">Professional</option>
                    <option value="casual">Casual & Friendly</option>
                    <option value="technical">Technical</option>
                    <option value="creative">Creative & Fun</option>
                    <option value="authoritative">Authoritative</option>
                  </select>
                </div>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{
                  display: 'block',
                  color: colors.text.secondary,
                  fontSize: '14px',
                  fontWeight: '500',
                  marginBottom: '8px'
                }}>
                  Project Description
                </label>
                <textarea
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  placeholder="Describe your project, product, or service so Agent E can create relevant content..."
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: `1px solid ${colors.border}`,
                    borderRadius: '8px',
                    fontSize: '14px',
                    background: colors.background,
                    outline: 'none',
                    minHeight: '100px',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  color: colors.text.secondary,
                  fontSize: '14px',
                  fontWeight: '500',
                  marginBottom: '8px'
                }}>
                  Target Audience
                </label>
                <input
                  type="text"
                  value={targetAudience}
                  onChange={(e) => setTargetAudience(e.target.value)}
                  placeholder="e.g., Tech entrepreneurs, Small business owners, Developers..."
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: `1px solid ${colors.border}`,
                    borderRadius: '8px',
                    fontSize: '14px',
                    background: colors.background,
                    outline: 'none'
                  }}
                />
              </div>
            </div>

            {/* Custom Prompts */}
            <div style={{ marginBottom: '32px' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '16px'
              }}>
                <h3 style={{
                  color: colors.text.primary,
                  fontSize: '18px',
                  fontWeight: '600',
                  margin: 0
                }}>
                  Custom Prompts
                </h3>
                <button
                  onClick={addNewPrompt}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                    padding: '8px 16px',
                    background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer'
                  }}
                >
                  <Plus size={16} />
                  Add Prompt
                </button>
              </div>

              {customPrompts.map((prompt) => (
                <div key={prompt.id} style={{
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  padding: '16px',
                  marginBottom: '12px',
                  background: colors.background
                }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '12px'
                  }}>
                    <input
                      type="text"
                      value={prompt.name}
                      onChange={(e) => {
                        setCustomPrompts(prev => prev.map(p =>
                          p.id === prompt.id ? { ...p, name: e.target.value } : p
                        ));
                      }}
                      style={{
                        background: 'transparent',
                        border: 'none',
                        fontSize: '16px',
                        fontWeight: '600',
                        color: colors.text.primary,
                        outline: 'none',
                        flex: 1
                      }}
                    />
                    <button
                      onClick={() => deletePrompt(prompt.id)}
                      style={{
                        background: 'none',
                        border: 'none',
                        color: colors.text.tertiary,
                        cursor: 'pointer',
                        padding: '4px'
                      }}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                  <textarea
                    value={prompt.prompt}
                    onChange={(e) => handleSavePrompt(prompt.id, e.target.value)}
                    placeholder="Enter your custom prompt for Agent E..."
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      background: colors.surface,
                      outline: 'none',
                      minHeight: '80px',
                      resize: 'vertical'
                    }}
                  />
                </div>
              ))}
            </div>

            {/* Save Button */}
            <button
              onClick={saveAgentESettings}
              disabled={saving || !user}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '12px 24px',
                background: saving || !user ? colors.text.tertiary : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: saving || !user ? 'not-allowed' : 'pointer',
                boxShadow: saving || !user ? 'none' : `0 4px 12px ${colors.primary}30`,
                opacity: saving || !user ? 0.6 : 1
              }}
            >
              <Save size={16} />
              {saving ? 'Saving...' : 'Save Agent E Settings'}
            </button>
          </div>
        )}

        {activeTab === 'integrations' && (
          <div>
            <h2 style={{
              color: colors.text.primary,
              fontSize: '24px',
              fontWeight: '600',
              marginBottom: '24px',
              display: 'flex',
              alignItems: 'center',
              gap: '12px'
            }}>
              <Zap size={24} color={colors.primary} />
              Integrations
            </h2>

            {/* X Account Connection */}
            <div style={{
              border: `1px solid ${colors.border}`,
              borderRadius: '12px',
              padding: '24px',
              marginBottom: '24px',
              background: colors.background
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                marginBottom: '16px'
              }}>
                <Twitter size={24} color={colors.primary} />
                <h3 style={{
                  color: colors.text.primary,
                  fontSize: '18px',
                  fontWeight: '600',
                  margin: 0
                }}>
                  X (Twitter) Account
                </h3>
              </div>

              <p style={{
                color: colors.text.secondary,
                fontSize: '14px',
                marginBottom: '20px'
              }}>
                Connect your X account to enable automated posting and content scheduling through our premium service.
              </p>

              {xAccountConnected && xAccountInfo ? (
                // Connected State
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: '16px',
                  background: '#F0FDF4',
                  border: '1px solid #BBF7D0',
                  borderRadius: '8px',
                  marginBottom: '16px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    {xAccountInfo.profile_image_url && (
                      <img
                        src={xAccountInfo.profile_image_url}
                        alt="Profile"
                        style={{
                          width: '40px',
                          height: '40px',
                          borderRadius: '50%',
                          objectFit: 'cover'
                        }}
                      />
                    )}
                    <div>
                      <div style={{
                        color: '#065F46',
                        fontSize: '16px',
                        fontWeight: '600'
                      }}>
                        {xAccountInfo.name || 'Connected Account'}
                      </div>
                      <div style={{
                        color: '#047857',
                        fontSize: '14px'
                      }}>
                        @{xAccountInfo.username}
                      </div>
                    </div>
                  </div>
                  <div style={{
                    padding: '6px 12px',
                    background: '#10B981',
                    color: 'white',
                    borderRadius: '6px',
                    fontSize: '12px',
                    fontWeight: '600'
                  }}>
                    Connected
                  </div>
                </div>
              ) : (
                // Not Connected State
                <div style={{
                  padding: '16px',
                  background: '#FEF3C7',
                  border: '1px solid #FDE68A',
                  borderRadius: '8px',
                  marginBottom: '16px',
                  textAlign: 'center'
                }}>
                  <div style={{
                    color: '#92400E',
                    fontSize: '14px',
                    marginBottom: '8px'
                  }}>
                    No X account connected
                  </div>
                  <div style={{
                    color: '#B45309',
                    fontSize: '12px'
                  }}>
                    Connect your account to start scheduling posts
                  </div>
                </div>
              )}

              <div style={{ display: 'flex', gap: '12px' }}>
                {xAccountConnected ? (
                  <button
                    onClick={handleDisconnectX}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      padding: '10px 20px',
                      background: '#FEE2E2',
                      color: '#DC2626',
                      border: '1px solid #FECACA',
                      borderRadius: '8px',
                      fontSize: '14px',
                      fontWeight: '500',
                      cursor: 'pointer'
                    }}
                  >
                    <Twitter size={16} />
                    Disconnect Account
                  </button>
                ) : (
                  <button
                    onClick={() => {
                      console.log('Connect X button clicked');
                      handleConnectX();
                    }}
                    disabled={connectingX}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      padding: '12px 24px',
                      background: connectingX ? colors.text.tertiary : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '14px',
                      fontWeight: '600',
                      cursor: connectingX ? 'not-allowed' : 'pointer',
                      opacity: connectingX ? 0.6 : 1
                    }}
                  >
                    <Twitter size={16} />
                    {connectingX ? 'Connecting...' : 'Connect X Account'}
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'account' && (
          <div>
            <h2 style={{
              color: colors.text.primary,
              fontSize: '24px',
              fontWeight: '600',
              marginBottom: '24px',
              display: 'flex',
              alignItems: 'center',
              gap: '12px'
            }}>
              <User size={24} color={colors.primary} />
              Account Settings
            </h2>

            {/* Profile Information */}
            <div style={{ marginBottom: '32px' }}>
              <h3 style={{
                color: colors.text.primary,
                fontSize: '18px',
                fontWeight: '600',
                marginBottom: '16px'
              }}>
                Profile Information
              </h3>

              <div style={{ display: 'grid', gap: '16px', maxWidth: '400px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    color: colors.text.primary,
                    fontSize: '14px',
                    fontWeight: '600',
                    marginBottom: '6px'
                  }}>
                    Full Name
                  </label>
                  <input
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '8px',
                      fontSize: '14px',
                      background: colors.surface,
                      color: colors.text.primary
                    }}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    color: colors.text.primary,
                    fontSize: '14px',
                    fontWeight: '600',
                    marginBottom: '6px'
                  }}>
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={email}
                    disabled
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '8px',
                      fontSize: '14px',
                      background: '#F9F9F9',
                      color: colors.text.secondary,
                      cursor: 'not-allowed'
                    }}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    color: colors.text.primary,
                    fontSize: '14px',
                    fontWeight: '600',
                    marginBottom: '6px'
                  }}>
                    Avatar URL (Optional)
                  </label>
                  <input
                    type="url"
                    value={avatarUrl}
                    onChange={(e) => setAvatarUrl(e.target.value)}
                    placeholder="https://example.com/your-avatar.jpg"
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '8px',
                      fontSize: '14px',
                      background: colors.surface,
                      color: colors.text.primary
                    }}
                  />
                  <p style={{
                    fontSize: '12px',
                    color: colors.text.tertiary,
                    marginTop: '4px',
                    marginBottom: '0'
                  }}>
                    Enter a URL to your profile picture
                  </p>
                </div>
              </div>
            </div>

            {/* Subscription Information */}
            <div style={{ marginBottom: '32px' }}>
              <h3 style={{
                color: colors.text.primary,
                fontSize: '18px',
                fontWeight: '600',
                marginBottom: '16px'
              }}>
                Subscription
              </h3>

              <div style={{
                background: `linear-gradient(135deg, ${colors.primary}15 0%, ${colors.primaryLight}15 100%)`,
                border: `1px solid ${colors.primary}30`,
                borderRadius: '12px',
                padding: '20px',
                maxWidth: '400px'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  marginBottom: '12px'
                }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Crown size={16} color="white" />
                  </div>
                  <div>
                    <div style={{
                      color: colors.text.primary,
                      fontSize: '16px',
                      fontWeight: '600'
                    }}>
                      {profile?.plan === 'pro' ? 'Pro Plan' : profile?.plan === 'enterprise' ? 'Enterprise Plan' : 'Free Plan'}
                    </div>
                    <div style={{
                      color: colors.text.secondary,
                      fontSize: '14px'
                    }}>
                      {profile?.plan === 'free' ? 'No subscription' : profile?.subscription_status === 'active' ? '$29/month • Active' : 'Subscription inactive'}
                    </div>
                  </div>
                </div>

                <div style={{
                  display: 'flex',
                  gap: '12px',
                  marginTop: '16px'
                }}>
                  <button style={{
                    padding: '8px 16px',
                    background: colors.surface,
                    color: colors.text.primary,
                    border: `1px solid ${colors.border}`,
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer'
                  }}>
                    Manage Billing
                  </button>
                  <button style={{
                    padding: '8px 16px',
                    background: 'transparent',
                    color: colors.text.secondary,
                    border: `1px solid ${colors.border}`,
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer'
                  }}>
                    Cancel Plan
                  </button>
                </div>
              </div>
            </div>

            {/* Save Button */}
            <button
              onClick={saveProfileSettings}
              disabled={saving || !user}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '12px 24px',
                background: saving || !user ? colors.text.tertiary : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: saving || !user ? 'not-allowed' : 'pointer',
                boxShadow: saving || !user ? 'none' : `0 4px 12px ${colors.primary}30`,
                opacity: saving || !user ? 0.6 : 1
              }}
            >
              <Save size={16} />
              {saving ? 'Saving...' : 'Save Account Settings'}
            </button>
          </div>
        )}

        {/* Other tabs can be added here */}
        {activeTab !== 'agent-e' && activeTab !== 'integrations' && activeTab !== 'account' && (
          <div style={{
            textAlign: 'center',
            padding: '60px 20px',
            color: colors.text.secondary
          }}>
            <h3 style={{ marginBottom: '12px' }}>Coming Soon</h3>
            <p>This section is under development.</p>
          </div>
        )}
      </div>

      {/* PIN Verification Modal */}
      {showPinModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: colors.surface,
            borderRadius: '16px',
            padding: '32px',
            width: '90%',
            maxWidth: '400px',
            textAlign: 'center'
          }}>
            <h3 style={{
              color: colors.text.primary,
              fontSize: '20px',
              fontWeight: '600',
              marginBottom: '16px'
            }}>
              🔗 Connect Your X Account
            </h3>

            <div style={{
              background: '#F0F9FF',
              border: '1px solid #BAE6FD',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '20px'
            }}>
              <p style={{
                color: '#0369A1',
                fontSize: '14px',
                marginBottom: '12px',
                fontWeight: '600'
              }}>
                📋 Instructions:
              </p>
              <ol style={{
                color: '#0369A1',
                fontSize: '13px',
                lineHeight: '1.5',
                margin: 0,
                paddingLeft: '20px'
              }}>
                <li>A new tab will open with X authorization page</li>
                <li>Click "Authorize app" on the X page</li>
                <li>Copy the PIN code shown on X</li>
                <li><strong>Come back to this page</strong> and enter the PIN below</li>
                <li>Click "Connect Account" to finish</li>
              </ol>

              <div style={{
                background: '#FEF3C7',
                border: '1px solid #FDE68A',
                borderRadius: '6px',
                padding: '8px 12px',
                marginTop: '12px'
              }}>
                <p style={{
                  color: '#92400E',
                  fontSize: '12px',
                  margin: 0,
                  fontWeight: '600'
                }}>
                  💡 This modal will stay open even if you refresh the page!
                </p>
              </div>
            </div>

            <p style={{
              color: colors.text.secondary,
              fontSize: '14px',
              marginBottom: '20px',
              textAlign: 'center',
              fontWeight: '600'
            }}>
              Enter the PIN code from X:
            </p>

            <input
              type="text"
              value={pin}
              onChange={(e) => setPin(e.target.value)}
              placeholder="Enter PIN code"
              style={{
                width: '100%',
                padding: '12px 16px',
                border: `1px solid ${colors.border}`,
                borderRadius: '8px',
                fontSize: '16px',
                textAlign: 'center',
                marginBottom: '24px',
                outline: 'none'
              }}
              maxLength={7}
            />

            <div style={{ display: 'flex', gap: '8px', justifyContent: 'center', flexWrap: 'wrap' }}>
              <button
                onClick={() => {
                  // Clear localStorage tokens
                  localStorage.removeItem('pending_x_oauth_token');
                  localStorage.removeItem('pending_x_oauth_secret');

                  // Clear state
                  setShowPinModal(false);
                  setPin('');
                  setOauthToken('');
                  setOauthTokenSecret('');
                }}
                style={{
                  padding: '12px 20px',
                  background: colors.background,
                  color: colors.text.primary,
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                Cancel
              </button>

              <button
                onClick={() => {
                  // Re-open X authorization page
                  const authUrl = `https://api.twitter.com/oauth/authorize?oauth_token=${oauthToken}`;
                  window.open(authUrl, '_blank');
                }}
                disabled={!oauthToken}
                style={{
                  padding: '12px 20px',
                  background: '#1DA1F2',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                Open X Page
              </button>

              <button
                onClick={handleVerifyPin}
                disabled={!pin.trim() || verifyingPin}
                style={{
                  padding: '12px 24px',
                  background: (!pin.trim() || verifyingPin)
                    ? colors.text.tertiary
                    : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: (!pin.trim() || verifyingPin) ? 'not-allowed' : 'pointer'
                }}
              >
                {verifyingPin ? 'Verifying...' : 'Connect Account'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

SettingsPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default SettingsPage;
