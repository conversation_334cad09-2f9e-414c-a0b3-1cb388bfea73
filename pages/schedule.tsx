// pages/schedule.tsx
import React, { useState } from 'react';
import SidebarLayout from '../components/SidebarLayoutSimple';
import { Calendar, Clock, Plus, Edit3, Trash2, Send, Eye } from 'lucide-react';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

interface ScheduledPost {
  id: string;
  content: string;
  scheduledTime: Date;
  status: 'scheduled' | 'posted' | 'failed';
  platform: 'twitter' | 'linkedin' | 'instagram';
}

const SchedulePage: NextPageWithLayout = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showNewPostModal, setShowNewPostModal] = useState(false);
  const [scheduledPosts, setScheduledPosts] = useState<ScheduledPost[]>([
    {
      id: '1',
      content: 'Excited to share our latest AI breakthrough! 🚀 #AI #Innovation',
      scheduledTime: new Date(2024, 11, 25, 14, 30),
      status: 'scheduled',
      platform: 'twitter'
    },
    {
      id: '2',
      content: 'Building the future of AI-powered content creation. What are your thoughts?',
      scheduledTime: new Date(2024, 11, 26, 10, 0),
      status: 'scheduled',
      platform: 'twitter'
    }
  ]);

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    surface: '#FFFFFF',
    background: '#FFF8F3',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    border: '#F5E6D3',
    hover: '#FFF0E6'
  };

  // Calendar generation
  const generateCalendar = () => {
    const year = selectedDate.getFullYear();
    const month = selectedDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const current = new Date(startDate);

    for (let i = 0; i < 42; i++) {
      days.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }

    return days;
  };

  const getPostsForDate = (date: Date) => {
    return scheduledPosts.filter(post => 
      post.scheduledTime.toDateString() === date.toDateString()
    );
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div style={{
      padding: '40px 60px',
      height: '100vh',
      overflow: 'auto',
      background: colors.background
    }}>
      {/* Header */}
      <div style={{
        marginBottom: '40px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <h1 style={{
            color: colors.text.primary,
            margin: 0,
            fontSize: '32px',
            fontWeight: '300',
            letterSpacing: '-1px',
            marginBottom: '8px',
            fontFamily: 'Georgia, serif'
          }}>
            Content Scheduler
          </h1>
          <p style={{
            color: colors.text.secondary,
            margin: 0,
            fontSize: '16px'
          }}>
            Plan and schedule your content across platforms
          </p>
        </div>
        
        <button
          onClick={() => setShowNewPostModal(true)}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '12px 24px',
            background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
            color: 'white',
            border: 'none',
            borderRadius: '12px',
            fontSize: '14px',
            fontWeight: '600',
            cursor: 'pointer',
            boxShadow: `0 4px 12px ${colors.primary}30`
          }}
        >
          <Plus size={16} />
          Schedule Post
        </button>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 350px',
        gap: '32px',
        height: 'calc(100vh - 200px)'
      }}>
        {/* Calendar */}
        <div style={{
          background: colors.surface,
          borderRadius: '16px',
          padding: '24px',
          border: `1px solid ${colors.border}`,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
        }}>
          {/* Calendar Header */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '24px'
          }}>
            <h2 style={{
              color: colors.text.primary,
              fontSize: '20px',
              fontWeight: '600',
              margin: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <Calendar size={20} color={colors.primary} />
              {monthNames[selectedDate.getMonth()]} {selectedDate.getFullYear()}
            </h2>
            
            <div style={{ display: 'flex', gap: '8px' }}>
              <button
                onClick={() => setSelectedDate(new Date(selectedDate.getFullYear(), selectedDate.getMonth() - 1))}
                style={{
                  padding: '8px 12px',
                  background: colors.background,
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  cursor: 'pointer',
                  color: colors.text.primary
                }}
              >
                ←
              </button>
              <button
                onClick={() => setSelectedDate(new Date())}
                style={{
                  padding: '8px 12px',
                  background: colors.background,
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  cursor: 'pointer',
                  color: colors.text.primary,
                  fontSize: '12px'
                }}
              >
                Today
              </button>
              <button
                onClick={() => setSelectedDate(new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1))}
                style={{
                  padding: '8px 12px',
                  background: colors.background,
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  cursor: 'pointer',
                  color: colors.text.primary
                }}
              >
                →
              </button>
            </div>
          </div>

          {/* Day Headers */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(7, 1fr)',
            gap: '1px',
            marginBottom: '8px'
          }}>
            {dayNames.map(day => (
              <div key={day} style={{
                padding: '12px 8px',
                textAlign: 'center',
                fontSize: '12px',
                fontWeight: '600',
                color: colors.text.secondary,
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}>
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Grid */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(7, 1fr)',
            gap: '1px',
            background: colors.border,
            borderRadius: '8px',
            overflow: 'hidden'
          }}>
            {generateCalendar().map((date, index) => {
              const isCurrentMonth = date.getMonth() === selectedDate.getMonth();
              const isToday = date.toDateString() === new Date().toDateString();
              const postsForDate = getPostsForDate(date);
              
              return (
                <div
                  key={index}
                  style={{
                    background: colors.surface,
                    minHeight: '80px',
                    padding: '8px',
                    cursor: 'pointer',
                    transition: 'background-color 0.2s ease',
                    opacity: isCurrentMonth ? 1 : 0.3,
                    position: 'relative'
                  }}
                  onMouseEnter={(e) => {
                    if (isCurrentMonth) {
                      e.currentTarget.style.background = colors.hover;
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = colors.surface;
                  }}
                >
                  <div style={{
                    fontSize: '14px',
                    fontWeight: isToday ? '600' : '400',
                    color: isToday ? colors.primary : colors.text.primary,
                    marginBottom: '4px'
                  }}>
                    {date.getDate()}
                  </div>
                  
                  {/* Post indicators */}
                  {postsForDate.slice(0, 2).map((post, i) => (
                    <div
                      key={post.id}
                      style={{
                        fontSize: '10px',
                        padding: '2px 4px',
                        background: colors.primary + '20',
                        color: colors.primary,
                        borderRadius: '4px',
                        marginBottom: '2px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      {formatTime(post.scheduledTime)}
                    </div>
                  ))}
                  
                  {postsForDate.length > 2 && (
                    <div style={{
                      fontSize: '10px',
                      color: colors.text.tertiary,
                      textAlign: 'center'
                    }}>
                      +{postsForDate.length - 2} more
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Scheduled Posts Sidebar */}
        <div style={{
          background: colors.surface,
          borderRadius: '16px',
          padding: '24px',
          border: `1px solid ${colors.border}`,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          overflow: 'auto'
        }}>
          <h3 style={{
            color: colors.text.primary,
            fontSize: '18px',
            fontWeight: '600',
            marginBottom: '16px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <Clock size={18} color={colors.primary} />
            Upcoming Posts
          </h3>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {scheduledPosts
              .sort((a, b) => a.scheduledTime.getTime() - b.scheduledTime.getTime())
              .map(post => (
                <div
                  key={post.id}
                  style={{
                    padding: '16px',
                    background: colors.background,
                    borderRadius: '12px',
                    border: `1px solid ${colors.border}`
                  }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '8px'
                  }}>
                    <div style={{
                      fontSize: '12px',
                      color: colors.text.secondary,
                      fontWeight: '500'
                    }}>
                      {post.scheduledTime.toLocaleDateString()} at {formatTime(post.scheduledTime)}
                    </div>
                    <div style={{
                      padding: '2px 6px',
                      background: post.status === 'scheduled' ? colors.primary + '20' : '#00C851' + '20',
                      color: post.status === 'scheduled' ? colors.primary : '#00C851',
                      borderRadius: '4px',
                      fontSize: '10px',
                      fontWeight: '500',
                      textTransform: 'uppercase'
                    }}>
                      {post.status}
                    </div>
                  </div>
                  
                  <p style={{
                    color: colors.text.primary,
                    fontSize: '14px',
                    lineHeight: '1.4',
                    margin: '0 0 12px 0'
                  }}>
                    {post.content}
                  </p>
                  
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <div style={{
                      fontSize: '12px',
                      color: colors.text.tertiary,
                      textTransform: 'capitalize'
                    }}>
                      {post.platform}
                    </div>
                    
                    <div style={{ display: 'flex', gap: '4px' }}>
                      <button style={{
                        padding: '4px',
                        background: 'transparent',
                        border: 'none',
                        cursor: 'pointer',
                        borderRadius: '4px'
                      }}>
                        <Eye size={14} color={colors.text.tertiary} />
                      </button>
                      <button style={{
                        padding: '4px',
                        background: 'transparent',
                        border: 'none',
                        cursor: 'pointer',
                        borderRadius: '4px'
                      }}>
                        <Edit3 size={14} color={colors.text.tertiary} />
                      </button>
                      <button style={{
                        padding: '4px',
                        background: 'transparent',
                        border: 'none',
                        cursor: 'pointer',
                        borderRadius: '4px'
                      }}>
                        <Trash2 size={14} color={colors.text.tertiary} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};

SchedulePage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default SchedulePage;
