import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('Creating database tables...');

    // Create scheduled_posts table
    const { error: scheduledPostsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS scheduled_posts (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID NOT NULL,
          content TEXT NOT NULL,
          platform VARCHAR(50) NOT NULL DEFAULT 'twitter',
          scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
          status VARCHAR(20) NOT NULL DEFAULT 'scheduled',
          platform_post_id VARCHAR(255),
          error_message TEXT,
          posted_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (scheduledPostsError) {
      console.error('Error creating scheduled_posts:', scheduledPostsError);
    }

    // Create posted_content table
    const { error: postedContentError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS posted_content (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID NOT NULL,
          content TEXT NOT NULL,
          platform VARCHAR(50) NOT NULL DEFAULT 'twitter',
          platform_post_id VARCHAR(255) NOT NULL,
          scheduled_post_id UUID,
          status VARCHAR(20) NOT NULL DEFAULT 'posted',
          posted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (postedContentError) {
      console.error('Error creating posted_content:', postedContentError);
    }

    // Create x_accounts table
    const { error: xAccountsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS x_accounts (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID NOT NULL,
          x_user_id VARCHAR(255) NOT NULL,
          username VARCHAR(255) NOT NULL,
          name VARCHAR(255),
          profile_image_url TEXT,
          access_token TEXT NOT NULL,
          access_secret TEXT NOT NULL,
          is_active BOOLEAN DEFAULT true,
          connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          disconnected_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (xAccountsError) {
      console.error('Error creating x_accounts:', xAccountsError);
    }

    // Create x_oauth_tokens table
    const { error: oauthTokensError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS x_oauth_tokens (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID NOT NULL,
          oauth_token VARCHAR(255) NOT NULL,
          oauth_token_secret VARCHAR(255) NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (oauthTokensError) {
      console.error('Error creating x_oauth_tokens:', oauthTokensError);
    }

    return res.status(200).json({ 
      success: true,
      message: 'Database tables created successfully',
      errors: {
        scheduledPosts: scheduledPostsError?.message,
        postedContent: postedContentError?.message,
        xAccounts: xAccountsError?.message,
        oauthTokens: oauthTokensError?.message
      }
    });

  } catch (error) {
    console.error('Migration error:', error);
    return res.status(500).json({ error: 'Migration failed' });
  }
}
