import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const sql = `
    -- Create X account connection tables
    CREATE TABLE IF NOT EXISTS x_accounts (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      x_user_id VARCHAR(255) NOT NULL,
      username VARCHAR(255) NOT NULL,
      name VARCHA<PERSON>(255),
      profile_image_url TEXT,
      access_token TEXT NOT NULL,
      access_secret TEXT NOT NULL,
      is_active BOOLEAN DEFAULT true,
      connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      disconnected_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE(user_id, x_user_id)
    );

    -- Create temporary OAuth tokens table
    CREATE TABLE IF NOT EXISTS x_oauth_tokens (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      oauth_token VARCHAR(255) NOT NULL,
      oauth_token_secret VARCHAR(255) NOT NULL,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE(oauth_token)
    );

    -- Enable Row Level Security
    ALTER TABLE x_accounts ENABLE ROW LEVEL SECURITY;
    ALTER TABLE x_oauth_tokens ENABLE ROW LEVEL SECURITY;

    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Users can manage their own X accounts" ON x_accounts;
    DROP POLICY IF EXISTS "Users can manage their own OAuth tokens" ON x_oauth_tokens;

    -- Create policies
    CREATE POLICY "Users can manage their own X accounts" ON x_accounts
      FOR ALL USING (auth.uid() = user_id);

    CREATE POLICY "Users can manage their own OAuth tokens" ON x_oauth_tokens
      FOR ALL USING (auth.uid() = user_id);
  `;

  try {
    // Test if tables exist by trying to select from them
    const { error: testError } = await supabase
      .from('x_accounts')
      .select('id')
      .limit(1);

    if (!testError) {
      return res.status(200).json({
        success: true,
        message: 'X account tables already exist and are accessible'
      });
    }

    // If tables don't exist, return SQL for manual creation
    return res.status(200).json({
      success: false,
      error: 'Tables do not exist. Please create them manually.',
      sql: sql
    });

  } catch (error) {
    console.error('Setup error:', error);
    return res.status(500).json({
      success: false,
      error: 'Setup failed',
      details: error.message,
      sql: sql
    });
  }
}
