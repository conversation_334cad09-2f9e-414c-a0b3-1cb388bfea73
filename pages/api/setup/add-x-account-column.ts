import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if the column already exists
    const { data: columns, error: columnError } = await supabase
      .rpc('check_column_exists', {
        table_name: 'user_profiles',
        column_name: 'x_account_info'
      });

    if (columnError) {
      console.log('Column check failed, proceeding with migration anyway');
    }

    // Add the column if it doesn't exist
    const migrationSQL = `
      DO $$ 
      BEGIN 
          IF NOT EXISTS (
              SELECT 1 
              FROM information_schema.columns 
              WHERE table_name = 'user_profiles' 
              AND column_name = 'x_account_info'
          ) THEN
              ALTER TABLE user_profiles ADD COLUMN x_account_info JSONB;
              RAISE NOTICE 'Added x_account_info column to user_profiles table';
          ELSE
              RAISE NOTICE 'x_account_info column already exists in user_profiles table';
          END IF;
      END $$;
    `;

    const { error: migrationError } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });

    if (migrationError) {
      console.error('Migration error:', migrationError);
      
      // Try a simpler approach
      const { error: simpleError } = await supabase
        .from('user_profiles')
        .select('x_account_info')
        .limit(1);

      if (simpleError && simpleError.message.includes('column "x_account_info" does not exist')) {
        return res.status(200).json({
          success: false,
          message: 'Column does not exist. Please run this SQL in your Supabase SQL editor:',
          sql: 'ALTER TABLE user_profiles ADD COLUMN x_account_info JSONB;'
        });
      }
    }

    // Test if we can now access the column
    const { data: testData, error: testError } = await supabase
      .from('user_profiles')
      .select('x_account_info')
      .limit(1);

    if (testError) {
      return res.status(500).json({
        success: false,
        error: 'Column migration may have failed',
        details: testError.message,
        sql: 'ALTER TABLE user_profiles ADD COLUMN x_account_info JSONB;'
      });
    }

    return res.status(200).json({
      success: true,
      message: 'x_account_info column is available in user_profiles table',
      columnExists: true
    });

  } catch (error) {
    console.error('Setup error:', error);
    return res.status(500).json({
      success: false,
      error: 'Setup failed',
      details: error.message,
      sql: 'ALTER TABLE user_profiles ADD COLUMN x_account_info JSONB;'
    });
  }
}
