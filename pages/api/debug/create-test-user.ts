import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const testEmail = '<EMAIL>';
    const testPassword = 'testpassword123';
    const testName = 'Test User';

    console.log('Creating test user...');

    // Try to sign up the test user
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: testName,
        }
      }
    });

    console.log('Sign up result:', { data: !!data, error });

    if (error) {
      // If user already exists, try to sign in
      if (error.message.includes('already registered')) {
        console.log('User exists, trying to sign in...');

        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: testEmail,
          password: testPassword,
        });

        console.log('Sign in result:', { data: !!signInData, error: signInError });

        if (signInError) {
          return res.status(400).json({
            error: 'Failed to sign in existing user',
            details: signInError.message
          });
        }

        return res.status(200).json({
          success: true,
          message: 'Signed in existing test user',
          user: signInData.user,
          credentials: { email: testEmail, password: testPassword }
        });
      } else {
        return res.status(400).json({
          error: 'Failed to create test user',
          details: error.message
        });
      }
    }

    return res.status(200).json({
      success: true,
      message: 'Test user created successfully',
      user: data.user,
      credentials: { email: testEmail, password: testPassword }
    });

  } catch (error) {
    console.error('Test user creation error:', error);
    return res.status(500).json({
      error: 'Failed to create test user',
      details: error.message
    });
  }
}
