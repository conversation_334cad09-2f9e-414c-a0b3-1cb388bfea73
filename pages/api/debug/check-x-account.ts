import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { userId } = req.query;

  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }

  try {
    // Get the user's profile data
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching profile:', error);
      return res.status(500).json({ 
        error: 'Failed to fetch profile',
        details: error.message,
        code: error.code
      });
    }

    return res.status(200).json({
      success: true,
      profile: profile,
      x_account_info: profile?.x_account_info || null,
      has_x_account: !!profile?.x_account_info,
      is_active: profile?.x_account_info?.is_active || false
    });

  } catch (error) {
    console.error('Error checking X account:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
