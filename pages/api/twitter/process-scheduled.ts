import { NextApiRequest, NextApiResponse } from 'next';
import { Twitter<PERSON><PERSON> } from 'twitter-api-v2';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // This endpoint should be called by a cron job or scheduled task
  // For security, you might want to add authentication here
  const authHeader = req.headers.authorization;
  const expectedToken = process.env.CRON_SECRET;

  if (expectedToken && authHeader !== `Bearer ${expectedToken}`) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get Twitter API credentials
    const twitterApiKey = process.env.TWITTER_API_KEY;
    const twitterApiSecret = process.env.TWITTER_API_SECRET;

    if (!twitterApiKey || !twitterApiSecret) {
      return res.status(500).json({
        error: 'Twitter API credentials not configured'
      });
    }

    // Get all posts scheduled for now or earlier
    const now = new Date();
    const { data: scheduledPosts, error: fetchError } = await supabase
      .from('scheduled_posts')
      .select('*')
      .eq('status', 'scheduled')
      .lte('scheduled_time', now.toISOString())
      .order('scheduled_time', { ascending: true });

    if (fetchError) {
      console.error('Error fetching scheduled posts:', fetchError);
      return res.status(500).json({ error: 'Failed to fetch scheduled posts' });
    }

    const results = [];

    for (const post of scheduledPosts) {
      try {
        // Get user's connected X account
        const { data: xAccount, error: xAccountError } = await supabase
          .from('x_accounts')
          .select('access_token, access_secret, username')
          .eq('user_id', post.user_id)
          .eq('is_active', true)
          .single();

        if (xAccountError || !xAccount) {
          console.error(`No X account found for user ${post.user_id}`);

          // Mark as failed
          await supabase
            .from('scheduled_posts')
            .update({
              status: 'failed',
              error_message: 'No connected X account found'
            })
            .eq('id', post.id);

          results.push({
            postId: post.id,
            status: 'failed',
            error: 'No connected X account found'
          });
          continue;
        }

        // Initialize Twitter client with user's credentials
        const twitterClient = new TwitterApi({
          appKey: twitterApiKey,
          appSecret: twitterApiSecret,
          accessToken: xAccount.access_token,
          accessSecret: xAccount.access_secret,
        });

        // Post to Twitter
        const tweet = await twitterClient.v2.tweet(post.content);

        // Update the scheduled post status
        await supabase
          .from('scheduled_posts')
          .update({
            status: 'posted',
            posted_at: new Date().toISOString(),
            platform_post_id: tweet.data.id
          })
          .eq('id', post.id);

        // Save to posted content table
        await supabase
          .from('posted_content')
          .insert({
            user_id: post.user_id,
            content: post.content,
            platform: 'twitter',
            platform_post_id: tweet.data.id,
            posted_at: new Date().toISOString(),
            status: 'posted',
            scheduled_post_id: post.id
          });

        results.push({
          postId: post.id,
          tweetId: tweet.data.id,
          status: 'success'
        });

        console.log(`Successfully posted scheduled tweet: ${tweet.data.id}`);

      } catch (error) {
        console.error(`Error posting scheduled tweet ${post.id}:`, error);

        // Update the scheduled post status to failed
        await supabase
          .from('scheduled_posts')
          .update({
            status: 'failed',
            error_message: error.message || 'Unknown error'
          })
          .eq('id', post.id);

        results.push({
          postId: post.id,
          status: 'failed',
          error: error.message
        });
      }
    }

    return res.status(200).json({
      success: true,
      processed: results.length,
      results
    });

  } catch (error) {
    console.error('Error processing scheduled posts:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
