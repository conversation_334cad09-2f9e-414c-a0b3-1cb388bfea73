import { NextApiRequest, NextApiResponse } from 'next';
import { Twitter<PERSON><PERSON> } from 'twitter-api-v2';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { content, userId, scheduledTime } = req.body;

  if (!content || !userId) {
    return res.status(400).json({ error: 'Content and userId are required' });
  }

  try {
    // Get our Twitter API credentials from environment variables
    const twitterApiKey = process.env.TWITTER_API_KEY;
    const twitterApiSecret = process.env.TWITTER_API_SECRET;
    const twitterAccessToken = process.env.TWITTER_ACCESS_TOKEN;
    const twitterAccessSecret = process.env.TWITTER_ACCESS_SECRET;

    if (!twitterApiKey || !twitterApiSecret || !twitterAccessToken || !twitterAccessSecret) {
      return res.status(500).json({ 
        error: 'Twitter API credentials not configured. Please contact support.' 
      });
    }

    // Initialize Twitter client with our credentials
    const twitterClient = new TwitterApi({
      appKey: twitterApiKey,
      appSecret: twitterApiSecret,
      accessToken: twitterAccessToken,
      accessSecret: twitterAccessSecret,
    });

    // If this is a scheduled post, save it to database instead of posting immediately
    if (scheduledTime) {
      const { data, error } = await supabase
        .from('scheduled_posts')
        .insert({
          user_id: userId,
          content,
          scheduled_time: scheduledTime,
          platform: 'twitter',
          status: 'scheduled'
        })
        .select()
        .single();

      if (error) {
        console.error('Error saving scheduled post:', error);
        return res.status(500).json({ error: 'Failed to schedule post' });
      }

      return res.status(200).json({ 
        success: true, 
        message: 'Post scheduled successfully',
        scheduledPost: data
      });
    }

    // Post immediately to Twitter
    const tweet = await twitterClient.v2.tweet(content);

    // Save the posted tweet to our database
    const { data, error } = await supabase
      .from('posted_content')
      .insert({
        user_id: userId,
        content,
        platform: 'twitter',
        platform_post_id: tweet.data.id,
        posted_at: new Date().toISOString(),
        status: 'posted'
      })
      .select()
      .single();

    if (error) {
      console.error('Error saving posted content:', error);
      // Don't fail the request since the tweet was posted successfully
    }

    return res.status(200).json({ 
      success: true, 
      message: 'Tweet posted successfully',
      tweetId: tweet.data.id,
      postedContent: data
    });

  } catch (error) {
    console.error('Error posting to Twitter:', error);
    
    // Handle specific Twitter API errors
    if (error.code === 403) {
      return res.status(403).json({ 
        error: 'Twitter API access denied. Please check permissions.' 
      });
    }
    
    if (error.code === 429) {
      return res.status(429).json({ 
        error: 'Twitter API rate limit exceeded. Please try again later.' 
      });
    }

    return res.status(500).json({ 
      error: 'Failed to post tweet. Please try again.' 
    });
  }
}
