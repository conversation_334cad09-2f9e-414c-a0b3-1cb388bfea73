import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { userId } = req.query;

  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }

  try {
    if (req.method === 'GET') {
      // Get all scheduled posts for the user
      const { data, error } = await supabase
        .from('scheduled_posts')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'scheduled')
        .order('scheduled_time', { ascending: true });

      if (error) {
        console.error('Error fetching scheduled posts:', error);
        return res.status(500).json({ error: 'Failed to fetch scheduled posts' });
      }

      return res.status(200).json({ scheduledPosts: data });
    }

    if (req.method === 'DELETE') {
      // Delete a scheduled post
      const { postId } = req.body;

      if (!postId) {
        return res.status(400).json({ error: 'postId is required' });
      }

      const { error } = await supabase
        .from('scheduled_posts')
        .delete()
        .eq('id', postId)
        .eq('user_id', userId);

      if (error) {
        console.error('Error deleting scheduled post:', error);
        return res.status(500).json({ error: 'Failed to delete scheduled post' });
      }

      return res.status(200).json({ success: true, message: 'Scheduled post deleted' });
    }

    if (req.method === 'PUT') {
      // Update a scheduled post
      const { postId, content, scheduledTime } = req.body;

      if (!postId) {
        return res.status(400).json({ error: 'postId is required' });
      }

      const { data, error } = await supabase
        .from('scheduled_posts')
        .update({
          content: content,
          scheduled_time: scheduledTime
        })
        .eq('id', postId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating scheduled post:', error);
        return res.status(500).json({ error: 'Failed to update scheduled post' });
      }

      return res.status(200).json({ 
        success: true, 
        message: 'Scheduled post updated',
        updatedPost: data
      });
    }

    return res.status(405).json({ error: 'Method not allowed' });

  } catch (error) {
    console.error('Error in scheduled posts API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
