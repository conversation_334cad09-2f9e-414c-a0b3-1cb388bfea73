import { NextApiRequest, NextApiResponse } from 'next';
import { Twitter<PERSON><PERSON> } from 'twitter-api-v2';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get Twitter API credentials
    const twitterApiKey = process.env.TWITTER_API_KEY;
    const twitterApiSecret = process.env.TWITTER_API_SECRET;

    console.log('Twitter API Key:', twitterApiKey ? 'Present' : 'Missing');
    console.log('Twitter API Secret:', twitterApiSecret ? 'Present' : 'Missing');

    if (!twitterApiKey || !twitterApiSecret) {
      return res.status(500).json({ 
        error: 'Twitter API credentials not configured',
        details: {
          apiKey: twitterApiKey ? 'Present' : 'Missing',
          apiSecret: twitterApiSecret ? 'Present' : 'Missing'
        }
      });
    }

    // Test basic client initialization
    const twitterClient = new TwitterApi({
      appKey: twitterApiKey,
      appSecret: twitterApiSecret,
    });

    console.log('Twitter client initialized successfully');

    // Try to generate auth link with oob
    try {
      const { url: authUrl, oauth_token, oauth_token_secret } = await twitterClient.generateAuthLink(
        'oob',
        { linkMode: 'authorize' }
      );

      return res.status(200).json({ 
        success: true,
        message: 'Twitter OAuth flow working',
        authUrl: authUrl.substring(0, 50) + '...',
        oauth_token: oauth_token.substring(0, 10) + '...',
        hasSecret: !!oauth_token_secret
      });

    } catch (authError) {
      console.error('Auth generation error:', authError);
      return res.status(500).json({ 
        error: 'Failed to generate auth URL',
        details: authError.message || 'Unknown error',
        code: authError.code || 'Unknown'
      });
    }

  } catch (error) {
    console.error('Twitter test error:', error);
    return res.status(500).json({ 
      error: 'Twitter API test failed',
      details: error.message || 'Unknown error'
    });
  }
}
