import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get user settings from the user_settings table
    const { data: settings, error } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching user settings:', error);
      return res.status(500).json({ error: 'Failed to fetch settings' });
    }

    // Return settings or default values
    const defaultSettings = {
      project_name: 'My Project',
      brand_voice: 'professional',
      target_audience: 'professionals',
      custom_prompts: []
    };

    res.status(200).json({
      settings: settings || defaultSettings
    });

  } catch (error) {
    console.error('Error in settings API:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
