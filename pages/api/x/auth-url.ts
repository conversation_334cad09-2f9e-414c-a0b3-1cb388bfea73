import { NextApiRequest, NextApiResponse } from 'next';
import { Twitter<PERSON><PERSON> } from 'twitter-api-v2';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { userId } = req.body;

  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }

  try {
    // Get X API credentials (using your existing credentials)
    const apiKey = process.env.X_API_KEY;
    const apiSecret = process.env.X_API_SECRET_KEY;

    if (!apiKey || !apiSecret) {
      return res.status(500).json({
        error: 'X API credentials not configured'
      });
    }

    // Initialize Twitter client for OAuth 1.0a
    const twitterClient = new TwitterApi({
      appKey: apiKey,
      appSecret: apiSecret,
    });

    // Generate OAuth URL with callback URL (OAuth 1.0a flow)
    const callbackUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3002'}/api/x/callback`;
    const { url: authUrl, oauth_token, oauth_token_secret } = await twitterClient.generateAuthLink(
      callbackUrl,
      { linkMode: 'authorize' }
    );

    // Store OAuth tokens in database temporarily
    const { error: storeError } = await supabase
      .from('x_oauth_tokens')
      .insert({
        user_id: userId,
        oauth_token,
        oauth_token_secret
      });

    if (storeError) {
      console.error('Error storing OAuth tokens:', storeError);
      return res.status(500).json({ error: 'Failed to store OAuth tokens' });
    }

    return res.status(200).json({
      authUrl,
      message: 'Redirecting to X for authorization...'
    });

  } catch (error) {
    console.error('Error generating auth URL:', error);
    return res.status(500).json({ error: 'Failed to generate authorization URL' });
  }
}
