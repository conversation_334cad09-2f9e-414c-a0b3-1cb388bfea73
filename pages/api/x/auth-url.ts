import { NextApiRequest, NextApiResponse } from 'next';
import { Twitter<PERSON><PERSON> } from 'twitter-api-v2';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { userId } = req.body;

  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }

  try {
    // Get Twitter API credentials
    const twitterApiKey = process.env.TWITTER_API_KEY;
    const twitterApiSecret = process.env.TWITTER_API_SECRET;

    if (!twitterApiKey || !twitterApiSecret) {
      return res.status(500).json({
        error: 'Twitter API credentials not configured'
      });
    }

    // Initialize Twitter client for OAuth
    const twitterClient = new TwitterApi({
      appKey: twitterApiKey,
      appSecret: twitterApiSecret,
    });

    // Generate OAuth URL - use 'oob' for desktop apps
    const { url: authUrl, oauth_token, oauth_token_secret } = await twitterClient.generateAuthLink(
      'oob',
      { linkMode: 'authorize' }
    );

    // For now, we'll return the oauth_token_secret to the client
    // In production, this should be stored securely server-side

    return res.status(200).json({
      authUrl,
      oauth_token,
      oauth_token_secret,
      message: 'After authorizing, you will receive a PIN code. Please enter it to complete the connection.'
    });

  } catch (error) {
    console.error('Error generating auth URL:', error);
    return res.status(500).json({ error: 'Failed to generate authorization URL' });
  }
}
