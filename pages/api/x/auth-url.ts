import { NextApiRequest, NextApiResponse } from 'next';
import { Twitter<PERSON><PERSON> } from 'twitter-api-v2';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { userId } = req.body;

  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }

  try {
    // Get Twitter API credentials
    const twitterApiKey = process.env.TWITTER_API_KEY;
    const twitterApiSecret = process.env.TWITTER_API_SECRET;

    if (!twitterApiKey || !twitterApiSecret) {
      return res.status(500).json({
        error: 'Twitter API credentials not configured'
      });
    }

    // Initialize Twitter client for OAuth
    const twitterClient = new TwitterApi({
      appKey: twitterApiKey,
      appSecret: twitterApiSecret,
    });

    // Generate OAuth URL with callback URL
    const callbackUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/api/x/callback`;
    const { url: authUrl, oauth_token, oauth_token_secret } = await twitterClient.generateAuthLink(
      callbackUrl,
      { linkMode: 'authorize' }
    );

    // Store OAuth tokens in database temporarily
    const { error: storeError } = await supabase
      .from('x_oauth_tokens')
      .insert({
        user_id: userId,
        oauth_token,
        oauth_token_secret
      });

    if (storeError) {
      console.error('Error storing OAuth tokens:', storeError);
      return res.status(500).json({ error: 'Failed to store OAuth tokens' });
    }

    return res.status(200).json({
      authUrl,
      message: 'Redirecting to X for authorization...'
    });

  } catch (error) {
    console.error('Error generating auth URL:', error);
    return res.status(500).json({ error: 'Failed to generate authorization URL' });
  }
}
