import { NextApiRequest, NextApiResponse } from 'next';
import { supabaseAdmin } from '../../../lib/supabase';
import crypto from 'crypto';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { userId } = req.body;

  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }

  try {
    // Get X OAuth 2.0 credentials
    const clientId = process.env.X_CLIENT_ID;
    const clientSecret = process.env.X_CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      return res.status(500).json({
        error: 'X OAuth 2.0 credentials not configured'
      });
    }

    // Generate state parameter for security
    const state = crypto.randomBytes(32).toString('hex');

    // Generate code verifier and challenge for PKCE
    const codeVerifier = crypto.randomBytes(32).toString('base64url');
    const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64url');

    // Store OAuth state and code verifier temporarily
    const { error: storeError } = await supabaseAdmin
      .from('x_oauth_tokens')
      .insert({
        user_id: userId,
        oauth_token: state, // Using oauth_token field to store state
        oauth_token_secret: codeVerifier // Using oauth_token_secret field to store code verifier
      });

    if (storeError) {
      console.error('Error storing OAuth state:', storeError);
      return res.status(500).json({ error: 'Failed to store OAuth state' });
    }

    // Build OAuth 2.0 authorization URL
    const callbackUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/api/x/callback`;
    const scopes = 'tweet.read tweet.write users.read offline.access';

    const authUrl = new URL('https://twitter.com/i/oauth2/authorize');
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('client_id', clientId);
    authUrl.searchParams.set('redirect_uri', callbackUrl);
    authUrl.searchParams.set('scope', scopes);
    authUrl.searchParams.set('state', state);
    authUrl.searchParams.set('code_challenge', codeChallenge);
    authUrl.searchParams.set('code_challenge_method', 'S256');

    return res.status(200).json({
      authUrl: authUrl.toString(),
      message: 'Redirecting to X for authorization...'
    });

  } catch (error) {
    console.error('Error generating auth URL:', error);
    return res.status(500).json({ error: 'Failed to generate authorization URL' });
  }
}
