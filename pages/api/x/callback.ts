import { NextApiRequest, NextApiResponse } from 'next';
import { supabaseAdmin } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { code, state, error: oauthError } = req.query;

  // Handle OAuth errors
  if (oauthError) {
    console.error('OAuth error:', oauthError);
    return res.redirect('/settings?tab=integrations&error=oauth_denied');
  }

  if (!code || !state) {
    return res.redirect('/settings?tab=integrations&error=oauth_failed');
  }

  try {
    // Get the stored OAuth state and code verifier
    const { data: oauthData, error: fetchError } = await supabaseAdmin
      .from('x_oauth_tokens')
      .select('*')
      .eq('oauth_token', state) // state is stored in oauth_token field
      .single();

    if (fetchError || !oauthData) {
      console.error('OAuth state not found:', fetchError);
      return res.redirect('/settings?tab=integrations&error=invalid_state');
    }

    // Get X OAuth 2.0 credentials
    const clientId = process.env.X_CLIENT_ID;
    const clientSecret = process.env.X_CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      return res.redirect('/settings?tab=integrations&error=config_error');
    }

    // Exchange authorization code for access token
    const callbackUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/api/x/callback`;
    const codeVerifier = oauthData.oauth_token_secret; // code verifier is stored in oauth_token_secret field

    const tokenResponse = await fetch('https://api.twitter.com/2/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code: code as string,
        redirect_uri: callbackUrl,
        code_verifier: codeVerifier
      })
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.text();
      console.error('Token exchange failed:', errorData);
      return res.redirect('/settings?tab=integrations&error=token_exchange_failed');
    }

    const tokenData = await tokenResponse.json();
    const { access_token, refresh_token } = tokenData;

    // Get user information using the access token
    const userResponse = await fetch('https://api.twitter.com/2/users/me?user.fields=name,username,profile_image_url', {
      headers: {
        'Authorization': `Bearer ${access_token}`
      }
    });

    if (!userResponse.ok) {
      console.error('Failed to fetch user info');
      return res.redirect('/settings?tab=integrations&error=user_fetch_failed');
    }

    const userData = await userResponse.json();
    const userInfo = userData.data;

    // Store the user's X account information
    const { error: storeError } = await supabaseAdmin
      .from('x_accounts')
      .upsert({
        user_id: oauthData.user_id,
        x_user_id: userInfo.id,
        username: userInfo.username,
        name: userInfo.name,
        profile_image_url: userInfo.profile_image_url,
        access_token: access_token,
        access_secret: refresh_token || '', // Store refresh token in access_secret field
        is_active: true
      });

    if (storeError) {
      console.error('Error storing X account:', storeError);
      return res.redirect('/settings?tab=integrations&error=store_failed');
    }

    // Clean up OAuth tokens
    await supabaseAdmin
      .from('x_oauth_tokens')
      .delete()
      .eq('oauth_token', state);

    // Redirect back to settings with success
    return res.redirect('/settings?tab=integrations&success=connected');

  } catch (error) {
    console.error('OAuth callback error:', error);
    return res.redirect('/settings?tab=integrations&error=oauth_failed');
  }
}
