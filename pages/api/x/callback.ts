import { NextApiRequest, NextApiResponse } from 'next';
import { Twitter<PERSON>pi } from 'twitter-api-v2';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { oauth_token, oauth_verifier } = req.query;

  if (!oauth_token || !oauth_verifier) {
    return res.redirect('/settings?tab=integrations&error=oauth_failed');
  }

  try {
    // Get the stored OAuth tokens
    const { data: oauthData, error: fetchError } = await supabase
      .from('x_oauth_tokens')
      .select('*')
      .eq('oauth_token', oauth_token)
      .single();

    if (fetchError || !oauthData) {
      console.error('OAuth token not found:', fetchError);
      return res.redirect('/settings?tab=integrations&error=invalid_token');
    }

    // Get X API credentials (using your existing credentials)
    const apiKey = process.env.X_API_KEY;
    const apiSecret = process.env.X_API_SECRET_KEY;

    if (!apiKey || !apiSecret) {
      return res.redirect('/settings?tab=integrations&error=config_error');
    }

    // Initialize Twitter client with OAuth tokens
    const twitterClient = new TwitterApi({
      appKey: apiKey,
      appSecret: apiSecret,
      accessToken: oauth_token as string,
      accessSecret: oauthData.oauth_token_secret,
    });

    // Complete the OAuth process
    const { accessToken, accessSecret } = await twitterClient.login(oauth_verifier as string);

    // Create authenticated client
    const authenticatedClient = new TwitterApi({
      appKey: apiKey,
      appSecret: apiSecret,
      accessToken,
      accessSecret,
    });

    // Get user information
    const userInfo = await authenticatedClient.v2.me({
      'user.fields': ['name', 'username', 'profile_image_url']
    });

    // Store the user's X account information
    const { error: storeError } = await supabase
      .from('x_accounts')
      .upsert({
        user_id: oauthData.user_id,
        x_user_id: userInfo.data.id,
        username: userInfo.data.username,
        name: userInfo.data.name,
        profile_image_url: userInfo.data.profile_image_url,
        access_token: accessToken,
        access_secret: accessSecret,
        is_active: true
      });

    if (storeError) {
      console.error('Error storing X account:', storeError);
      return res.redirect('/settings?tab=integrations&error=store_failed');
    }

    // Clean up OAuth tokens
    await supabase
      .from('x_oauth_tokens')
      .delete()
      .eq('oauth_token', oauth_token);

    // Redirect back to settings with success
    return res.redirect('/settings?tab=integrations&success=connected');

  } catch (error) {
    console.error('OAuth callback error:', error);
    return res.redirect('/settings?tab=integrations&error=oauth_failed');
  }
}
