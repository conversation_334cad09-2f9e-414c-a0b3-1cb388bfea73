import { NextApiRequest, NextApiResponse } from 'next';
import { supabaseAdmin } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { userId } = req.body;

  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }

  try {
    // Check if supabaseAdmin is available
    if (!supabaseAdmin) {
      return res.status(500).json({ error: 'Database configuration error' });
    }

    // Mark X account as inactive
    const { error } = await supabaseAdmin
      .from('x_accounts')
      .update({
        is_active: false,
        disconnected_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('is_active', true);

    if (error) {
      console.error('Error disconnecting X account:', error);
      return res.status(500).json({ error: 'Failed to disconnect account' });
    }

    return res.status(200).json({
      success: true,
      message: 'X account disconnected successfully'
    });

  } catch (error) {
    console.error('Error in disconnect endpoint:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
