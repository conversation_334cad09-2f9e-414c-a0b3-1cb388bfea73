import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { userId } = req.body;

  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }

  try {
    // Get current profile and update X account info
    const { data: profile, error: fetchError } = await supabase
      .from('user_profiles')
      .select('x_account_info')
      .eq('user_id', userId)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('Error fetching profile:', fetchError);
      return res.status(500).json({ error: 'Failed to disconnect account' });
    }

    // Update the X account info to mark as inactive
    const updatedXAccountInfo = profile?.x_account_info ? {
      ...profile.x_account_info,
      is_active: false,
      disconnected_at: new Date().toISOString()
    } : null;

    const { error } = await supabase
      .from('user_profiles')
      .upsert({
        user_id: userId,
        x_account_info: updatedXAccountInfo
      });

    if (error) {
      console.error('Error disconnecting X account:', error);
      return res.status(500).json({ error: 'Failed to disconnect account' });
    }

    return res.status(200).json({
      success: true,
      message: 'X account disconnected successfully'
    });

  } catch (error) {
    console.error('Error in disconnect endpoint:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
