import { NextApiRequest, NextApiResponse } from 'next';
import { Twitter<PERSON><PERSON> } from 'twitter-api-v2';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { oauth_token, oauth_token_secret, pin, userId } = req.body;

  if (!oauth_token || !oauth_token_secret || !pin || !userId) {
    return res.status(400).json({ error: 'oauth_token, oauth_token_secret, pin, and userId are required' });
  }

  try {

    // Get Twitter API credentials
    const twitterApiKey = process.env.TWITTER_API_KEY;
    const twitterApiSecret = process.env.TWITTER_API_SECRET;

    if (!twitterApiKey || !twitterApiSecret) {
      return res.status(500).json({ error: 'Twitter API credentials not configured' });
    }

    // Initialize Twitter client with OAuth tokens
    const twitterClient = new TwitterApi({
      appKey: twitterApiKey,
      appSecret: twitterApiSecret,
      accessToken: oauth_token,
      accessSecret: oauth_token_secret,
    });

    // Complete the OAuth process with PIN
    const { accessToken, accessSecret } = await twitterClient.login(pin);

    // Create authenticated client
    const authenticatedClient = new TwitterApi({
      appKey: twitterApiKey,
      appSecret: twitterApiSecret,
      accessToken,
      accessSecret,
    });

    // Get user information
    const userInfo = await authenticatedClient.v2.me({
      'user.fields': ['name', 'username', 'profile_image_url']
    });

    // For now, we'll store the account info in user_profiles table
    // In production, this should be in a dedicated x_accounts table
    try {
      const { error: storeError } = await supabase
        .from('user_profiles')
        .upsert({
          user_id: userId,
          x_account_info: {
            x_user_id: userInfo.data.id,
            username: userInfo.data.username,
            name: userInfo.data.name,
            profile_image_url: userInfo.data.profile_image_url,
            access_token: accessToken,
            access_secret: accessSecret,
            connected_at: new Date().toISOString(),
            is_active: true
          }
        });

      if (storeError) {
        console.error('Error storing X account:', storeError);
        // Continue anyway - we'll return the account info to the client
      }
    } catch (error) {
      console.error('Error storing X account:', error);
      // Continue anyway
    }

    return res.status(200).json({
      success: true,
      message: 'X account connected successfully',
      accountInfo: {
        username: userInfo.data.username,
        name: userInfo.data.name,
        profile_image_url: userInfo.data.profile_image_url
      }
    });

  } catch (error) {
    console.error('PIN verification error:', error);

    if (error.code === 401) {
      return res.status(400).json({ error: 'Invalid PIN code. Please try again.' });
    }

    return res.status(500).json({ error: 'Failed to verify PIN code' });
  }
}
