import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { userId } = req.query;

  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }

  try {
    // Get the user's connected X account
    const { data: xAccount, error } = await supabase
      .from('x_accounts')
      .select('username, name, profile_image_url, is_active, connected_at')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching X account:', error);
      return res.status(500).json({ error: 'Failed to fetch account status' });
    }

    if (!xAccount) {
      return res.status(200).json({ 
        connected: false,
        accountInfo: null
      });
    }

    return res.status(200).json({ 
      connected: true,
      accountInfo: {
        username: xAccount.username,
        name: xAccount.name,
        profile_image_url: xAccount.profile_image_url,
        connected_at: xAccount.connected_at
      }
    });

  } catch (error) {
    console.error('Error checking account status:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
