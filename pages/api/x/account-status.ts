import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { userId } = req.query;

  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }

  try {
    console.log('Checking X account status for userId:', userId);

    // Get the user's X account info from user_profiles
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('x_account_info')
      .eq('user_id', userId)
      .single();

    console.log('Account status query result:', {
      hasProfile: !!profile,
      hasXAccountInfo: !!profile?.x_account_info,
      isActive: profile?.x_account_info?.is_active,
      username: profile?.x_account_info?.username,
      error: error?.message
    });

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching X account:', error);
      return res.status(500).json({ error: 'Failed to fetch account status' });
    }

    if (!profile || !profile.x_account_info || !profile.x_account_info.is_active) {
      console.log('No active X account found');
      return res.status(200).json({
        connected: false,
        accountInfo: null
      });
    }

    const xAccount = profile.x_account_info;
    console.log('Active X account found:', xAccount.username);

    return res.status(200).json({
      connected: true,
      accountInfo: {
        username: xAccount.username,
        name: xAccount.name,
        profile_image_url: xAccount.profile_image_url,
        connected_at: xAccount.connected_at
      }
    });

  } catch (error) {
    console.error('Error checking account status:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
