import React, { useState } from 'react';

const SetupDatabase = () => {
  const [result, setResult] = useState('');
  const [loading, setLoading] = useState(false);

  const runMigration = async () => {
    setLoading(true);
    setResult('');

    try {
      const response = await fetch('/api/setup/add-x-account-column', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (data.success) {
        setResult('✅ Success! X account column is ready.');
      } else {
        setResult(`❌ Migration needed. Please run this SQL in your Supabase SQL editor:\n\n${data.sql}`);
      }
    } catch (error) {
      setResult(`❌ Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '40px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Database Setup</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <p>This will add the missing <code>x_account_info</code> column to the <code>user_profiles</code> table.</p>
        <p>This column is required for X account connections to persist properly.</p>
      </div>

      <button 
        onClick={runMigration}
        disabled={loading}
        style={{
          padding: '12px 24px',
          background: '#10B981',
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          cursor: 'pointer',
          fontSize: '16px'
        }}
      >
        {loading ? 'Running Migration...' : 'Add X Account Column'}
      </button>

      {result && (
        <div style={{
          padding: '20px',
          background: result.includes('✅') ? '#F0FDF4' : '#FEF2F2',
          border: `1px solid ${result.includes('✅') ? '#BBF7D0' : '#FECACA'}`,
          borderRadius: '8px',
          marginTop: '20px'
        }}>
          <pre style={{ 
            margin: 0,
            whiteSpace: 'pre-wrap',
            fontFamily: 'monospace'
          }}>
            {result}
          </pre>
        </div>
      )}

      <div style={{
        marginTop: '40px',
        padding: '20px',
        background: '#F8FAFC',
        borderRadius: '8px',
        border: '1px solid #E2E8F0'
      }}>
        <h3>Manual Setup (if needed)</h3>
        <p>If the automatic migration doesn't work, run this SQL in your Supabase SQL editor:</p>
        <pre style={{
          background: '#1F2937',
          color: '#F9FAFB',
          padding: '15px',
          borderRadius: '6px',
          overflow: 'auto'
        }}>
{`ALTER TABLE user_profiles ADD COLUMN x_account_info JSONB;

-- Optional: Add index for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_x_account_info 
ON user_profiles USING GIN (x_account_info);`}
        </pre>
      </div>
    </div>
  );
};

export default SetupDatabase;
