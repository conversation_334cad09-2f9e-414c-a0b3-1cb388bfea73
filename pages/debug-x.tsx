import React, { useState, useEffect } from 'react';
import { useUser } from '../contexts/UserContext';

const DebugX = () => {
  const { user } = useUser();
  const [debugInfo, setDebugInfo] = useState(null);
  const [loading, setLoading] = useState(false);

  const checkXAccount = async () => {
    if (!user) {
      setDebugInfo({ error: 'No user logged in' });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/debug/check-x-account?userId=${user.id}`);
      const data = await response.json();
      setDebugInfo(data);
    } catch (error) {
      setDebugInfo({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const checkAccountStatus = async () => {
    if (!user) {
      setDebugInfo({ error: 'No user logged in' });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/x/account-status?userId=${user.id}`);
      const data = await response.json();
      setDebugInfo({ accountStatus: data });
    } catch (error) {
      setDebugInfo({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '40px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>X Account Debug</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <strong>User:</strong> {user ? user.email : 'Not logged in'}
        <br />
        <strong>User ID:</strong> {user ? user.id : 'N/A'}
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={checkXAccount}
          disabled={loading || !user}
          style={{
            padding: '12px 24px',
            background: '#1DA1F2',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          {loading ? 'Loading...' : 'Check X Account in DB'}
        </button>

        <button 
          onClick={checkAccountStatus}
          disabled={loading || !user}
          style={{
            padding: '12px 24px',
            background: '#10B981',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer'
          }}
        >
          {loading ? 'Loading...' : 'Check Account Status API'}
        </button>
      </div>

      {debugInfo && (
        <div style={{
          padding: '20px',
          background: '#f5f5f5',
          borderRadius: '8px',
          marginTop: '20px'
        }}>
          <h3>Debug Info:</h3>
          <pre style={{ 
            background: 'white', 
            padding: '15px', 
            borderRadius: '4px',
            overflow: 'auto',
            fontSize: '12px'
          }}>
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default DebugX;
