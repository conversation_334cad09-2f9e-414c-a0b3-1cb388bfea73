import React, { useState } from 'react';

const SetupXTables = () => {
  const [result, setResult] = useState('');
  const [loading, setLoading] = useState(false);

  const createTables = async () => {
    setLoading(true);
    setResult('Creating X account tables...');

    try {
      const response = await fetch('/api/setup/create-x-tables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (data.success) {
        setResult('✅ Success! X account tables created successfully.');
      } else {
        setResult(`❌ Error: ${data.error}\n\nPlease run this SQL manually in Supabase:\n\n${data.sql}`);
      }
    } catch (error) {
      setResult(`❌ Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '40px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Setup X Account Tables</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <p>This will create the proper database tables for X account connections:</p>
        <ul>
          <li><code>x_accounts</code> - Store connected X account info</li>
          <li><code>x_oauth_tokens</code> - Temporary OAuth tokens</li>
        </ul>
      </div>

      <button 
        onClick={createTables}
        disabled={loading}
        style={{
          padding: '12px 24px',
          background: '#1DA1F2',
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          cursor: 'pointer',
          fontSize: '16px'
        }}
      >
        {loading ? 'Creating Tables...' : 'Create X Account Tables'}
      </button>

      {result && (
        <div style={{
          padding: '20px',
          background: result.includes('✅') ? '#F0FDF4' : '#FEF2F2',
          border: `1px solid ${result.includes('✅') ? '#BBF7D0' : '#FECACA'}`,
          borderRadius: '8px',
          marginTop: '20px'
        }}>
          <pre style={{ 
            margin: 0,
            whiteSpace: 'pre-wrap',
            fontFamily: 'monospace'
          }}>
            {result}
          </pre>
        </div>
      )}

      <div style={{
        marginTop: '40px',
        padding: '20px',
        background: '#F8FAFC',
        borderRadius: '8px',
        border: '1px solid #E2E8F0'
      }}>
        <h3>Manual Setup (if needed)</h3>
        <p>If the automatic setup doesn't work, run this SQL in your Supabase SQL editor:</p>
        <pre style={{
          background: '#1F2937',
          color: '#F9FAFB',
          padding: '15px',
          borderRadius: '6px',
          overflow: 'auto',
          fontSize: '12px'
        }}>
{`-- Create X account connection tables
CREATE TABLE IF NOT EXISTS x_accounts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  x_user_id VARCHAR(255) NOT NULL,
  username VARCHAR(255) NOT NULL,
  name VARCHAR(255),
  profile_image_url TEXT,
  access_token TEXT NOT NULL,
  access_secret TEXT NOT NULL,
  is_active BOOLEAN DEFAULT true,
  connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  disconnected_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, x_user_id)
);

-- Create temporary OAuth tokens table
CREATE TABLE IF NOT EXISTS x_oauth_tokens (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  oauth_token VARCHAR(255) NOT NULL,
  oauth_token_secret VARCHAR(255) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(oauth_token)
);

-- Enable Row Level Security
ALTER TABLE x_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE x_oauth_tokens ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can manage their own X accounts" ON x_accounts
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own OAuth tokens" ON x_oauth_tokens
  FOR ALL USING (auth.uid() = user_id);`}
        </pre>
      </div>
    </div>
  );
};

export default SetupXTables;
