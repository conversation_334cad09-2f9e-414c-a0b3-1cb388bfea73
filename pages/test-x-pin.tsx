import React, { useState } from 'react';
import { useUser } from '../contexts/UserContext';

const TestXPin = () => {
  const { user } = useUser();
  const [pin, setPin] = useState('');
  const [oauthToken, setOauthToken] = useState('');
  const [oauthTokenSecret, setOauthTokenSecret] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState('');

  const handleGetAuthUrl = async () => {
    if (!user) {
      setResult('Please log in first');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/x/auth-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id }),
      });

      const data = await response.json();
      
      if (response.ok) {
        setOauthToken(data.oauth_token);
        setOauthTokenSecret(data.oauth_token_secret);
        setResult(`Auth URL generated. OAuth Token: ${data.oauth_token.substring(0, 20)}...`);
        window.open(data.authUrl, '_blank');
      } else {
        setResult(`Error: ${data.error}`);
      }
    } catch (error) {
      setResult(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyPin = async () => {
    if (!user || !pin || !oauthToken || !oauthTokenSecret) {
      setResult('Missing required data');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/x/verify-pin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          oauth_token: oauthToken,
          oauth_token_secret: oauthTokenSecret,
          pin: pin.trim(),
          userId: user.id
        }),
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(`Success! Connected account: @${data.accountInfo.username}`);
      } else {
        setResult(`Error: ${data.error}`);
      }
    } catch (error) {
      setResult(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '40px', maxWidth: '600px', margin: '0 auto' }}>
      <h1>Test X Account Connection</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <strong>User:</strong> {user ? user.email : 'Not logged in'}
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={handleGetAuthUrl}
          disabled={loading || !user}
          style={{
            padding: '12px 24px',
            background: '#1DA1F2',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          {loading ? 'Loading...' : 'Get X Auth URL'}
        </button>
      </div>

      {oauthToken && (
        <div style={{ marginBottom: '20px' }}>
          <div style={{ marginBottom: '10px' }}>
            <strong>OAuth Token:</strong> {oauthToken.substring(0, 20)}...
          </div>
          
          <div style={{ marginBottom: '10px' }}>
            <label>
              <strong>Enter PIN from X:</strong>
              <br />
              <input
                type="text"
                value={pin}
                onChange={(e) => setPin(e.target.value)}
                placeholder="Enter PIN code"
                style={{
                  padding: '8px',
                  fontSize: '16px',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  width: '200px',
                  marginTop: '5px'
                }}
              />
            </label>
          </div>

          <button 
            onClick={handleVerifyPin}
            disabled={loading || !pin}
            style={{
              padding: '12px 24px',
              background: '#10B981',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            {loading ? 'Verifying...' : 'Verify PIN'}
          </button>
        </div>
      )}

      {result && (
        <div style={{
          padding: '15px',
          background: result.includes('Error') ? '#FEE2E2' : '#F0FDF4',
          border: `1px solid ${result.includes('Error') ? '#FECACA' : '#BBF7D0'}`,
          borderRadius: '8px',
          marginTop: '20px'
        }}>
          <strong>Result:</strong> {result}
        </div>
      )}
    </div>
  );
};

export default TestXPin;
