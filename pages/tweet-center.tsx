// pages/tweet-center.tsx
import React, { useState, useRef, useEffect } from 'react';
import SidebarLayout from '../components/SidebarLayoutSimple';
import { Bot } from 'lucide-react';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const TweetCenterPage: NextPageWithLayout = () => {
  const [content, setContent] = useState('');
  const [aiSuggestion, setAiSuggestion] = useState('');
  const [showSuggestion, setShowSuggestion] = useState(false);
  const [showAgentEInput, setShowAgentEInput] = useState(false);
  const [agentEPrompt, setAgentEPrompt] = useState('');
  const [showOptionsDropdown, setShowOptionsDropdown] = useState(false);
  const [autoRunMode, setAutoRunMode] = useState(false);
  const [aiEnabled, setAiEnabled] = useState(true);
  const [formatMode, setFormatMode] = useState<'thread' | 'single'>('single');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    border: '#F5E6D3',
    borderHover: '#E8D5C4',
    surface: '#FFFFFF',
    surfaceHover: '#F9F7F4',
    warmGlow: '#FFE0B2',
    paper: '#FEFEFE'
  };

  // Real AI prediction based on content context
  const generateContextualSuggestion = (text: string) => {
    if (text.length < 10) return '';

    try {
      // Simple prediction logic based on sentence patterns
      const lastSentence = text.split('.').pop()?.trim() || text;

      // If sentence seems incomplete, suggest completion
      if (lastSentence.length > 0) {
        // Sports context
        if (lastSentence.toLowerCase().includes('sports') || lastSentence.toLowerCase().includes('game') || lastSentence.toLowerCase().includes('team')) {
          const sportsSuggestions = [
            ' requires dedication and consistent practice',
            ' teaches us valuable life lessons',
            ' brings people together like nothing else',
            ' is more than just competition'
          ];
          return sportsSuggestions[Math.floor(Math.random() * sportsSuggestions.length)];
        }

        // Tech context
        if (lastSentence.toLowerCase().includes('technology') || lastSentence.toLowerCase().includes('coding') || lastSentence.toLowerCase().includes('software')) {
          const techSuggestions = [
            ' is evolving faster than ever before',
            ' has the power to solve real problems',
            ' requires continuous learning and adaptation',
            ' should be accessible to everyone'
          ];
          return techSuggestions[Math.floor(Math.random() * techSuggestions.length)];
        }

        // Business context
        if (lastSentence.toLowerCase().includes('business') || lastSentence.toLowerCase().includes('startup') || lastSentence.toLowerCase().includes('entrepreneur')) {
          const businessSuggestions = [
            ' is about solving problems for people',
            ' requires patience and persistence',
            ' success comes from understanding your customers',
            ' failure is just feedback in disguise'
          ];
          return businessSuggestions[Math.floor(Math.random() * businessSuggestions.length)];
        }

        // General sentence completion based on common patterns
        if (lastSentence.endsWith('I think') || lastSentence.endsWith('I believe')) {
          return ' that consistency beats perfection every time';
        }

        if (lastSentence.includes('The key to')) {
          return ' success is taking action despite uncertainty';
        }

        if (lastSentence.includes('What I learned')) {
          return ' is that small steps lead to big changes';
        }

        // Default contextual completions
        const generalSuggestions = [
          ' and here\'s why that matters',
          ' - let me explain',
          ' in my experience',
          ' based on what I\'ve seen',
          ' and the results speak for themselves'
        ];

        return generalSuggestions[Math.floor(Math.random() * generalSuggestions.length)];
      }

      return '';
    } catch (error) {
      console.error('Error generating suggestion:', error);
      return '';
    }
  };

  // AI prediction with context awareness
  useEffect(() => {
    if (content.length > 15 && aiEnabled) {
      const timer = setTimeout(() => {
        const suggestion = generateContextualSuggestion(content);
        setAiSuggestion(suggestion);
        setShowSuggestion(true);
      }, 800);
      return () => clearTimeout(timer);
    } else {
      setShowSuggestion(false);
    }
  }, [content, aiEnabled]);

  const handleTabPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab' && showSuggestion) {
      e.preventDefault();
      setContent(content + aiSuggestion);
      setShowSuggestion(false);
      setAiSuggestion('');
    }
  };



  const handleAgentESubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!agentEPrompt.trim()) return;

    // Generate content based on the prompt
    const generatedContent = generateContentFromPrompt(agentEPrompt);
    setContent(generatedContent);
    setAgentEPrompt('');
    setShowAgentEInput(false);
  };

  const generateContentFromPrompt = (prompt: string) => {
    const lowerPrompt = prompt.toLowerCase();

    // Different content types based on prompt
    if (lowerPrompt.includes('thread') || lowerPrompt.includes('twitter thread')) {
      return `Here's a thread about ${prompt.replace(/thread|twitter thread/gi, '').trim()}:\n\n1/ The key to understanding this topic is...\n\n2/ Most people think...\n\n3/ But here's what actually works...`;
    }

    if (lowerPrompt.includes('tips') || lowerPrompt.includes('advice')) {
      return `${prompt.charAt(0).toUpperCase() + prompt.slice(1)}:\n\n• Focus on the fundamentals first\n• Consistency beats perfection\n• Learn from others who've succeeded\n• Take action despite uncertainty`;
    }

    if (lowerPrompt.includes('story') || lowerPrompt.includes('experience')) {
      return `Here's my experience with ${prompt.replace(/story|experience/gi, '').trim()}:\n\nIt started when I realized that most advice online was generic. I needed something that actually worked in the real world...`;
    }

    // Default content generation
    return `${prompt.charAt(0).toUpperCase() + prompt.slice(1)}.\n\nHere's what I've learned from years of experience: the biggest difference between success and failure isn't talent or luck—it's consistency.\n\nMost people give up right before they would have succeeded.`;
  };

  // Auto-run mode - automatically generates tweets
  const generateAutoTweet = () => {
    const topics = [
      'productivity tips for entrepreneurs',
      'lessons learned from building a startup',
      'the importance of consistency in business',
      'how to stay motivated during tough times',
      'building habits that stick',
      'the power of compound growth',
      'why most people give up too early',
      'simple strategies for better focus'
    ];

    const randomTopic = topics[Math.floor(Math.random() * topics.length)];
    const generatedContent = generateContentFromPrompt(randomTopic);
    setContent(generatedContent);
  };

  // Auto-run effect
  useEffect(() => {
    if (autoRunMode && !content) {
      const timer = setTimeout(() => {
        generateAutoTweet();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [autoRunMode, content]);

  return (
    <div style={{
      padding: '40px 60px',
      height: '100vh',
      overflow: 'auto',
      background: colors.paper,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center'
    }}>
      {/* Header */}
      <div style={{
        width: '100%',
        maxWidth: '900px',
        marginBottom: '40px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h1 style={{
          color: colors.text.primary,
          margin: 0,
          fontSize: '28px',
          fontWeight: '600',
          letterSpacing: '-0.4px',
          fontFamily: 'SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif'
        }}>
          Drafting Desk
        </h1>

        {/* Options Dropdown */}
        <div style={{ position: 'relative' }}>
          <button
            onClick={() => setShowOptionsDropdown(!showOptionsDropdown)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 16px',
              background: colors.surface,
              border: `1px solid ${colors.border}`,
              borderRadius: '12px',
              fontSize: '14px',
              fontWeight: '500',
              color: colors.text.primary,
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
          >
            Options
            <span style={{
              transform: showOptionsDropdown ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s ease',
              fontSize: '12px'
            }}>
              ▼
            </span>
          </button>

          {/* Dropdown Menu */}
          {showOptionsDropdown && (
            <div style={{
              position: 'absolute',
              top: '100%',
              right: 0,
              marginTop: '8px',
              background: 'white',
              border: `1px solid ${colors.border}`,
              borderRadius: '12px',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
              padding: '12px',
              minWidth: '220px',
              zIndex: 1000
            }}>
              {/* AI Predictions Toggle */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px 0',
                borderBottom: `1px solid ${colors.border}`
              }}>
                <span style={{ fontSize: '14px', color: colors.text.primary }}>
                  AI Predictions
                </span>
                <button
                  onClick={() => setAiEnabled(!aiEnabled)}
                  style={{
                    width: '40px',
                    height: '20px',
                    borderRadius: '10px',
                    border: 'none',
                    background: aiEnabled ? colors.primary : colors.border,
                    position: 'relative',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                >
                  <div style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    background: 'white',
                    position: 'absolute',
                    top: '2px',
                    left: aiEnabled ? '22px' : '2px',
                    transition: 'all 0.2s ease'
                  }} />
                </button>
              </div>

              {/* Auto-Run Mode Toggle */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px 0',
                borderBottom: `1px solid ${colors.border}`
              }}>
                <div>
                  <span style={{ fontSize: '14px', color: colors.text.primary, fontWeight: '500' }}>
                    Auto-Run Mode ✨
                  </span>
                  <div style={{ fontSize: '12px', color: colors.text.tertiary, marginTop: '2px' }}>
                    Automatically generates tweets
                  </div>
                </div>
                <button
                  onClick={() => {
                    setAutoRunMode(!autoRunMode);
                    if (!autoRunMode) {
                      setContent(''); // Clear content to trigger auto-generation
                    }
                  }}
                  style={{
                    width: '40px',
                    height: '20px',
                    borderRadius: '10px',
                    border: 'none',
                    background: autoRunMode ? '#10B981' : colors.border,
                    position: 'relative',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    boxShadow: autoRunMode ? '0 0 12px rgba(16, 185, 129, 0.4)' : 'none'
                  }}
                >
                  <div style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    background: 'white',
                    position: 'absolute',
                    top: '2px',
                    left: autoRunMode ? '22px' : '2px',
                    transition: 'all 0.2s ease'
                  }} />
                </button>
              </div>

              {/* Format Mode Toggle */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px 0',
                borderBottom: `1px solid ${colors.border}`
              }}>
                <span style={{ fontSize: '14px', color: colors.text.primary }}>
                  Thread Mode
                </span>
                <button
                  onClick={() => setFormatMode(formatMode === 'thread' ? 'single' : 'thread')}
                  style={{
                    width: '40px',
                    height: '20px',
                    borderRadius: '10px',
                    border: 'none',
                    background: formatMode === 'thread' ? colors.primary : colors.border,
                    position: 'relative',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                >
                  <div style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    background: 'white',
                    position: 'absolute',
                    top: '2px',
                    left: formatMode === 'thread' ? '22px' : '2px',
                    transition: 'all 0.2s ease'
                  }} />
                </button>
              </div>

              {/* Agent E Button */}
              <div style={{
                padding: '8px 0'
              }}>
                <button
                  onClick={() => {
                    setShowAgentEInput(true);
                    setShowOptionsDropdown(false);
                  }}
                  style={{
                    width: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '8px',
                    padding: '10px 16px',
                    background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '600',
                    color: 'white',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                >
                  <Bot size={16} />
                  Ask Agent E
                </button>
              </div>
            </div>
          )}
        </div>
      </div>



      {/* Seamless Writing Interface */}
      <div style={{
        width: '100%',
        maxWidth: '900px',
        background: 'transparent',
        position: 'relative',
        minHeight: '500px',
        display: 'flex',
        flexDirection: 'column'
      }}>

        {/* Inline Writing Area with Tab Completion */}
        <div style={{
          flex: 1,
          position: 'relative',
          padding: '40px 60px',
          minHeight: '450px'
        }}>
          {/* AI Suggestion Overlay - Inline Gray Text */}
          {showSuggestion && aiEnabled && (
            <div style={{
              position: 'absolute',
              top: '40px',
              left: '60px',
              right: '60px',
              bottom: '40px',
              pointerEvents: 'none',
              zIndex: 1,
              overflow: 'hidden'
            }}>
              <div className="sf-pro" style={{
                fontSize: '20px',
                lineHeight: '1.7',
                color: 'transparent',
                whiteSpace: 'pre-wrap',
                wordWrap: 'break-word',
                letterSpacing: '0.3px',
                fontWeight: '400'
              }}>
                {content}
                <span style={{
                  color: '#9CA3AF',
                  opacity: 0.6,
                  fontStyle: 'normal'
                }}>
                  {aiSuggestion}
                </span>
              </div>
            </div>
          )}

          {/* Main textarea */}
          <textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onKeyDown={handleTabPress}
            placeholder={aiEnabled ? 'Start writing...' : 'What\'s on your mind?'}
            className="sf-pro"
            style={{
              width: '100%',
              height: '100%',
              minHeight: '400px',
              padding: '0',
              border: 'none',
              background: 'transparent',
              fontSize: '20px',
              lineHeight: '1.7',
              color: colors.text.primary,
              resize: 'none',
              outline: 'none',
              letterSpacing: '0.3px',
              position: 'relative',
              zIndex: 2,
              fontWeight: '400'
            }}
          />
        </div>

        {/* Minimal Action Bar */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '0 60px 40px',
          marginTop: '20px'
        }}>
          <div className="sf-pro" style={{
            fontSize: '14px',
            color: colors.text.secondary,
            fontWeight: '400',
            opacity: 0.7
          }}>
            {content.length} characters
          </div>

          <div style={{ display: 'flex', gap: '12px' }}>
            <button
              onClick={() => setContent('')}
              className="sf-pro"
              style={{
                padding: '12px 24px',
                background: 'transparent',
                border: `1px solid ${colors.border}`,
                borderRadius: '10px',
                color: colors.text.secondary,
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.background = colors.surfaceHover;
                target.style.borderColor = colors.borderHover;
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.background = 'transparent';
                target.style.borderColor = colors.border;
              }}
            >
              Save Draft
            </button>

            <button
              onClick={() => {
                // Publish logic here
                console.log('Publishing:', content);
              }}
              className="sf-pro"
              style={{
                padding: '12px 28px',
                background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                border: 'none',
                borderRadius: '10px',
                color: 'white',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                boxShadow: `0 4px 12px ${colors.primary}30`,
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.transform = 'translateY(-1px)';
                target.style.boxShadow = `0 6px 16px ${colors.primary}40`;
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.transform = 'translateY(0)';
                target.style.boxShadow = `0 4px 12px ${colors.primary}30`;
              }}
            >
              Publish
            </button>
          </div>
        </div>

        {/* Agent E Input Field - Only shows when clicked */}
        {showAgentEInput && (
          <div style={{
            position: 'fixed',
            bottom: '24px',
            left: '50%',
            transform: 'translateX(-50%)',
            width: '600px',
            height: '48px',
            background: 'white',
            borderRadius: '24px',
            display: 'flex',
            alignItems: 'center',
            padding: '0 20px',
            boxShadow: `0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05)`,
            zIndex: 1000,
            border: `2px solid ${colors.primary}`
          }}>
            <Bot size={18} color={colors.primary} style={{ marginRight: '12px' }} />
            <form onSubmit={handleAgentESubmit} style={{ flex: 1, display: 'flex', alignItems: 'center' }}>
              <input
                type="text"
                value={agentEPrompt}
                onChange={(e) => setAgentEPrompt(e.target.value)}
                placeholder="Ask Agent E to write something for you..."
                autoFocus
                style={{
                  flex: 1,
                  border: 'none',
                  outline: 'none',
                  fontSize: '14px',
                  color: colors.text.primary,
                  background: 'transparent',
                  fontWeight: '400'
                }}
              />
              <button
                type="submit"
                style={{
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  padding: '4px',
                  marginLeft: '8px'
                }}
              >
                <span style={{ fontSize: '16px' }}>↵</span>
              </button>
            </form>
            <button
              onClick={() => setShowAgentEInput(false)}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                padding: '4px',
                marginLeft: '8px',
                color: colors.text.tertiary
              }}
            >
              ✕
            </button>
          </div>
        )}
      </div>



      <style jsx>{`
        @keyframes pulse {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.2); }
        }
      `}</style>
    </div>
  );
};

TweetCenterPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default TweetCenterPage;