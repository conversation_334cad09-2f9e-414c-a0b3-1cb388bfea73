OPENAI_API_KEY=********************************************************************************************************************************************************************
ELEVENLABS_API_KEY=***************************************************
D_ID_API_KEY=***********************************:NPysxyEDWMHypUpGXdNcF
X_API_KEY=*************************
X_API_SECRET_KEY=lD2zt7nwDJau6Emv2WAOAS4VXepnucgzaxoGeWyp1m7TDAQROa

# Twitter API v2 credentials (for posting)
TWITTER_API_KEY=*************************
TWITTER_API_SECRET=lD2zt7nwDJau6Emv2WAOAS4VXepnucgzaxoGeWyp1m7TDAQROa
TWITTER_ACCESS_TOKEN=your_access_token_here
TWITTER_ACCESS_SECRET=your_access_secret_here

# Next.js URL for OAuth callbacks
NEXTAUTH_URL=http://localhost:3002

# Cron job security
CRON_SECRET=your_secure_random_string_here

AGORA_APP_ID=a64041bae4ed448fab0c24c1e2b9ab3b
DEEPGRAM_API_KEY=****************************************

# X OAuth 2.0 Credentials (for modern OAuth flow)
X_CLIENT_ID=NHcyejc1M3oyLTRTTDAybl9uVUI6MTpjaQ
X_CLIENT_SECRET=XU1XL4k3fAgmiuVxbeEEYjcjRtU3jd2K86Y5E2m5qpKYDiQZEY

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://nlckamsrdiwkyyrxzntf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here