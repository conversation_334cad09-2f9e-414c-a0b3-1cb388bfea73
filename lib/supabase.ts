import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Client-side Supabase client (with RLS)
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Server-side Supabase client (bypasses RLS) - only create if service key exists
export const supabaseAdmin = supabaseServiceKey
  ? createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
  : null

// Types for our database
export interface User {
  id: string
  email: string
  full_name: string
  avatar_url?: string
  plan: 'free' | 'pro' | 'enterprise'
  created_at: string
  updated_at: string
  is_online: boolean
  last_seen: string
}

export interface UserProfile {
  id: string
  user_id: string
  full_name: string
  avatar_url?: string
  plan: 'free' | 'pro' | 'enterprise'
  subscription_status: 'active' | 'inactive' | 'cancelled'
  subscription_end_date?: string
  is_online: boolean
  last_seen: string
  created_at: string
  updated_at: string
}

export interface AgentESettings {
  id: string
  user_id: string
  project_name: string
  project_description: string
  target_audience: string
  brand_voice: string
  custom_prompts: Array<{
    id: number
    name: string
    prompt: string
  }>
  x_api_key?: string
  x_api_secret?: string
  created_at: string
  updated_at: string
}
