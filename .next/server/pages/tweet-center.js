/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/tweet-center";
exports.ids = ["pages/tweet-center"];
exports.modules = {

/***/ "__barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!********************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlignCenter: () => (/* reexport safe */ _icons_align_center_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   AlignLeft: () => (/* reexport safe */ _icons_align_left_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Bot: () => (/* reexport safe */ _icons_bot_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Sparkles: () => (/* reexport safe */ _icons_sparkles_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Type: () => (/* reexport safe */ _icons_type_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_align_center_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/align-center.js */ \"./node_modules/lucide-react/dist/esm/icons/align-center.js\");\n/* harmony import */ var _icons_align_left_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/align-left.js */ \"./node_modules/lucide-react/dist/esm/icons/align-left.js\");\n/* harmony import */ var _icons_bot_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/bot.js */ \"./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _icons_sparkles_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/sparkles.js */ \"./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _icons_type_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/type.js */ \"./node_modules/lucide-react/dist/esm/icons/type.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BbGlnbkNlbnRlcixBbGlnbkxlZnQsQm90LFNwYXJrbGVzLFR5cGUhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQ2dFO0FBQ0o7QUFDYjtBQUNVIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzIzODkiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFsaWduQ2VudGVyIH0gZnJvbSBcIi4vaWNvbnMvYWxpZ24tY2VudGVyLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQWxpZ25MZWZ0IH0gZnJvbSBcIi4vaWNvbnMvYWxpZ24tbGVmdC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJvdCB9IGZyb20gXCIuL2ljb25zL2JvdC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNwYXJrbGVzIH0gZnJvbSBcIi4vaWNvbnMvc3BhcmtsZXMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUeXBlIH0gZnJvbSBcIi4vaWNvbnMvdHlwZS5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Calendar: () => (/* reexport safe */ _icons_calendar_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Home: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   LogIn: () => (/* reexport safe */ _icons_log_in_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   MessageCircle: () => (/* reexport safe */ _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Search: () => (/* reexport safe */ _icons_search_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Video: () => (/* reexport safe */ _icons_video_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_calendar_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/calendar.js */ \"./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/house.js */ \"./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _icons_log_in_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/log-in.js */ \"./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/message-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _icons_search_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/search.js */ \"./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_video_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/video.js */ \"./node_modules/lucide-react/dist/esm/icons/video.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ2FsZW5kYXIsSG9tZSxMb2dJbixNZXNzYWdlQ2lyY2xlLFNlYXJjaCxVc2VyLFZpZGVvIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM2RDtBQUNKO0FBQ1A7QUFDRTtBQUNnQjtBQUNmO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/ZjIwNiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQmFyQ2hhcnQzIH0gZnJvbSBcIi4vaWNvbnMvYmFyLWNoYXJ0LTMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYWxlbmRhciB9IGZyb20gXCIuL2ljb25zL2NhbGVuZGFyLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSG9tZSB9IGZyb20gXCIuL2ljb25zL2hvdXNlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9nSW4gfSBmcm9tIFwiLi9pY29ucy9sb2ctaW4uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNZXNzYWdlQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvbWVzc2FnZS1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZWFyY2ggfSBmcm9tIFwiLi9pY29ucy9zZWFyY2guanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyIH0gZnJvbSBcIi4vaWNvbnMvdXNlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFZpZGVvIH0gZnJvbSBcIi4vaWNvbnMvdmlkZW8uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Bot,CheckCircle,Loader,Send,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**********************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bot,CheckCircle,Loader,Send,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bot: () => (/* reexport safe */ _icons_bot_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_circle_check_big_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Loader: () => (/* reexport safe */ _icons_loader_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Send: () => (/* reexport safe */ _icons_send_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bot_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bot.js */ \"./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _icons_circle_check_big_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/circle-check-big.js */ \"./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _icons_loader_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/loader.js */ \"./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _icons_send_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/send.js */ \"./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Cb3QsQ2hlY2tDaXJjbGUsTG9hZGVyLFNlbmQsWCE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFDK0M7QUFDcUI7QUFDZjtBQUNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzRmMmEiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJvdCB9IGZyb20gXCIuL2ljb25zL2JvdC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZWNrQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvY2lyY2xlLWNoZWNrLWJpZy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExvYWRlciB9IGZyb20gXCIuL2ljb25zL2xvYWRlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNlbmQgfSBmcm9tIFwiLi9pY29ucy9zZW5kLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgWCB9IGZyb20gXCIuL2ljb25zL3guanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Bot,CheckCircle,Loader,Send,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!********************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Eye: () => (/* reexport safe */ _icons_eye_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   EyeOff: () => (/* reexport safe */ _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Lock: () => (/* reexport safe */ _icons_lock_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Mail: () => (/* reexport safe */ _icons_mail_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_eye_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/eye.js */ \"./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/eye-off.js */ \"./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _icons_lock_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/lock.js */ \"./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _icons_mail_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/mail.js */ \"./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FeWUsRXllT2ZmLExvY2ssTWFpbCxVc2VyLFghPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDK0M7QUFDTztBQUNMO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2V4aWUtYWkvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9lMzY0Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFeWUgfSBmcm9tIFwiLi9pY29ucy9leWUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFeWVPZmYgfSBmcm9tIFwiLi9pY29ucy9leWUtb2ZmLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9jayB9IGZyb20gXCIuL2ljb25zL2xvY2suanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNYWlsIH0gZnJvbSBcIi4vaWNvbnMvbWFpbC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXIgfSBmcm9tIFwiLi9pY29ucy91c2VyLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgWCB9IGZyb20gXCIuL2ljb25zL3guanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftweet-center&preferredRegion=&absolutePagePath=.%2Fpages%2Ftweet-center.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftweet-center&preferredRegion=&absolutePagePath=.%2Fpages%2Ftweet-center.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/tweet-center.tsx */ \"./pages/tweet-center.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/tweet-center\",\n        pathname: \"/tweet-center\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTJnBhZ2U9JTJGdHdlZXQtY2VudGVyJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGcGFnZXMlMkZ0d2VldC1jZW50ZXIudHN4JmFic29sdXRlQXBwUGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfYXBwJmFic29sdXRlRG9jdW1lbnRQYXRoPXByaXZhdGUtbmV4dC1wYWdlcyUyRl9kb2N1bWVudCZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDaEM7QUFDTDtBQUMxRDtBQUNvRDtBQUNWO0FBQzFDO0FBQ3FEO0FBQ3JEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyxvREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyx1QkFBdUIsd0VBQUssQ0FBQyxvREFBUTtBQUNyQyx1QkFBdUIsd0VBQUssQ0FBQyxvREFBUTtBQUNyQywyQkFBMkIsd0VBQUssQ0FBQyxvREFBUTtBQUN6QyxlQUFlLHdFQUFLLENBQUMsb0RBQVE7QUFDN0Isd0JBQXdCLHdFQUFLLENBQUMsb0RBQVE7QUFDN0M7QUFDTyxnQ0FBZ0Msd0VBQUssQ0FBQyxvREFBUTtBQUM5QyxnQ0FBZ0Msd0VBQUssQ0FBQyxvREFBUTtBQUM5QyxpQ0FBaUMsd0VBQUssQ0FBQyxvREFBUTtBQUMvQyxnQ0FBZ0Msd0VBQUssQ0FBQyxvREFBUTtBQUM5QyxvQ0FBb0Msd0VBQUssQ0FBQyxvREFBUTtBQUN6RDtBQUNPLHdCQUF3Qix5R0FBZ0I7QUFDL0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxXQUFXO0FBQ1gsZ0JBQWdCO0FBQ2hCLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL2V4aWUtYWkvPzEwM2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSBhcHAgYW5kIGRvY3VtZW50IG1vZHVsZXMuXG5pbXBvcnQgRG9jdW1lbnQgZnJvbSBcInByaXZhdGUtbmV4dC1wYWdlcy9fZG9jdW1lbnRcIjtcbmltcG9ydCBBcHAgZnJvbSBcInByaXZhdGUtbmV4dC1wYWdlcy9fYXBwXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlcy90d2VldC1jZW50ZXIudHN4XCI7XG4vLyBSZS1leHBvcnQgdGhlIGNvbXBvbmVudCAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IG1ldGhvZHMuXG5leHBvcnQgY29uc3QgZ2V0U3RhdGljUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgXCJnZXRTdGF0aWNQcm9wc1wiKTtcbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCBcImdldFN0YXRpY1BhdGhzXCIpO1xuZXhwb3J0IGNvbnN0IGdldFNlcnZlclNpZGVQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCBcImdldFNlcnZlclNpZGVQcm9wc1wiKTtcbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG5leHBvcnQgY29uc3QgcmVwb3J0V2ViVml0YWxzID0gaG9pc3QodXNlcmxhbmQsIFwicmVwb3J0V2ViVml0YWxzXCIpO1xuLy8gUmUtZXhwb3J0IGxlZ2FjeSBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U3RhdGljUHJvcHNcIik7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U3RhdGljUGF0aHMgPSBob2lzdCh1c2VybGFuZCwgXCJ1bnN0YWJsZV9nZXRTdGF0aWNQYXRoc1wiKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXJhbXMgPSBob2lzdCh1c2VybGFuZCwgXCJ1bnN0YWJsZV9nZXRTdGF0aWNQYXJhbXNcIik7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgXCJ1bnN0YWJsZV9nZXRTZXJ2ZXJQcm9wc1wiKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTZXJ2ZXJTaWRlUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgXCJ1bnN0YWJsZV9nZXRTZXJ2ZXJTaWRlUHJvcHNcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc1JvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFUyxcbiAgICAgICAgcGFnZTogXCIvdHdlZXQtY2VudGVyXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi90d2VldC1jZW50ZXJcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIlxuICAgIH0sXG4gICAgY29tcG9uZW50czoge1xuICAgICAgICBBcHAsXG4gICAgICAgIERvY3VtZW50XG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftweet-center&preferredRegion=&absolutePagePath=.%2Fpages%2Ftweet-center.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/AgentEChatSimple.tsx":
/*!*****************************************!*\
  !*** ./components/AgentEChatSimple.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_Loader_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,Loader,Send,X!=!lucide-react */ \"__barrel_optimize__?names=Bot,CheckCircle,Loader,Send,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\n\n\n\nconst AgentEChatSimple = ({ isOpen, onClose, currentContent })=>{\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            id: \"1\",\n            type: \"agent\",\n            content: \"Hi! I'm Agent E. I can help you post, schedule, or improve your content. What would you like me to do?\",\n            timestamp: new Date()\n        }\n    ]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        surface: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\"\n        },\n        border: \"#F5E6D3\"\n    };\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleAgentAction = async (userMessage)=>{\n        const lowerMessage = userMessage.toLowerCase();\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 1500));\n        if (lowerMessage.includes(\"post now\") || lowerMessage.includes(\"publish\")) {\n            // Simulate posting to X\n            return `✅ Posted to X successfully! Your content \"${currentContent.substring(0, 50)}...\" is now live!`;\n        }\n        if (lowerMessage.includes(\"schedule\")) {\n            return `⏰ Scheduled your post for 9:00 AM tomorrow. I'll automatically post it at the optimal time for engagement.`;\n        }\n        if (lowerMessage.includes(\"improve\") || lowerMessage.includes(\"better\")) {\n            return `✨ Here's an improved version:\\n\\n\"${currentContent} 🚀 #AI #productivity #automation\"\\n\\nI've added relevant hashtags and emojis for better engagement!`;\n        }\n        if (lowerMessage.includes(\"thread\")) {\n            return `🧵 I can break this into a 3-part thread for better storytelling. Would you like me to create the thread structure?`;\n        }\n        return \"I can help you:\\n• Post now to X\\n• Schedule for later\\n• Improve your content\\n• Create a thread\\n\\nWhat would you like to do?\";\n    };\n    const handleSendMessage = async ()=>{\n        if (!inputValue.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            type: \"user\",\n            content: inputValue,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue(\"\");\n        setIsLoading(true);\n        try {\n            const agentResponse = await handleAgentAction(inputValue);\n            const agentMessage = {\n                id: (Date.now() + 1).toString(),\n                type: \"agent\",\n                content: agentResponse,\n                timestamp: new Date(),\n                status: inputValue.toLowerCase().includes(\"post now\") ? \"posted\" : \"sent\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    agentMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const quickActions = [\n        {\n            label: \"\\uD83D\\uDE80 Post Now\",\n            action: \"post now\"\n        },\n        {\n            label: \"⏰ Schedule\",\n            action: \"schedule this\"\n        },\n        {\n            label: \"✨ Improve\",\n            action: \"improve this\"\n        },\n        {\n            label: \"\\uD83E\\uDDF5 Thread\",\n            action: \"make thread\"\n        }\n    ];\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"fixed\",\n            bottom: \"20px\",\n            right: \"20px\",\n            width: \"400px\",\n            height: \"500px\",\n            background: colors.surface,\n            borderRadius: \"16px\",\n            boxShadow: \"0 12px 48px rgba(0, 0, 0, 0.15)\",\n            border: `1px solid ${colors.border}`,\n            display: \"flex\",\n            flexDirection: \"column\",\n            zIndex: 10000,\n            overflow: \"hidden\"\n        },\n        className: \"jsx-532d500bfc6d5d04\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"16px 20px\",\n                    borderBottom: `1px solid ${colors.border}`,\n                    background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                    color: \"white\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\"\n                },\n                className: \"jsx-532d500bfc6d5d04\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"12px\"\n                        },\n                        className: \"jsx-532d500bfc6d5d04\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Bot, {\n                                size: 20,\n                                color: \"white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-532d500bfc6d5d04\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            margin: 0,\n                                            fontSize: \"16px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        className: \"jsx-532d500bfc6d5d04\",\n                                        children: \"Agent E\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            margin: 0,\n                                            fontSize: \"12px\",\n                                            opacity: 0.8\n                                        },\n                                        className: \"jsx-532d500bfc6d5d04\",\n                                        children: \"AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        style: {\n                            background: \"none\",\n                            border: \"none\",\n                            color: \"white\",\n                            cursor: \"pointer\",\n                            padding: \"4px\"\n                        },\n                        className: \"jsx-532d500bfc6d5d04\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                            size: 18\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    flex: 1,\n                    padding: \"16px\",\n                    overflowY: \"auto\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    gap: \"12px\"\n                },\n                className: \"jsx-532d500bfc6d5d04\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                justifyContent: message.type === \"user\" ? \"flex-end\" : \"flex-start\"\n                            },\n                            className: \"jsx-532d500bfc6d5d04\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: \"80%\",\n                                    padding: \"10px 14px\",\n                                    borderRadius: message.type === \"user\" ? \"16px 16px 4px 16px\" : \"16px 16px 16px 4px\",\n                                    background: message.type === \"user\" ? `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)` : colors.background,\n                                    color: message.type === \"user\" ? \"white\" : colors.text.primary,\n                                    fontSize: \"14px\",\n                                    lineHeight: \"1.4\",\n                                    whiteSpace: \"pre-wrap\"\n                                },\n                                className: \"jsx-532d500bfc6d5d04\",\n                                children: [\n                                    message.content,\n                                    message.status === \"posted\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"4px\",\n                                            marginTop: \"6px\",\n                                            fontSize: \"12px\",\n                                            opacity: 0.8\n                                        },\n                                        className: \"jsx-532d500bfc6d5d04\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.CheckCircle, {\n                                                size: 12\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-532d500bfc6d5d04\",\n                                                children: \"Posted\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, undefined)\n                        }, message.id, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, undefined)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"flex-start\"\n                        },\n                        className: \"jsx-532d500bfc6d5d04\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: \"10px 14px\",\n                                borderRadius: \"16px 16px 16px 4px\",\n                                background: colors.background,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"8px\"\n                            },\n                            className: \"jsx-532d500bfc6d5d04\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Loader, {\n                                    size: 14,\n                                    style: {\n                                        animation: \"spin 1s linear infinite\"\n                                    },\n                                    color: colors.primary\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"14px\",\n                                        color: colors.text.secondary\n                                    },\n                                    className: \"jsx-532d500bfc6d5d04\",\n                                    children: \"Agent E is working...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef,\n                        className: \"jsx-532d500bfc6d5d04\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"12px 16px 0\",\n                    borderTop: `1px solid ${colors.border}`\n                },\n                className: \"jsx-532d500bfc6d5d04\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"grid\",\n                        gridTemplateColumns: \"repeat(2, 1fr)\",\n                        gap: \"8px\",\n                        marginBottom: \"12px\"\n                    },\n                    className: \"jsx-532d500bfc6d5d04\",\n                    children: quickActions.map((button, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setInputValue(button.action),\n                            style: {\n                                padding: \"8px 12px\",\n                                background: colors.background,\n                                border: `1px solid ${colors.border}`,\n                                borderRadius: \"8px\",\n                                fontSize: \"12px\",\n                                color: colors.text.secondary,\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\"\n                            },\n                            onMouseEnter: (e)=>{\n                                const target = e.target;\n                                target.style.background = colors.primary + \"20\";\n                                target.style.borderColor = colors.primary;\n                            },\n                            onMouseLeave: (e)=>{\n                                const target = e.target;\n                                target.style.background = colors.background;\n                                target.style.borderColor = colors.border;\n                            },\n                            className: \"jsx-532d500bfc6d5d04\",\n                            children: button.label\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"0 16px 16px\"\n                },\n                className: \"jsx-532d500bfc6d5d04\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        gap: \"8px\",\n                        alignItems: \"flex-end\"\n                    },\n                    className: \"jsx-532d500bfc6d5d04\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: inputValue,\n                            onChange: (e)=>setInputValue(e.target.value),\n                            onKeyPress: handleKeyPress,\n                            placeholder: \"Ask Agent E to help with your content...\",\n                            style: {\n                                flex: 1,\n                                padding: \"10px 12px\",\n                                border: `1px solid ${colors.border}`,\n                                borderRadius: \"12px\",\n                                fontSize: \"14px\",\n                                background: colors.surface,\n                                outline: \"none\"\n                            },\n                            className: \"jsx-532d500bfc6d5d04\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSendMessage,\n                            disabled: !inputValue.trim() || isLoading,\n                            style: {\n                                padding: \"10px\",\n                                background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                color: \"white\",\n                                border: \"none\",\n                                borderRadius: \"12px\",\n                                cursor: inputValue.trim() && !isLoading ? \"pointer\" : \"not-allowed\",\n                                opacity: inputValue.trim() && !isLoading ? 1 : 0.5\n                            },\n                            className: \"jsx-532d500bfc6d5d04\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Send, {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"532d500bfc6d5d04\",\n                children: \"@-webkit-keyframes spin{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{from{-moz-transform:rotate(0deg);transform:rotate(0deg)}to{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{from{-o-transform:rotate(0deg);transform:rotate(0deg)}to{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{from{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChatSimple.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgentEChatSimple);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AgentEChatSimple.tsx\n");

/***/ }),

/***/ "./components/AuthModal.tsx":
/*!**********************************!*\
  !*** ./components/AuthModal.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!lucide-react */ \"__barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\n\n\n\nconst AuthModal = ({ isOpen, onClose })=>{\n    const [isSignUp, setIsSignUp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { signIn, signUp } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        surface: \"#FFFFFF\",\n        background: \"#F5F1EB\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\"\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        try {\n            let result;\n            if (isSignUp) {\n                result = await signUp(email, password, fullName);\n            } else {\n                result = await signIn(email, password);\n            }\n            if (result.error) {\n                setError(result.error.message);\n            } else {\n                onClose();\n                // Reset form\n                setEmail(\"\");\n                setPassword(\"\");\n                setFullName(\"\");\n            }\n        } catch (err) {\n            setError(\"An unexpected error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"fixed\",\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            zIndex: 1000\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: colors.surface,\n                borderRadius: \"16px\",\n                padding: \"32px\",\n                width: \"100%\",\n                maxWidth: \"400px\",\n                margin: \"20px\",\n                boxShadow: \"0 20px 40px rgba(0, 0, 0, 0.1)\",\n                position: \"relative\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    style: {\n                        position: \"absolute\",\n                        top: \"16px\",\n                        right: \"16px\",\n                        background: \"none\",\n                        border: \"none\",\n                        cursor: \"pointer\",\n                        padding: \"8px\",\n                        borderRadius: \"8px\",\n                        color: colors.text.secondary\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: \"center\",\n                        marginBottom: \"32px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"32px\",\n                                color: colors.primary,\n                                fontFamily: \"Georgia, serif\",\n                                fontStyle: \"italic\",\n                                marginBottom: \"8px\"\n                            },\n                            children: \"ℰ\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: colors.text.primary,\n                                fontSize: \"24px\",\n                                fontWeight: \"600\",\n                                margin: 0,\n                                marginBottom: \"8px\"\n                            },\n                            children: isSignUp ? \"Create Account\" : \"Welcome Back\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: colors.text.secondary,\n                                fontSize: \"14px\",\n                                margin: 0\n                            },\n                            children: isSignUp ? \"Join Exie to get started\" : \"Sign in to your account\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    children: [\n                        isSignUp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        color: colors.text.primary,\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: \"Full Name\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.User, {\n                                            size: 16,\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                color: colors.text.tertiary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fullName,\n                                            onChange: (e)=>setFullName(e.target.value),\n                                            required: isSignUp,\n                                            style: {\n                                                width: \"100%\",\n                                                padding: \"12px 16px 12px 40px\",\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                boxSizing: \"border-box\"\n                                            },\n                                            placeholder: \"Enter your full name\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        color: colors.text.primary,\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Mail, {\n                                            size: 16,\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                color: colors.text.tertiary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            required: true,\n                                            style: {\n                                                width: \"100%\",\n                                                padding: \"12px 16px 12px 40px\",\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                boxSizing: \"border-box\"\n                                            },\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"24px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        color: colors.text.primary,\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: \"Password\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Lock, {\n                                            size: 16,\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                color: colors.text.tertiary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value),\n                                            required: true,\n                                            style: {\n                                                width: \"100%\",\n                                                padding: \"12px 40px 12px 40px\",\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                boxSizing: \"border-box\"\n                                            },\n                                            placeholder: \"Enter your password\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            style: {\n                                                position: \"absolute\",\n                                                right: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                background: \"none\",\n                                                border: \"none\",\n                                                cursor: \"pointer\",\n                                                color: colors.text.tertiary\n                                            },\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.EyeOff, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 33\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Eye, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 56\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, undefined),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: \"#DC2626\",\n                                fontSize: \"14px\",\n                                marginBottom: \"20px\",\n                                padding: \"12px\",\n                                backgroundColor: \"#FEF2F2\",\n                                border: \"1px solid #FECACA\",\n                                borderRadius: \"8px\"\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            style: {\n                                width: \"100%\",\n                                padding: \"12px\",\n                                background: loading ? colors.text.tertiary : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                color: \"white\",\n                                border: \"none\",\n                                borderRadius: \"8px\",\n                                fontSize: \"14px\",\n                                fontWeight: \"600\",\n                                cursor: loading ? \"not-allowed\" : \"pointer\",\n                                boxShadow: loading ? \"none\" : `0 4px 12px ${colors.primary}30`,\n                                marginBottom: \"20px\"\n                            },\n                            children: loading ? \"Please wait...\" : isSignUp ? \"Create Account\" : \"Sign In\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: \"center\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setIsSignUp(!isSignUp),\n                                style: {\n                                    background: \"none\",\n                                    border: \"none\",\n                                    color: colors.primary,\n                                    fontSize: \"14px\",\n                                    cursor: \"pointer\",\n                                    textDecoration: \"underline\"\n                                },\n                                children: isSignUp ? \"Already have an account? Sign in\" : \"Don't have an account? Sign up\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AuthModal.tsx\n");

/***/ }),

/***/ "./components/SidebarLayoutSimple.tsx":
/*!********************************************!*\
  !*** ./components/SidebarLayoutSimple.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthModal */ \"./components/AuthModal.tsx\");\n\n\n\n\n\n\n\nconst SidebarLayout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, profile, loading } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Debug logging\n    console.log(\"Sidebar render:\", {\n        user: !!user,\n        userEmail: user?.email,\n        profile: !!profile,\n        profileName: profile?.full_name,\n        loading\n    });\n    // User data - only use real data when authenticated\n    const userData = user ? {\n        name: profile?.full_name || user?.user_metadata?.full_name || user?.email?.split(\"@\")[0] || \"User\",\n        email: user?.email || \"\",\n        plan: profile?.plan === \"pro\" ? \"Pro\" : profile?.plan === \"enterprise\" ? \"Enterprise\" : \"Free\",\n        avatar: profile?.avatar_url || null,\n        isOnline: profile?.is_online || false\n    } : null;\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        background: \"#F5F1EB\",\n        surface: \"#FFFFFF\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            surface: \"rgba(255, 107, 53, 0.05)\",\n            text: \"#2D1B14\",\n            textSecondary: \"#5D4037\",\n            textMuted: \"#8D6E63\",\n            accent: \"#FF6B35\",\n            accentSoft: \"#FF8A65\",\n            accentLight: \"#FFF7F4\",\n            border: \"rgba(255, 107, 53, 0.15)\",\n            hover: \"rgba(255, 107, 53, 0.08)\",\n            divider: \"rgba(255, 107, 53, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.25)\" // Orange glow\n        }\n    };\n    const menuItems = [\n        {\n            section: \"WORKSPACE\",\n            items: [\n                {\n                    href: \"/\",\n                    label: \"Briefing Room\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home\n                },\n                {\n                    href: \"/tweet-center\",\n                    label: \"Drafting Desk\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageCircle\n                },\n                {\n                    href: \"/schedule\",\n                    label: \"Content Scheduler\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Calendar\n                },\n                {\n                    href: \"/dashboard\",\n                    label: \"Growth Lab\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.BarChart3\n                },\n                {\n                    href: \"/meeting\",\n                    label: \"AI Meetings\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Video\n                }\n            ]\n        },\n        {\n            section: \"SETTINGS\",\n            items: [\n                {\n                    href: \"/settings\",\n                    label: \"Settings\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User\n                }\n            ]\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: colors.background,\n            padding: \"16px\",\n            gap: \"16px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    background: colors.sidebar.background,\n                    height: \"calc(100vh - 32px)\",\n                    borderRadius: \"24px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: `0 8px 32px ${colors.sidebar.glow}, 0 2px 8px rgba(255, 107, 53, 0.1)`,\n                    border: `1px solid ${colors.sidebar.border}`,\n                    overflow: \"hidden\",\n                    position: \"relative\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"24px 20px 20px 20px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"10px\",\n                            background: colors.sidebar.background\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"https://nlckamsrdiwkyyrxzntf.supabase.co/storage/v1/object/sign/logos/elogos.png?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InN0b3JhZ2UtdXJsLXNpZ25pbmcta2V5X2RiNTE0YzE5LTlhNTQtNGZiNy1hMjY3LTJmNjY5ZDlhZjY1OCJ9.eyJ1cmwiOiJsb2dvcy9lbG9nb3MucG5nIiwiaWF0IjoxNzQ4Mjk2NDIyLCJleHAiOjE3Nzk4MzI0MjJ9.FJlERvbHYRsm-4XpUyFKY1_xnFV988GB6X9M3vMarjE\",\n                                alt: \"Exie Logo\",\n                                style: {\n                                    height: \"36px\",\n                                    width: \"auto\",\n                                    objectFit: \"contain\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n                                    fontSize: \"19px\",\n                                    fontWeight: \"500\",\n                                    color: \"#FF6B35\",\n                                    letterSpacing: \"-0.4px\",\n                                    lineHeight: \"1\"\n                                },\n                                children: \"Exie\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"0 16px 16px 16px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                display: \"flex\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Search, {\n                                    size: 14,\n                                    color: colors.sidebar.textMuted,\n                                    style: {\n                                        position: \"absolute\",\n                                        left: \"12px\",\n                                        zIndex: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search...\",\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"8px 12px 8px 36px\",\n                                        border: `1px solid ${colors.sidebar.border}`,\n                                        borderRadius: \"8px\",\n                                        background: colors.sidebar.surface,\n                                        color: colors.sidebar.text,\n                                        fontSize: \"12px\",\n                                        fontWeight: \"400\",\n                                        outline: \"none\",\n                                        transition: \"all 0.2s ease\"\n                                    },\n                                    onFocus: (e)=>{\n                                        e.target.style.borderColor = colors.sidebar.accent;\n                                        e.target.style.background = colors.sidebar.background;\n                                    },\n                                    onBlur: (e)=>{\n                                        e.target.style.borderColor = colors.sidebar.border;\n                                        e.target.style.background = colors.sidebar.surface;\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"8px 16px\",\n                            flex: 1,\n                            display: \"flex\",\n                            flexDirection: \"column\"\n                        },\n                        children: menuItems.map((section, sectionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: sectionIndex < menuItems.length - 1 ? \"24px\" : \"0\"\n                                },\n                                children: [\n                                    sectionIndex > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            height: \"1px\",\n                                            background: `linear-gradient(90deg, transparent, ${colors.sidebar.divider}, transparent)`,\n                                            margin: \"16px 12px 20px 12px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"10px\",\n                                            fontWeight: \"600\",\n                                            color: colors.sidebar.textMuted,\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"1px\",\n                                            marginBottom: \"10px\",\n                                            paddingLeft: \"12px\"\n                                        },\n                                        children: section.section\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\",\n                                            gap: \"2px\"\n                                        },\n                                        children: section.items.map((item)=>{\n                                            const active = isActive(item.href);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        padding: \"8px 12px\",\n                                                        borderRadius: \"8px\",\n                                                        background: active ? colors.sidebar.surface : \"transparent\",\n                                                        cursor: \"pointer\",\n                                                        transition: \"all 0.2s ease\",\n                                                        position: \"relative\"\n                                                    },\n                                                    onMouseEnter: (e)=>{\n                                                        if (!active) {\n                                                            e.currentTarget.style.background = colors.sidebar.hover;\n                                                        }\n                                                    },\n                                                    onMouseLeave: (e)=>{\n                                                        if (!active) {\n                                                            e.currentTarget.style.background = \"transparent\";\n                                                        }\n                                                    },\n                                                    children: [\n                                                        active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: \"absolute\",\n                                                                left: \"0\",\n                                                                top: \"50%\",\n                                                                transform: \"translateY(-50%)\",\n                                                                width: \"2px\",\n                                                                height: \"16px\",\n                                                                background: colors.sidebar.accent,\n                                                                borderRadius: \"0 2px 2px 0\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: \"16px\",\n                                                                height: \"16px\",\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                justifyContent: \"center\",\n                                                                marginRight: \"10px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 14,\n                                                                color: active ? colors.sidebar.accent : colors.sidebar.textSecondary,\n                                                                strokeWidth: 2\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                                fontSize: \"13px\",\n                                                                fontWeight: active ? \"500\" : \"400\",\n                                                                letterSpacing: \"0.1px\"\n                                                            },\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, item.href, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, section.section, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            margin: \"0 16px 16px 16px\",\n                            height: \"1px\",\n                            background: `linear-gradient(90deg, transparent, ${colors.sidebar.divider}, transparent)`\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"8px 16px 16px 16px\"\n                        },\n                        children: user && userData ? // Authenticated user - show profile\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"10px\",\n                                    padding: \"10px 12px\",\n                                    background: colors.sidebar.background,\n                                    borderRadius: \"14px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                    border: `1px solid ${colors.sidebar.border}`,\n                                    boxShadow: `0 2px 8px ${colors.sidebar.glow}`\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.surface;\n                                    e.currentTarget.style.borderColor = colors.sidebar.accent + \"40\";\n                                    e.currentTarget.style.transform = \"translateY(-1px)\";\n                                    e.currentTarget.style.boxShadow = `0 4px 16px ${colors.sidebar.glow}`;\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.background;\n                                    e.currentTarget.style.borderColor = colors.sidebar.border;\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                    e.currentTarget.style.boxShadow = `0 2px 8px ${colors.sidebar.glow}`;\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: userData.avatar ? \"transparent\" : `linear-gradient(135deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft})`,\n                                            borderRadius: \"10px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            overflow: \"hidden\",\n                                            border: `2px solid ${colors.sidebar.border}`\n                                        },\n                                        children: [\n                                            userData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userData.avatar,\n                                                alt: userData.name,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"8px\"\n                                                },\n                                                onError: (e)=>{\n                                                    // Fallback to icon if image fails to load\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.parentElement.style.background = `linear-gradient(135deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft})`;\n                                                    e.currentTarget.parentElement.innerHTML = `<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path><circle cx=\"12\" cy=\"7\" r=\"4\"></circle></svg>`;\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 16,\n                                                color: \"#FFFFFF\",\n                                                strokeWidth: 2\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            userData.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"10px\",\n                                                    height: \"10px\",\n                                                    background: \"#00FF88\",\n                                                    borderRadius: \"50%\",\n                                                    border: \"2px solid #FFFFFF\",\n                                                    boxShadow: \"0 0 6px rgba(0, 255, 136, 0.8)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    lineHeight: \"1.2\",\n                                                    marginBottom: \"2px\"\n                                                },\n                                                children: userData.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.textMuted,\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"500\",\n                                                    lineHeight: \"1.2\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            userData.plan,\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: colors.sidebar.accent\n                                                        },\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, undefined) : // Not authenticated - show sign in button\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>setShowAuthModal(true),\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                padding: \"8px 12px\",\n                                borderRadius: \"8px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\",\n                                background: \"transparent\"\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.background = colors.sidebar.hover;\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.background = \"transparent\";\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"16px\",\n                                        height: \"16px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        marginRight: \"10px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogIn, {\n                                        size: 14,\n                                        color: colors.sidebar.textSecondary,\n                                        strokeWidth: 2\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: colors.sidebar.textSecondary,\n                                        fontSize: \"13px\",\n                                        fontWeight: \"400\",\n                                        letterSpacing: \"0.1px\"\n                                    },\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.surface,\n                    borderRadius: \"24px\",\n                    height: \"calc(100vh - 32px)\",\n                    position: \"relative\",\n                    boxShadow: \"0 4px 20px rgba(255, 107, 53, 0.08)\",\n                    border: `1px solid ${colors.sidebar.border}`,\n                    overflow: \"hidden\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"32px\",\n                        height: \"100%\",\n                        overflow: \"auto\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 469,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayoutSimple.tsx\n");

/***/ }),

/***/ "./contexts/UserContext.tsx":
/*!**********************************!*\
  !*** ./contexts/UserContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"./lib/supabase.ts\");\n\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useUser = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n};\nconst UserProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                await fetchUserProfile(session.user.id);\n                await updateOnlineStatus(true);\n            }\n            setLoading(false);\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state change:\", {\n                event,\n                user: !!session?.user,\n                email: session?.user?.email\n            });\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                console.log(\"User authenticated, fetching profile...\");\n                await fetchUserProfile(session.user.id);\n                await updateOnlineStatus(true);\n            } else {\n                console.log(\"User signed out, clearing profile\");\n                setProfile(null);\n            }\n            setLoading(false);\n        });\n        // Update online status when user leaves\n        const handleBeforeUnload = ()=>{\n            if (user) {\n                updateOnlineStatus(false);\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>{\n            subscription.unsubscribe();\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        user\n    ]);\n    const fetchUserProfile = async (userId)=>{\n        try {\n            console.log(\"Fetching user profile for userId:\", userId);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").select(\"*\").eq(\"user_id\", userId).single();\n            console.log(\"Profile fetch result:\", {\n                data,\n                error\n            });\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error fetching user profile:\", error);\n                return;\n            }\n            if (data) {\n                console.log(\"Setting profile data:\", data);\n                setProfile(data);\n            } else {\n                // Create default profile if it doesn't exist\n                console.log(\"Creating new profile for user:\", userId);\n                const { data: newProfile, error: createError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").insert({\n                    user_id: userId,\n                    full_name: user?.user_metadata?.full_name || \"User\",\n                    plan: \"free\",\n                    subscription_status: \"inactive\"\n                }).select().single();\n                console.log(\"Profile creation result:\", {\n                    newProfile,\n                    createError\n                });\n                if (createError) {\n                    console.error(\"Error creating user profile:\", createError);\n                } else {\n                    console.log(\"Setting new profile:\", newProfile);\n                    setProfile(newProfile);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error in fetchUserProfile:\", error);\n        }\n    };\n    const signIn = async (email, password)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            error\n        };\n    };\n    const signUp = async (email, password, fullName)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    full_name: fullName\n                }\n            }\n        });\n        return {\n            error\n        };\n    };\n    const signOut = async ()=>{\n        await updateOnlineStatus(false);\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) return {\n            error: \"No user logged in\"\n        };\n        console.log(\"Updating profile for user:\", user.id, \"with updates:\", updates);\n        try {\n            // First, try to update the profile\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").update(updates).eq(\"user_id\", user.id).select().single();\n            console.log(\"Update result:\", {\n                data,\n                error\n            });\n            if (error) {\n                // If the profile doesn't exist, try to create it first\n                if (error.code === \"PGRST116\") {\n                    console.log(\"Profile not found, creating new profile...\");\n                    const { data: newProfile, error: createError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").insert({\n                        user_id: user.id,\n                        full_name: user?.user_metadata?.full_name || \"User\",\n                        plan: \"free\",\n                        subscription_status: \"inactive\",\n                        ...updates\n                    }).select().single();\n                    if (createError) {\n                        console.error(\"Error creating profile:\", createError);\n                        return {\n                            error: createError\n                        };\n                    } else {\n                        console.log(\"Profile created successfully:\", newProfile);\n                        setProfile(newProfile);\n                        return {\n                            error: null\n                        };\n                    }\n                } else {\n                    console.error(\"Error updating profile:\", error);\n                    return {\n                        error\n                    };\n                }\n            } else {\n                console.log(\"Profile updated successfully:\", data);\n                setProfile(data);\n                return {\n                    error: null\n                };\n            }\n        } catch (error) {\n            console.error(\"Unexpected error in updateProfile:\", error);\n            return {\n                error\n            };\n        }\n    };\n    const updateOnlineStatus = async (isOnline)=>{\n        if (!user) return;\n        try {\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").update({\n                is_online: isOnline,\n                last_seen: new Date().toISOString()\n            }).eq(\"user_id\", user.id);\n        } catch (error) {\n            console.error(\"Error updating online status:\", error);\n        }\n    };\n    const value = {\n        user,\n        profile,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        updateProfile,\n        updateOnlineStatus\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/contexts/UserContext.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/UserContext.tsx\n");

/***/ }),

/***/ "./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nlckamsrdiwkyyrxzntf.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Client-side Supabase client (with RLS)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Server-side Supabase client (bypasses RLS) - only create if service key exists\nconst supabaseAdmin = supabaseServiceKey ? (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/supabase.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n\n // Global styles\n\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__.UserProvider, {\n        children: getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n            lineNumber: 21,\n            columnNumber: 18\n        }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQStCLENBQUMsZ0JBQWdCO0FBSU87QUFVdkQsU0FBU0MsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELHFCQUNFLDhEQUFDTCwrREFBWUE7a0JBQ1ZJLHdCQUFVLDhEQUFDRjtZQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7O0FBR3pDO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLy4vcGFnZXMvX2FwcC50c3g/MmZiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcyc7IC8vIEdsb2JhbCBzdHlsZXNcbmltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgdHlwZSB7IFJlYWN0RWxlbWVudCwgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHR5cGUgeyBOZXh0UGFnZSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgVXNlclByb3ZpZGVyIH0gZnJvbSAnLi4vY29udGV4dHMvVXNlckNvbnRleHQnO1xuXG5leHBvcnQgdHlwZSBOZXh0UGFnZVdpdGhMYXlvdXQgPSBOZXh0UGFnZSAmIHtcbiAgZ2V0TGF5b3V0PzogKHBhZ2U6IFJlYWN0RWxlbWVudCkgPT4gUmVhY3ROb2RlO1xufTtcblxuZXhwb3J0IHR5cGUgQXBwUHJvcHNXaXRoTGF5b3V0ID0gQXBwUHJvcHMgJiB7XG4gIENvbXBvbmVudDogTmV4dFBhZ2VXaXRoTGF5b3V0O1xufTtcblxuZnVuY3Rpb24gTXlBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wc1dpdGhMYXlvdXQpIHtcbiAgLy8gVXNlIHRoZSBsYXlvdXQgZGVmaW5lZCBhdCB0aGUgcGFnZSBsZXZlbCwgaWYgYXZhaWxhYmxlXG4gIGNvbnN0IGdldExheW91dCA9IENvbXBvbmVudC5nZXRMYXlvdXQgfHwgKChwYWdlKSA9PiBwYWdlKTtcblxuICByZXR1cm4gKFxuICAgIDxVc2VyUHJvdmlkZXI+XG4gICAgICB7Z2V0TGF5b3V0KDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz4pfVxuICAgIDwvVXNlclByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZGVmYXVsdCBNeUFwcDsiXSwibmFtZXMiOlsiVXNlclByb3ZpZGVyIiwiTXlBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJnZXRMYXlvdXQiLCJwYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!lucide-react */ \"__barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_AgentEChatSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/AgentEChatSimple */ \"./components/AgentEChatSimple.tsx\");\n// pages/tweet-center.tsx\n\n\n\n\n\nconst TweetCenterPage = ()=>{\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAgentE, setShowAgentE] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"single\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        borderHover: \"#E8D5C4\",\n        surface: \"#FFFFFF\",\n        surfaceHover: \"#F9F7F4\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Intelligent AI prediction based on content context\n    const generateContextualSuggestion = (text)=>{\n        const lowerText = text.toLowerCase();\n        // Trading/Finance context\n        if (lowerText.includes(\"trading\") || lowerText.includes(\"stocks\") || lowerText.includes(\"crypto\")) {\n            return \" - risk management is everything. Never trade with money you can't afford to lose.\";\n        }\n        // AI/Tech context\n        if (lowerText.includes(\"ai\") || lowerText.includes(\"artificial intelligence\") || lowerText.includes(\"machine learning\")) {\n            return \" is transforming how we work. The key is learning to collaborate with AI, not compete against it.\";\n        }\n        // Productivity context\n        if (lowerText.includes(\"productivity\") || lowerText.includes(\"workflow\") || lowerText.includes(\"efficiency\")) {\n            return \": 1) Single-task focus 2) Time blocking 3) Automate repetitive work. Small changes, big results.\";\n        }\n        // Building/Entrepreneurship context\n        if (lowerText.includes(\"building\") || lowerText.includes(\"startup\") || lowerText.includes(\"business\")) {\n            return \" in public. Share your journey, failures, and wins. Your audience wants authenticity, not perfection.\";\n        }\n        // Learning/Growth context\n        if (lowerText.includes(\"learning\") || lowerText.includes(\"skill\") || lowerText.includes(\"growth\")) {\n            return \" - the best investment you can make is in yourself. Consistency beats intensity every time.\";\n        }\n        // Default contextual suggestions\n        const endings = [\n            \" - here's what I learned from 5 years of experience.\",\n            \". The biggest mistake I see people make is...\",\n            \". Here are 3 things that changed everything for me:\",\n            \" - and it completely shifted my perspective.\",\n            \". If I started over today, I'd focus on this first.\"\n        ];\n        return endings[Math.floor(Math.random() * endings.length)];\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    const autoFormat = ()=>{\n        if (content.length > 280) {\n            // Convert to thread format\n            const sentences = content.split(\". \");\n            const threads = [];\n            let currentThread = \"\";\n            sentences.forEach((sentence)=>{\n                if ((currentThread + sentence + \". \").length <= 280) {\n                    currentThread += sentence + \". \";\n                } else {\n                    if (currentThread) threads.push(currentThread.trim());\n                    currentThread = sentence + \". \";\n                }\n            });\n            if (currentThread) threads.push(currentThread.trim());\n            setContent(threads.join(\"\\n\\n\"));\n            setFormatMode(\"thread\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.paper,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"800px\",\n                    marginBottom: \"40px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"12px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            gap: \"16px\",\n                            marginTop: \"24px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"6px 12px\",\n                                    background: aiEnabled ? `${colors.primary}15` : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: `1px solid ${aiEnabled ? colors.primary : colors.border}`,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onClick: ()=>setAiEnabled(!aiEnabled),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sparkles, {\n                                        size: 14,\n                                        color: aiEnabled ? colors.primary : colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: aiEnabled ? colors.primary : colors.text.tertiary\n                                        },\n                                        children: \"AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: autoFormat,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: colors.surface,\n                                    border: `1px solid ${colors.border}`,\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"500\",\n                                    color: colors.text.secondary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Type, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Auto Format\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: formatMode === \"thread\" ? `${colors.primary}15` : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: `1px solid ${formatMode === \"thread\" ? colors.primary : colors.border}`\n                                },\n                                children: [\n                                    formatMode === \"thread\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.AlignLeft, {\n                                        size: 14,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 40\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.AlignCenter, {\n                                        size: 14,\n                                        color: colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 89\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: formatMode === \"thread\" ? colors.primary : colors.text.tertiary\n                                        },\n                                        children: formatMode === \"thread\" ? \"Thread\" : \"Single\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAgentE(true),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                    border: \"none\",\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"600\",\n                                    color: \"white\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    boxShadow: `0 2px 8px ${colors.primary}30`\n                                },\n                                onMouseEnter: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(-1px)\";\n                                    target.style.boxShadow = `0 4px 12px ${colors.primary}40`;\n                                },\n                                onMouseLeave: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(0)\";\n                                    target.style.boxShadow = `0 2px 8px ${colors.primary}30`;\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Agent E\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    background: \"transparent\",\n                    position: \"relative\",\n                    minHeight: \"500px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            position: \"relative\",\n                            padding: \"40px 60px\",\n                            minHeight: \"450px\"\n                        },\n                        children: [\n                            showSuggestion && aiEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"40px\",\n                                    left: \"60px\",\n                                    right: \"60px\",\n                                    bottom: \"40px\",\n                                    pointerEvents: \"none\",\n                                    zIndex: 1,\n                                    overflow: \"hidden\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sf-pro\",\n                                    style: {\n                                        fontSize: \"20px\",\n                                        lineHeight: \"1.7\",\n                                        color: \"transparent\",\n                                        whiteSpace: \"pre-wrap\",\n                                        wordWrap: \"break-word\",\n                                        letterSpacing: \"0.3px\",\n                                        fontWeight: \"400\"\n                                    },\n                                    children: [\n                                        content,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#9CA3AF\",\n                                                opacity: 0.6,\n                                                fontStyle: \"normal\"\n                                            },\n                                            children: aiSuggestion\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: content,\n                                onChange: (e)=>setContent(e.target.value),\n                                onKeyDown: handleTabPress,\n                                placeholder: aiEnabled ? \"Start writing and I'll help you continue...\" : \"What's on your mind?\",\n                                className: \"sf-pro\",\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    minHeight: \"400px\",\n                                    padding: \"0\",\n                                    border: \"none\",\n                                    background: \"transparent\",\n                                    fontSize: \"20px\",\n                                    lineHeight: \"1.7\",\n                                    color: colors.text.primary,\n                                    resize: \"none\",\n                                    outline: \"none\",\n                                    letterSpacing: \"0.3px\",\n                                    position: \"relative\",\n                                    zIndex: 2,\n                                    fontWeight: \"400\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            padding: \"0 60px 40px\",\n                            marginTop: \"20px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sf-pro\",\n                                style: {\n                                    fontSize: \"14px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"400\",\n                                    opacity: 0.7\n                                },\n                                children: [\n                                    content.length,\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setContent(\"\"),\n                                        className: \"sf-pro\",\n                                        style: {\n                                            padding: \"12px 24px\",\n                                            background: \"transparent\",\n                                            border: `1px solid ${colors.border}`,\n                                            borderRadius: \"10px\",\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = colors.surfaceHover;\n                                            target.style.borderColor = colors.borderHover;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = \"transparent\";\n                                            target.style.borderColor = colors.border;\n                                        },\n                                        children: \"Save Draft\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // Publish logic here\n                                            console.log(\"Publishing:\", content);\n                                        },\n                                        className: \"sf-pro\",\n                                        style: {\n                                            padding: \"12px 28px\",\n                                            background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                            border: \"none\",\n                                            borderRadius: \"10px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            boxShadow: `0 4px 12px ${colors.primary}30`,\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(-1px)\";\n                                            target.style.boxShadow = `0 6px 16px ${colors.primary}40`;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(0)\";\n                                            target.style.boxShadow = `0 4px 12px ${colors.primary}30`;\n                                        },\n                                        children: \"Publish\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentEChatSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAgentE,\n                onClose: ()=>setShowAgentE(false),\n                currentContent: content\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, undefined);\n};\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 430,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TweetCenterPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-jsx/style");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftweet-center&preferredRegion=&absolutePagePath=.%2Fpages%2Ftweet-center.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();