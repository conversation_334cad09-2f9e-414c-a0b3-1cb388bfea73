/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Home: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   LogIn: () => (/* reexport safe */ _icons_log_in_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   MessageCircle: () => (/* reexport safe */ _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Video: () => (/* reexport safe */ _icons_video_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/house.js */ \"./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _icons_log_in_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/log-in.js */ \"./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/message-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_video_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/video.js */ \"./node_modules/lucide-react/dist/esm/icons/video.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsSG9tZSxMb2dJbixNZXNzYWdlQ2lyY2xlLFVzZXIsVmlkZW8hPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNkQ7QUFDWDtBQUNFO0FBQ2dCO0FBQ25CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2M2NmQiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJhckNoYXJ0MyB9IGZyb20gXCIuL2ljb25zL2Jhci1jaGFydC0zLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSG9tZSB9IGZyb20gXCIuL2ljb25zL2hvdXNlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9nSW4gfSBmcm9tIFwiLi9pY29ucy9sb2ctaW4uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNZXNzYWdlQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvbWVzc2FnZS1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyIH0gZnJvbSBcIi4vaWNvbnMvdXNlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFZpZGVvIH0gZnJvbSBcIi4vaWNvbnMvdmlkZW8uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!********************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Eye: () => (/* reexport safe */ _icons_eye_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   EyeOff: () => (/* reexport safe */ _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Lock: () => (/* reexport safe */ _icons_lock_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Mail: () => (/* reexport safe */ _icons_mail_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_eye_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/eye.js */ \"./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/eye-off.js */ \"./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _icons_lock_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/lock.js */ \"./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _icons_mail_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/mail.js */ \"./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FeWUsRXllT2ZmLExvY2ssTWFpbCxVc2VyLFghPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDK0M7QUFDTztBQUNMO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2V4aWUtYWkvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9lMzY0Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFeWUgfSBmcm9tIFwiLi9pY29ucy9leWUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFeWVPZmYgfSBmcm9tIFwiLi9pY29ucy9leWUtb2ZmLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9jayB9IGZyb20gXCIuL2ljb25zL2xvY2suanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNYWlsIH0gZnJvbSBcIi4vaWNvbnMvbWFpbC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXIgfSBmcm9tIFwiLi9pY29ucy91c2VyLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgWCB9IGZyb20gXCIuL2ljb25zL3guanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/index.tsx */ \"./pages/index.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/AuthModal.tsx":
/*!**********************************!*\
  !*** ./components/AuthModal.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!lucide-react */ \"__barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\n\n\n\nconst AuthModal = ({ isOpen, onClose })=>{\n    const [isSignUp, setIsSignUp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { signIn, signUp } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        surface: \"#FFFFFF\",\n        background: \"#F5F1EB\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\"\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        try {\n            let result;\n            if (isSignUp) {\n                result = await signUp(email, password, fullName);\n            } else {\n                result = await signIn(email, password);\n            }\n            if (result.error) {\n                setError(result.error.message);\n            } else {\n                onClose();\n                // Reset form\n                setEmail(\"\");\n                setPassword(\"\");\n                setFullName(\"\");\n            }\n        } catch (err) {\n            setError(\"An unexpected error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"fixed\",\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            zIndex: 1000\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: colors.surface,\n                borderRadius: \"16px\",\n                padding: \"32px\",\n                width: \"100%\",\n                maxWidth: \"400px\",\n                margin: \"20px\",\n                boxShadow: \"0 20px 40px rgba(0, 0, 0, 0.1)\",\n                position: \"relative\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    style: {\n                        position: \"absolute\",\n                        top: \"16px\",\n                        right: \"16px\",\n                        background: \"none\",\n                        border: \"none\",\n                        cursor: \"pointer\",\n                        padding: \"8px\",\n                        borderRadius: \"8px\",\n                        color: colors.text.secondary\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: \"center\",\n                        marginBottom: \"32px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"32px\",\n                                color: colors.primary,\n                                fontFamily: \"Georgia, serif\",\n                                fontStyle: \"italic\",\n                                marginBottom: \"8px\"\n                            },\n                            children: \"ℰ\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: colors.text.primary,\n                                fontSize: \"24px\",\n                                fontWeight: \"600\",\n                                margin: 0,\n                                marginBottom: \"8px\"\n                            },\n                            children: isSignUp ? \"Create Account\" : \"Welcome Back\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: colors.text.secondary,\n                                fontSize: \"14px\",\n                                margin: 0\n                            },\n                            children: isSignUp ? \"Join Exie to get started\" : \"Sign in to your account\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    children: [\n                        isSignUp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        color: colors.text.primary,\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: \"Full Name\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.User, {\n                                            size: 16,\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                color: colors.text.tertiary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fullName,\n                                            onChange: (e)=>setFullName(e.target.value),\n                                            required: isSignUp,\n                                            style: {\n                                                width: \"100%\",\n                                                padding: \"12px 16px 12px 40px\",\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                boxSizing: \"border-box\"\n                                            },\n                                            placeholder: \"Enter your full name\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        color: colors.text.primary,\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Mail, {\n                                            size: 16,\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                color: colors.text.tertiary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            required: true,\n                                            style: {\n                                                width: \"100%\",\n                                                padding: \"12px 16px 12px 40px\",\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                boxSizing: \"border-box\"\n                                            },\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"24px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        color: colors.text.primary,\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: \"Password\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Lock, {\n                                            size: 16,\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                color: colors.text.tertiary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value),\n                                            required: true,\n                                            style: {\n                                                width: \"100%\",\n                                                padding: \"12px 40px 12px 40px\",\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                boxSizing: \"border-box\"\n                                            },\n                                            placeholder: \"Enter your password\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            style: {\n                                                position: \"absolute\",\n                                                right: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                background: \"none\",\n                                                border: \"none\",\n                                                cursor: \"pointer\",\n                                                color: colors.text.tertiary\n                                            },\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.EyeOff, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 33\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Eye, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 56\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, undefined),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: \"#DC2626\",\n                                fontSize: \"14px\",\n                                marginBottom: \"20px\",\n                                padding: \"12px\",\n                                backgroundColor: \"#FEF2F2\",\n                                border: \"1px solid #FECACA\",\n                                borderRadius: \"8px\"\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            style: {\n                                width: \"100%\",\n                                padding: \"12px\",\n                                background: loading ? colors.text.tertiary : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                color: \"white\",\n                                border: \"none\",\n                                borderRadius: \"8px\",\n                                fontSize: \"14px\",\n                                fontWeight: \"600\",\n                                cursor: loading ? \"not-allowed\" : \"pointer\",\n                                boxShadow: loading ? \"none\" : `0 4px 12px ${colors.primary}30`,\n                                marginBottom: \"20px\"\n                            },\n                            children: loading ? \"Please wait...\" : isSignUp ? \"Create Account\" : \"Sign In\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: \"center\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setIsSignUp(!isSignUp),\n                                style: {\n                                    background: \"none\",\n                                    border: \"none\",\n                                    color: colors.primary,\n                                    fontSize: \"14px\",\n                                    cursor: \"pointer\",\n                                    textDecoration: \"underline\"\n                                },\n                                children: isSignUp ? \"Already have an account? Sign in\" : \"Don't have an account? Sign up\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AuthModal.tsx\n");

/***/ }),

/***/ "./components/SidebarLayoutSimple.tsx":
/*!********************************************!*\
  !*** ./components/SidebarLayoutSimple.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthModal */ \"./components/AuthModal.tsx\");\n\n\n\n\n\n\n\nconst SidebarLayout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, profile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Fallback user data for when not authenticated or loading\n    const userData = {\n        name: profile?.full_name || user?.user_metadata?.full_name || \"Alex Chen\",\n        email: user?.email || \"<EMAIL>\",\n        plan: profile?.plan === \"pro\" ? \"Pro\" : profile?.plan === \"enterprise\" ? \"Enterprise\" : \"Free\",\n        avatar: profile?.avatar_url || null,\n        isOnline: profile?.is_online || false\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        background: \"#F5F1EB\",\n        surface: \"#FFFFFF\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            surface: \"rgba(255, 107, 53, 0.05)\",\n            text: \"#2D1B14\",\n            textSecondary: \"#5D4037\",\n            textMuted: \"#8D6E63\",\n            accent: \"#FF6B35\",\n            accentSoft: \"#FF8A65\",\n            accentLight: \"#FFF7F4\",\n            border: \"rgba(255, 107, 53, 0.15)\",\n            hover: \"rgba(255, 107, 53, 0.08)\",\n            divider: \"rgba(255, 107, 53, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.25)\" // Orange glow\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageCircle\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.BarChart3\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Video\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: colors.background,\n            padding: \"16px\",\n            gap: \"16px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"180px\",\n                    background: colors.sidebar.background,\n                    height: \"calc(100vh - 32px)\",\n                    borderRadius: \"24px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: `0 8px 32px ${colors.sidebar.glow}, 0 2px 8px rgba(255, 107, 53, 0.1)`,\n                    border: `1px solid ${colors.sidebar.border}`,\n                    overflow: \"hidden\",\n                    position: \"relative\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            height: \"2px\",\n                            background: `linear-gradient(90deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft}, ${colors.sidebar.accent})`\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"20px 16px 16px 16px\",\n                            textAlign: \"center\",\n                            background: colors.sidebar.accentLight\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"32px\",\n                                    color: colors.sidebar.accent,\n                                    fontFamily: \"Dancing Script, cursive\",\n                                    fontWeight: \"600\",\n                                    letterSpacing: \"1px\",\n                                    textShadow: `0 2px 4px ${colors.sidebar.glow}`,\n                                    marginBottom: \"4px\"\n                                },\n                                children: \"Exie\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"10px\",\n                                    color: colors.sidebar.textMuted,\n                                    fontWeight: \"500\",\n                                    letterSpacing: \"1px\",\n                                    textTransform: \"uppercase\"\n                                },\n                                children: \"AI Assistant\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"16px 12px\",\n                            flex: 1,\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"4px\"\n                        },\n                        children: menuItems.map((item, index)=>{\n                            const active = isActive(item.href);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"12px 16px\",\n                                                borderRadius: \"16px\",\n                                                background: active ? colors.sidebar.surface : \"transparent\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                position: \"relative\",\n                                                border: active ? `1px solid ${colors.sidebar.border}` : \"1px solid transparent\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                if (!active) {\n                                                    e.currentTarget.style.background = colors.sidebar.hover;\n                                                    e.currentTarget.style.transform = \"translateX(2px)\";\n                                                    e.currentTarget.style.borderColor = colors.sidebar.border;\n                                                }\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                if (!active) {\n                                                    e.currentTarget.style.background = \"transparent\";\n                                                    e.currentTarget.style.transform = \"translateX(0)\";\n                                                    e.currentTarget.style.borderColor = \"transparent\";\n                                                }\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"0\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"3px\",\n                                                        height: \"20px\",\n                                                        background: `linear-gradient(180deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft})`,\n                                                        borderRadius: \"0 3px 3px 0\",\n                                                        boxShadow: `0 0 8px ${colors.sidebar.glow}`\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"20px\",\n                                                        height: \"20px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"12px\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        size: 16,\n                                                        color: active ? colors.sidebar.accent : colors.sidebar.textSecondary,\n                                                        strokeWidth: active ? 2.5 : 2\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                        fontSize: \"14px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        letterSpacing: \"0.2px\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    index < menuItems.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            alignItems: \"center\",\n                                            margin: \"8px 0\",\n                                            height: \"1px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"3px\"\n                                            },\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"4px\",\n                                                        height: \"1px\",\n                                                        background: colors.sidebar.divider,\n                                                        borderRadius: \"1px\"\n                                                    }\n                                                }, i, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, item.href, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            margin: \"0 16px 16px 16px\",\n                            height: \"1px\",\n                            background: `linear-gradient(90deg, transparent, ${colors.sidebar.divider}, transparent)`\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"12px 12px 16px 12px\",\n                            background: colors.sidebar.accentLight\n                        },\n                        children: user ? // Authenticated user - show profile\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"10px\",\n                                    padding: \"10px 12px\",\n                                    background: colors.sidebar.background,\n                                    borderRadius: \"14px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                    border: `1px solid ${colors.sidebar.border}`,\n                                    boxShadow: `0 2px 8px ${colors.sidebar.glow}`\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.surface;\n                                    e.currentTarget.style.borderColor = colors.sidebar.accent + \"40\";\n                                    e.currentTarget.style.transform = \"translateY(-1px)\";\n                                    e.currentTarget.style.boxShadow = `0 4px 16px ${colors.sidebar.glow}`;\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.background;\n                                    e.currentTarget.style.borderColor = colors.sidebar.border;\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                    e.currentTarget.style.boxShadow = `0 2px 8px ${colors.sidebar.glow}`;\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: userData.avatar ? \"transparent\" : `linear-gradient(135deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft})`,\n                                            borderRadius: \"10px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            overflow: \"hidden\",\n                                            border: `2px solid ${colors.sidebar.border}`\n                                        },\n                                        children: [\n                                            userData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userData.avatar,\n                                                alt: userData.name,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"8px\"\n                                                },\n                                                onError: (e)=>{\n                                                    // Fallback to icon if image fails to load\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.parentElement.style.background = `linear-gradient(135deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft})`;\n                                                    e.currentTarget.parentElement.innerHTML = `<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path><circle cx=\"12\" cy=\"7\" r=\"4\"></circle></svg>`;\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 16,\n                                                color: \"#FFFFFF\",\n                                                strokeWidth: 2\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            userData.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"10px\",\n                                                    height: \"10px\",\n                                                    background: \"#00FF88\",\n                                                    borderRadius: \"50%\",\n                                                    border: \"2px solid #FFFFFF\",\n                                                    boxShadow: \"0 0 6px rgba(0, 255, 136, 0.8)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    lineHeight: \"1.2\",\n                                                    marginBottom: \"2px\"\n                                                },\n                                                children: userData.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.textMuted,\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"500\",\n                                                    lineHeight: \"1.2\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            userData.plan,\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: colors.sidebar.accent\n                                                        },\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, undefined) : // Not authenticated - show sign in button\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>setShowAuthModal(true),\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"10px\",\n                                padding: \"10px 12px\",\n                                background: colors.sidebar.background,\n                                borderRadius: \"14px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                border: `1px solid ${colors.sidebar.border}`,\n                                boxShadow: `0 2px 8px ${colors.sidebar.glow}`\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.background = colors.sidebar.surface;\n                                e.currentTarget.style.borderColor = colors.sidebar.accent + \"40\";\n                                e.currentTarget.style.transform = \"translateY(-1px)\";\n                                e.currentTarget.style.boxShadow = `0 4px 16px ${colors.sidebar.glow}`;\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.background = colors.sidebar.background;\n                                e.currentTarget.style.borderColor = colors.sidebar.border;\n                                e.currentTarget.style.transform = \"translateY(0)\";\n                                e.currentTarget.style.boxShadow = `0 2px 8px ${colors.sidebar.glow}`;\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"32px\",\n                                        height: \"32px\",\n                                        background: `linear-gradient(135deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft})`,\n                                        borderRadius: \"10px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        border: `2px solid ${colors.sidebar.border}`\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogIn, {\n                                        size: 16,\n                                        color: \"#FFFFFF\",\n                                        strokeWidth: 2\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"13px\",\n                                                fontWeight: \"600\",\n                                                lineHeight: \"1.2\",\n                                                marginBottom: \"2px\"\n                                            },\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.textMuted,\n                                                fontSize: \"11px\",\n                                                fontWeight: \"500\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Access your account\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.surface,\n                    borderRadius: \"24px\",\n                    height: \"calc(100vh - 32px)\",\n                    position: \"relative\",\n                    boxShadow: \"0 4px 20px rgba(255, 107, 53, 0.08)\",\n                    border: `1px solid ${colors.sidebar.border}`,\n                    overflow: \"hidden\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"32px\",\n                        height: \"100%\",\n                        overflow: \"auto\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 417,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 437,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayoutSimple.tsx\n");

/***/ }),

/***/ "./contexts/UserContext.tsx":
/*!**********************************!*\
  !*** ./contexts/UserContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"./lib/supabase.ts\");\n\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useUser = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n};\nconst UserProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                await fetchUserProfile(session.user.id);\n                await updateOnlineStatus(true);\n            }\n            setLoading(false);\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                await fetchUserProfile(session.user.id);\n                await updateOnlineStatus(true);\n            } else {\n                setProfile(null);\n            }\n            setLoading(false);\n        });\n        // Update online status when user leaves\n        const handleBeforeUnload = ()=>{\n            if (user) {\n                updateOnlineStatus(false);\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>{\n            subscription.unsubscribe();\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        user\n    ]);\n    const fetchUserProfile = async (userId)=>{\n        try {\n            console.log(\"Fetching user profile for userId:\", userId);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").select(\"*\").eq(\"user_id\", userId).single();\n            console.log(\"Profile fetch result:\", {\n                data,\n                error\n            });\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error fetching user profile:\", error);\n                return;\n            }\n            if (data) {\n                console.log(\"Setting profile data:\", data);\n                setProfile(data);\n            } else {\n                // Create default profile if it doesn't exist\n                console.log(\"Creating new profile for user:\", userId);\n                const { data: newProfile, error: createError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").insert({\n                    user_id: userId,\n                    full_name: user?.user_metadata?.full_name || \"User\",\n                    plan: \"free\",\n                    subscription_status: \"inactive\"\n                }).select().single();\n                console.log(\"Profile creation result:\", {\n                    newProfile,\n                    createError\n                });\n                if (createError) {\n                    console.error(\"Error creating user profile:\", createError);\n                } else {\n                    console.log(\"Setting new profile:\", newProfile);\n                    setProfile(newProfile);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error in fetchUserProfile:\", error);\n        }\n    };\n    const signIn = async (email, password)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            error\n        };\n    };\n    const signUp = async (email, password, fullName)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    full_name: fullName\n                }\n            }\n        });\n        return {\n            error\n        };\n    };\n    const signOut = async ()=>{\n        await updateOnlineStatus(false);\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) return {\n            error: \"No user logged in\"\n        };\n        console.log(\"Updating profile for user:\", user.id, \"with updates:\", updates);\n        try {\n            // First, try to update the profile\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").update(updates).eq(\"user_id\", user.id).select().single();\n            console.log(\"Update result:\", {\n                data,\n                error\n            });\n            if (error) {\n                // If the profile doesn't exist, try to create it first\n                if (error.code === \"PGRST116\") {\n                    console.log(\"Profile not found, creating new profile...\");\n                    const { data: newProfile, error: createError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").insert({\n                        user_id: user.id,\n                        full_name: user?.user_metadata?.full_name || \"User\",\n                        plan: \"free\",\n                        subscription_status: \"inactive\",\n                        ...updates\n                    }).select().single();\n                    if (createError) {\n                        console.error(\"Error creating profile:\", createError);\n                        return {\n                            error: createError\n                        };\n                    } else {\n                        console.log(\"Profile created successfully:\", newProfile);\n                        setProfile(newProfile);\n                        return {\n                            error: null\n                        };\n                    }\n                } else {\n                    console.error(\"Error updating profile:\", error);\n                    return {\n                        error\n                    };\n                }\n            } else {\n                console.log(\"Profile updated successfully:\", data);\n                setProfile(data);\n                return {\n                    error: null\n                };\n            }\n        } catch (error) {\n            console.error(\"Unexpected error in updateProfile:\", error);\n            return {\n                error\n            };\n        }\n    };\n    const updateOnlineStatus = async (isOnline)=>{\n        if (!user) return;\n        try {\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").update({\n                is_online: isOnline,\n                last_seen: new Date().toISOString()\n            }).eq(\"user_id\", user.id);\n        } catch (error) {\n            console.error(\"Error updating online status:\", error);\n        }\n    };\n    const value = {\n        user,\n        profile,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        updateProfile,\n        updateOnlineStatus\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/contexts/UserContext.tsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/UserContext.tsx\n");

/***/ }),

/***/ "./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nlckamsrdiwkyyrxzntf.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/supabase.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n\n // Global styles\n\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__.UserProvider, {\n        children: getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n            lineNumber: 21,\n            columnNumber: 18\n        }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQStCLENBQUMsZ0JBQWdCO0FBSU87QUFVdkQsU0FBU0MsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELHFCQUNFLDhEQUFDTCwrREFBWUE7a0JBQ1ZJLHdCQUFVLDhEQUFDRjtZQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7O0FBR3pDO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLy4vcGFnZXMvX2FwcC50c3g/MmZiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcyc7IC8vIEdsb2JhbCBzdHlsZXNcbmltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgdHlwZSB7IFJlYWN0RWxlbWVudCwgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHR5cGUgeyBOZXh0UGFnZSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgVXNlclByb3ZpZGVyIH0gZnJvbSAnLi4vY29udGV4dHMvVXNlckNvbnRleHQnO1xuXG5leHBvcnQgdHlwZSBOZXh0UGFnZVdpdGhMYXlvdXQgPSBOZXh0UGFnZSAmIHtcbiAgZ2V0TGF5b3V0PzogKHBhZ2U6IFJlYWN0RWxlbWVudCkgPT4gUmVhY3ROb2RlO1xufTtcblxuZXhwb3J0IHR5cGUgQXBwUHJvcHNXaXRoTGF5b3V0ID0gQXBwUHJvcHMgJiB7XG4gIENvbXBvbmVudDogTmV4dFBhZ2VXaXRoTGF5b3V0O1xufTtcblxuZnVuY3Rpb24gTXlBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wc1dpdGhMYXlvdXQpIHtcbiAgLy8gVXNlIHRoZSBsYXlvdXQgZGVmaW5lZCBhdCB0aGUgcGFnZSBsZXZlbCwgaWYgYXZhaWxhYmxlXG4gIGNvbnN0IGdldExheW91dCA9IENvbXBvbmVudC5nZXRMYXlvdXQgfHwgKChwYWdlKSA9PiBwYWdlKTtcblxuICByZXR1cm4gKFxuICAgIDxVc2VyUHJvdmlkZXI+XG4gICAgICB7Z2V0TGF5b3V0KDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz4pfVxuICAgIDwvVXNlclByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZGVmYXVsdCBNeUFwcDsiXSwibmFtZXMiOlsiVXNlclByb3ZpZGVyIiwiTXlBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJnZXRMYXlvdXQiLCJwYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n// pages/index.tsx\n\n\n\n\nconst HomePage = ()=>{\n    const colors = {\n        primary: \"#F97316\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\"\n        },\n        border: \"#E5E7EB\",\n        surface: \"#FFFFFF\",\n        background: \"#F8F9FA\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"32px\",\n            height: \"100vh\",\n            overflow: \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"32px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.5px\",\n                            marginBottom: \"8px\"\n                        },\n                        children: \"Briefing Room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            fontSize: \"16px\",\n                            margin: 0,\n                            fontWeight: \"400\"\n                        },\n                        children: \"Your daily mission control center\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: \"800px\",\n                    margin: \"0 auto\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            marginBottom: \"24px\",\n                            boxShadow: `0 4px 16px rgba(0, 0, 0, 0.04)`,\n                            border: `1px solid ${colors.border}`\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"16px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        marginBottom: \"12px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                color: colors.text.primary,\n                                                margin: 0,\n                                                fontSize: \"20px\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"Today's Mission\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"4px 8px\",\n                                                background: `${colors.primary}15`,\n                                                borderRadius: \"6px\",\n                                                color: colors.primary,\n                                                fontSize: \"11px\",\n                                                fontWeight: \"600\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.5px\"\n                                            },\n                                            children: \"AI Generated\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: colors.text.secondary,\n                                        fontSize: \"16px\",\n                                        lineHeight: \"1.5\",\n                                        margin: 0,\n                                        marginBottom: \"20px\"\n                                    },\n                                    children: \"Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"grid\",\n                                        gridTemplateColumns: \"repeat(3, 1fr)\",\n                                        gap: \"16px\",\n                                        marginBottom: \"20px\"\n                                    },\n                                    children: [\n                                        {\n                                            label: \"Engagement Rate\",\n                                            value: \"+24%\"\n                                        },\n                                        {\n                                            label: \"New Followers\",\n                                            value: \"127\"\n                                        },\n                                        {\n                                            label: \"Content Score\",\n                                            value: \"8.9/10\"\n                                        }\n                                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: `${colors.primary}08`,\n                                                borderRadius: \"12px\",\n                                                padding: \"16px\",\n                                                textAlign: \"center\",\n                                                border: `1px solid ${colors.primary}15`\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"20px\",\n                                                        fontWeight: \"700\",\n                                                        color: colors.text.primary,\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"13px\",\n                                                        color: colors.text.tertiary,\n                                                        fontWeight: \"500\"\n                                                    },\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        gap: \"12px\",\n                                        flexWrap: \"wrap\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/meeting\",\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"12px 20px\",\n                                                    background: colors.primary,\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Join Call\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                padding: \"12px 20px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\"\n                                            },\n                                            children: \"Ask Mentor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/tweet-center\",\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"12px 20px\",\n                                                    background: colors.surface,\n                                                    color: colors.text.primary,\n                                                    border: `1px solid ${colors.border}`,\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Create Content\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            boxShadow: `0 4px 16px rgba(0, 0, 0, 0.04)`,\n                            border: `1px solid ${colors.border}`\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"flex-start\",\n                                gap: \"16px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"48px\",\n                                        height: \"48px\",\n                                        borderRadius: \"12px\",\n                                        background: `${colors.primary}15`,\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\",\n                                        flexShrink: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"24px\",\n                                                height: \"24px\",\n                                                borderRadius: \"6px\",\n                                                background: colors.primary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"2px\",\n                                                right: \"2px\",\n                                                width: \"12px\",\n                                                height: \"12px\",\n                                                borderRadius: \"50%\",\n                                                background: \"#00E676\",\n                                                border: \"2px solid white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: colors.text.primary,\n                                                margin: 0,\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                marginBottom: \"8px\"\n                                            },\n                                            children: \"AI Mentor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: colors.text.secondary,\n                                                margin: 0,\n                                                fontSize: \"15px\",\n                                                lineHeight: \"1.5\"\n                                            },\n                                            children: \"Ready to help you create content that resonates. What's on your mind today?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\nHomePage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();