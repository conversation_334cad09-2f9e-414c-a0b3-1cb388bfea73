/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/settings";
exports.ids = ["pages/settings"];
exports.modules = {

/***/ "__barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Calendar: () => (/* reexport safe */ _icons_calendar_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Home: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   LogIn: () => (/* reexport safe */ _icons_log_in_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   MessageCircle: () => (/* reexport safe */ _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Search: () => (/* reexport safe */ _icons_search_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Video: () => (/* reexport safe */ _icons_video_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_calendar_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/calendar.js */ \"./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/house.js */ \"./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _icons_log_in_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/log-in.js */ \"./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/message-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _icons_search_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/search.js */ \"./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_video_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/video.js */ \"./node_modules/lucide-react/dist/esm/icons/video.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ2FsZW5kYXIsSG9tZSxMb2dJbixNZXNzYWdlQ2lyY2xlLFNlYXJjaCxVc2VyLFZpZGVvIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM2RDtBQUNKO0FBQ1A7QUFDRTtBQUNnQjtBQUNmO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/ZjIwNiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQmFyQ2hhcnQzIH0gZnJvbSBcIi4vaWNvbnMvYmFyLWNoYXJ0LTMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYWxlbmRhciB9IGZyb20gXCIuL2ljb25zL2NhbGVuZGFyLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSG9tZSB9IGZyb20gXCIuL2ljb25zL2hvdXNlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9nSW4gfSBmcm9tIFwiLi9pY29ucy9sb2ctaW4uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNZXNzYWdlQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvbWVzc2FnZS1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZWFyY2ggfSBmcm9tIFwiLi9pY29ucy9zZWFyY2guanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyIH0gZnJvbSBcIi4vaWNvbnMvdXNlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFZpZGVvIH0gZnJvbSBcIi4vaWNvbnMvdmlkZW8uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bell: () => (/* reexport safe */ _icons_bell_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Bot: () => (/* reexport safe */ _icons_bot_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Crown: () => (/* reexport safe */ _icons_crown_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Plus: () => (/* reexport safe */ _icons_plus_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Save: () => (/* reexport safe */ _icons_save_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Shield: () => (/* reexport safe */ _icons_shield_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Trash2: () => (/* reexport safe */ _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Twitter: () => (/* reexport safe */ _icons_twitter_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   Zap: () => (/* reexport safe */ _icons_zap_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bell_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bell.js */ \"./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _icons_bot_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/bot.js */ \"./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _icons_crown_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/crown.js */ \"./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _icons_plus_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/plus.js */ \"./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _icons_save_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/save.js */ \"./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _icons_shield_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/shield.js */ \"./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/trash-2.js */ \"./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _icons_twitter_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/twitter.js */ \"./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_zap_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/zap.js */ \"./node_modules/lucide-react/dist/esm/icons/zap.js\");\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CZWxsLEJvdCxDcm93bixQbHVzLFNhdmUsU2hpZWxkLFRyYXNoMixUd2l0dGVyLFVzZXIsWmFwIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDaUQ7QUFDRjtBQUNJO0FBQ0Y7QUFDQTtBQUNJO0FBQ0M7QUFDQztBQUNOIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzNiZWEiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJlbGwgfSBmcm9tIFwiLi9pY29ucy9iZWxsLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQm90IH0gZnJvbSBcIi4vaWNvbnMvYm90LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ3Jvd24gfSBmcm9tIFwiLi9pY29ucy9jcm93bi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBsdXMgfSBmcm9tIFwiLi9pY29ucy9wbHVzLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2F2ZSB9IGZyb20gXCIuL2ljb25zL3NhdmUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTaGllbGQgfSBmcm9tIFwiLi9pY29ucy9zaGllbGQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUcmFzaDIgfSBmcm9tIFwiLi9pY29ucy90cmFzaC0yLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVHdpdHRlciB9IGZyb20gXCIuL2ljb25zL3R3aXR0ZXIuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyIH0gZnJvbSBcIi4vaWNvbnMvdXNlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFphcCB9IGZyb20gXCIuL2ljb25zL3phcC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!********************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Eye: () => (/* reexport safe */ _icons_eye_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   EyeOff: () => (/* reexport safe */ _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Lock: () => (/* reexport safe */ _icons_lock_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Mail: () => (/* reexport safe */ _icons_mail_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_eye_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/eye.js */ \"./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/eye-off.js */ \"./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _icons_lock_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/lock.js */ \"./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _icons_mail_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/mail.js */ \"./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FeWUsRXllT2ZmLExvY2ssTWFpbCxVc2VyLFghPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDK0M7QUFDTztBQUNMO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2V4aWUtYWkvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9lMzY0Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFeWUgfSBmcm9tIFwiLi9pY29ucy9leWUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFeWVPZmYgfSBmcm9tIFwiLi9pY29ucy9leWUtb2ZmLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9jayB9IGZyb20gXCIuL2ljb25zL2xvY2suanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNYWlsIH0gZnJvbSBcIi4vaWNvbnMvbWFpbC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXIgfSBmcm9tIFwiLi9pY29ucy91c2VyLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgWCB9IGZyb20gXCIuL2ljb25zL3guanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fsettings&preferredRegion=&absolutePagePath=.%2Fpages%2Fsettings.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fsettings&preferredRegion=&absolutePagePath=.%2Fpages%2Fsettings.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/settings.tsx */ \"./pages/settings.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/settings\",\n        pathname: \"/settings\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fsettings&preferredRegion=&absolutePagePath=.%2Fpages%2Fsettings.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/AuthModal.tsx":
/*!**********************************!*\
  !*** ./components/AuthModal.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!lucide-react */ \"__barrel_optimize__?names=Eye,EyeOff,Lock,Mail,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\n\n\n\nconst AuthModal = ({ isOpen, onClose })=>{\n    const [isSignUp, setIsSignUp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { signIn, signUp } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        surface: \"#FFFFFF\",\n        background: \"#F5F1EB\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\"\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        try {\n            let result;\n            if (isSignUp) {\n                result = await signUp(email, password, fullName);\n            } else {\n                result = await signIn(email, password);\n            }\n            if (result.error) {\n                setError(result.error.message);\n            } else {\n                onClose();\n                // Reset form\n                setEmail(\"\");\n                setPassword(\"\");\n                setFullName(\"\");\n            }\n        } catch (err) {\n            setError(\"An unexpected error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"fixed\",\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            zIndex: 1000\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: colors.surface,\n                borderRadius: \"16px\",\n                padding: \"32px\",\n                width: \"100%\",\n                maxWidth: \"400px\",\n                margin: \"20px\",\n                boxShadow: \"0 20px 40px rgba(0, 0, 0, 0.1)\",\n                position: \"relative\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    style: {\n                        position: \"absolute\",\n                        top: \"16px\",\n                        right: \"16px\",\n                        background: \"none\",\n                        border: \"none\",\n                        cursor: \"pointer\",\n                        padding: \"8px\",\n                        borderRadius: \"8px\",\n                        color: colors.text.secondary\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: \"center\",\n                        marginBottom: \"32px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"32px\",\n                                color: colors.primary,\n                                fontFamily: \"Georgia, serif\",\n                                fontStyle: \"italic\",\n                                marginBottom: \"8px\"\n                            },\n                            children: \"ℰ\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: colors.text.primary,\n                                fontSize: \"24px\",\n                                fontWeight: \"600\",\n                                margin: 0,\n                                marginBottom: \"8px\"\n                            },\n                            children: isSignUp ? \"Create Account\" : \"Welcome Back\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: colors.text.secondary,\n                                fontSize: \"14px\",\n                                margin: 0\n                            },\n                            children: isSignUp ? \"Join Exie to get started\" : \"Sign in to your account\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    children: [\n                        isSignUp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        color: colors.text.primary,\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: \"Full Name\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.User, {\n                                            size: 16,\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                color: colors.text.tertiary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fullName,\n                                            onChange: (e)=>setFullName(e.target.value),\n                                            required: isSignUp,\n                                            style: {\n                                                width: \"100%\",\n                                                padding: \"12px 16px 12px 40px\",\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                boxSizing: \"border-box\"\n                                            },\n                                            placeholder: \"Enter your full name\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        color: colors.text.primary,\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Mail, {\n                                            size: 16,\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                color: colors.text.tertiary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            required: true,\n                                            style: {\n                                                width: \"100%\",\n                                                padding: \"12px 16px 12px 40px\",\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                boxSizing: \"border-box\"\n                                            },\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"24px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        color: colors.text.primary,\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: \"Password\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Lock, {\n                                            size: 16,\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                color: colors.text.tertiary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value),\n                                            required: true,\n                                            style: {\n                                                width: \"100%\",\n                                                padding: \"12px 40px 12px 40px\",\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                boxSizing: \"border-box\"\n                                            },\n                                            placeholder: \"Enter your password\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            style: {\n                                                position: \"absolute\",\n                                                right: \"12px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                background: \"none\",\n                                                border: \"none\",\n                                                cursor: \"pointer\",\n                                                color: colors.text.tertiary\n                                            },\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.EyeOff, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 33\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Eye, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 56\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, undefined),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: \"#DC2626\",\n                                fontSize: \"14px\",\n                                marginBottom: \"20px\",\n                                padding: \"12px\",\n                                backgroundColor: \"#FEF2F2\",\n                                border: \"1px solid #FECACA\",\n                                borderRadius: \"8px\"\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            style: {\n                                width: \"100%\",\n                                padding: \"12px\",\n                                background: loading ? colors.text.tertiary : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                color: \"white\",\n                                border: \"none\",\n                                borderRadius: \"8px\",\n                                fontSize: \"14px\",\n                                fontWeight: \"600\",\n                                cursor: loading ? \"not-allowed\" : \"pointer\",\n                                boxShadow: loading ? \"none\" : `0 4px 12px ${colors.primary}30`,\n                                marginBottom: \"20px\"\n                            },\n                            children: loading ? \"Please wait...\" : isSignUp ? \"Create Account\" : \"Sign In\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: \"center\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setIsSignUp(!isSignUp),\n                                style: {\n                                    background: \"none\",\n                                    border: \"none\",\n                                    color: colors.primary,\n                                    fontSize: \"14px\",\n                                    cursor: \"pointer\",\n                                    textDecoration: \"underline\"\n                                },\n                                children: isSignUp ? \"Already have an account? Sign in\" : \"Don't have an account? Sign up\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AuthModal.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AuthModal.tsx\n");

/***/ }),

/***/ "./components/SidebarLayoutSimple.tsx":
/*!********************************************!*\
  !*** ./components/SidebarLayoutSimple.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthModal */ \"./components/AuthModal.tsx\");\n\n\n\n\n\n\n\nconst SidebarLayout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, profile, loading } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Debug logging\n    console.log(\"Sidebar render:\", {\n        user: !!user,\n        userEmail: user?.email,\n        profile: !!profile,\n        profileName: profile?.full_name,\n        loading\n    });\n    // User data - only use real data when authenticated\n    const userData = user ? {\n        name: profile?.full_name || user?.user_metadata?.full_name || user?.email?.split(\"@\")[0] || \"User\",\n        email: user?.email || \"\",\n        plan: profile?.plan === \"pro\" ? \"Pro\" : profile?.plan === \"enterprise\" ? \"Enterprise\" : \"Free\",\n        avatar: profile?.avatar_url || null,\n        isOnline: profile?.is_online || false\n    } : null;\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        background: \"#F5F1EB\",\n        surface: \"#FFFFFF\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            surface: \"rgba(255, 107, 53, 0.05)\",\n            text: \"#2D1B14\",\n            textSecondary: \"#5D4037\",\n            textMuted: \"#8D6E63\",\n            accent: \"#FF6B35\",\n            accentSoft: \"#FF8A65\",\n            accentLight: \"#FFF7F4\",\n            border: \"rgba(255, 107, 53, 0.15)\",\n            hover: \"rgba(255, 107, 53, 0.08)\",\n            divider: \"rgba(255, 107, 53, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.25)\" // Orange glow\n        }\n    };\n    const menuItems = [\n        {\n            section: \"WORKSPACE\",\n            items: [\n                {\n                    href: \"/\",\n                    label: \"Briefing Room\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home\n                },\n                {\n                    href: \"/tweet-center\",\n                    label: \"Drafting Desk\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageCircle\n                },\n                {\n                    href: \"/schedule\",\n                    label: \"Content Scheduler\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Calendar\n                },\n                {\n                    href: \"/dashboard\",\n                    label: \"Growth Lab\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.BarChart3\n                },\n                {\n                    href: \"/meeting\",\n                    label: \"AI Meetings\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Video\n                }\n            ]\n        },\n        {\n            section: \"SETTINGS\",\n            items: [\n                {\n                    href: \"/settings\",\n                    label: \"Settings\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User\n                }\n            ]\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: colors.background,\n            padding: \"16px\",\n            gap: \"16px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    background: colors.sidebar.background,\n                    height: \"calc(100vh - 32px)\",\n                    borderRadius: \"24px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: `0 8px 32px ${colors.sidebar.glow}, 0 2px 8px rgba(255, 107, 53, 0.1)`,\n                    border: `1px solid ${colors.sidebar.border}`,\n                    overflow: \"hidden\",\n                    position: \"relative\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"32px 20px 24px 20px\",\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            background: colors.sidebar.background\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"https://nlckamsrdiwkyyrxzntf.supabase.co/storage/v1/object/sign/logos/elogos.png?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InN0b3JhZ2UtdXJsLXNpZ25pbmcta2V5X2RiNTE0YzE5LTlhNTQtNGZiNy1hMjY3LTJmNjY5ZDlhZjY1OCJ9.eyJ1cmwiOiJsb2dvcy9lbG9nb3MucG5nIiwiaWF0IjoxNzQ4Mjk2NDIyLCJleHAiOjE3Nzk4MzI0MjJ9.FJlERvbHYRsm-4XpUyFKY1_xnFV988GB6X9M3vMarjE\",\n                            alt: \"Exie Logo\",\n                            style: {\n                                height: \"42px\",\n                                width: \"auto\",\n                                objectFit: \"contain\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"0 16px 16px 16px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                display: \"flex\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Search, {\n                                    size: 14,\n                                    color: colors.sidebar.textMuted,\n                                    style: {\n                                        position: \"absolute\",\n                                        left: \"12px\",\n                                        zIndex: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search...\",\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"8px 12px 8px 36px\",\n                                        border: `1px solid ${colors.sidebar.border}`,\n                                        borderRadius: \"8px\",\n                                        background: colors.sidebar.surface,\n                                        color: colors.sidebar.text,\n                                        fontSize: \"12px\",\n                                        fontWeight: \"400\",\n                                        outline: \"none\",\n                                        transition: \"all 0.2s ease\"\n                                    },\n                                    onFocus: (e)=>{\n                                        e.target.style.borderColor = colors.sidebar.accent;\n                                        e.target.style.background = colors.sidebar.background;\n                                    },\n                                    onBlur: (e)=>{\n                                        e.target.style.borderColor = colors.sidebar.border;\n                                        e.target.style.background = colors.sidebar.surface;\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"8px 16px\",\n                            flex: 1,\n                            display: \"flex\",\n                            flexDirection: \"column\"\n                        },\n                        children: menuItems.map((section, sectionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: sectionIndex < menuItems.length - 1 ? \"24px\" : \"0\"\n                                },\n                                children: [\n                                    sectionIndex > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            height: \"1px\",\n                                            background: `linear-gradient(90deg, transparent, ${colors.sidebar.divider}, transparent)`,\n                                            margin: \"16px 12px 20px 12px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"10px\",\n                                            fontWeight: \"600\",\n                                            color: colors.sidebar.textMuted,\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"1px\",\n                                            marginBottom: \"10px\",\n                                            paddingLeft: \"12px\"\n                                        },\n                                        children: section.section\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\",\n                                            gap: \"2px\"\n                                        },\n                                        children: section.items.map((item)=>{\n                                            const active = isActive(item.href);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        padding: \"8px 12px\",\n                                                        borderRadius: \"8px\",\n                                                        background: active ? colors.sidebar.surface : \"transparent\",\n                                                        cursor: \"pointer\",\n                                                        transition: \"all 0.2s ease\",\n                                                        position: \"relative\"\n                                                    },\n                                                    onMouseEnter: (e)=>{\n                                                        if (!active) {\n                                                            e.currentTarget.style.background = colors.sidebar.hover;\n                                                        }\n                                                    },\n                                                    onMouseLeave: (e)=>{\n                                                        if (!active) {\n                                                            e.currentTarget.style.background = \"transparent\";\n                                                        }\n                                                    },\n                                                    children: [\n                                                        active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: \"absolute\",\n                                                                left: \"0\",\n                                                                top: \"50%\",\n                                                                transform: \"translateY(-50%)\",\n                                                                width: \"2px\",\n                                                                height: \"16px\",\n                                                                background: colors.sidebar.accent,\n                                                                borderRadius: \"0 2px 2px 0\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: \"16px\",\n                                                                height: \"16px\",\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                justifyContent: \"center\",\n                                                                marginRight: \"10px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 14,\n                                                                color: active ? colors.sidebar.accent : colors.sidebar.textSecondary,\n                                                                strokeWidth: 2\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                                fontSize: \"13px\",\n                                                                fontWeight: active ? \"500\" : \"400\",\n                                                                letterSpacing: \"0.1px\"\n                                                            },\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, item.href, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, section.section, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            margin: \"0 16px 16px 16px\",\n                            height: \"1px\",\n                            background: `linear-gradient(90deg, transparent, ${colors.sidebar.divider}, transparent)`\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"12px 16px 20px 16px\"\n                        },\n                        children: user && userData ? // Authenticated user - show profile\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    padding: \"12px\",\n                                    background: \"transparent\",\n                                    borderRadius: \"12px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    border: \"none\"\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.hover;\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = \"transparent\";\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"36px\",\n                                            height: \"36px\",\n                                            background: userData.avatar ? \"transparent\" : colors.sidebar.surface,\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            overflow: \"hidden\",\n                                            border: `1px solid ${colors.sidebar.border}`\n                                        },\n                                        children: [\n                                            userData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userData.avatar,\n                                                alt: userData.name,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"50%\"\n                                                },\n                                                onError: (e)=>{\n                                                    // Fallback to icon if image fails to load\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.parentElement.style.background = colors.sidebar.surface;\n                                                    e.currentTarget.parentElement.innerHTML = `<svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"${colors.sidebar.textSecondary}\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path><circle cx=\"12\" cy=\"7\" r=\"4\"></circle></svg>`;\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 18,\n                                                color: colors.sidebar.textSecondary,\n                                                strokeWidth: 1.5\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"2px\",\n                                                    right: \"2px\",\n                                                    width: \"8px\",\n                                                    height: \"8px\",\n                                                    background: \"#10B981\",\n                                                    borderRadius: \"50%\",\n                                                    border: \"2px solid white\",\n                                                    boxShadow: \"0 0 8px rgba(16, 185, 129, 0.6), 0 0 4px rgba(16, 185, 129, 0.4)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    lineHeight: \"1.2\",\n                                                    marginBottom: \"2px\"\n                                                },\n                                                children: userData.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.textMuted,\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"500\",\n                                                    lineHeight: \"1.2\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            userData.plan,\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: colors.sidebar.accent\n                                                        },\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, undefined) : // Not authenticated - show sign in button\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>setShowAuthModal(true),\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                padding: \"8px 12px\",\n                                borderRadius: \"8px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\",\n                                background: \"transparent\"\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.background = colors.sidebar.hover;\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.background = \"transparent\";\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"16px\",\n                                        height: \"16px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        marginRight: \"10px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogIn, {\n                                        size: 14,\n                                        color: colors.sidebar.textSecondary,\n                                        strokeWidth: 2\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: colors.sidebar.textSecondary,\n                                        fontSize: \"13px\",\n                                        fontWeight: \"400\",\n                                        letterSpacing: \"0.1px\"\n                                    },\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.surface,\n                    borderRadius: \"24px\",\n                    height: \"calc(100vh - 32px)\",\n                    position: \"relative\",\n                    boxShadow: \"0 4px 20px rgba(255, 107, 53, 0.08)\",\n                    border: `1px solid ${colors.sidebar.border}`,\n                    overflow: \"hidden\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"32px\",\n                        height: \"100%\",\n                        overflow: \"auto\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1NpZGViYXJMYXlvdXRTaW1wbGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNkI7QUFDVztBQUNBO0FBQzREO0FBQ2xEO0FBQ2Q7QUFNcEMsTUFBTWMsZ0JBQThDLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQy9ELE1BQU1DLFNBQVNiLHNEQUFTQTtJQUN4QixNQUFNLEVBQUVjLElBQUksRUFBRUMsT0FBTyxFQUFFQyxPQUFPLEVBQUUsR0FBR1AsOERBQU9BO0lBQzFDLE1BQU0sQ0FBQ1EsZUFBZUMsaUJBQWlCLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUVuRCxnQkFBZ0I7SUFDaEJvQixRQUFRQyxHQUFHLENBQUMsbUJBQW1CO1FBQzdCTixNQUFNLENBQUMsQ0FBQ0E7UUFDUk8sV0FBV1AsTUFBTVE7UUFDakJQLFNBQVMsQ0FBQyxDQUFDQTtRQUNYUSxhQUFhUixTQUFTUztRQUN0QlI7SUFDRjtJQUVBLG9EQUFvRDtJQUNwRCxNQUFNUyxXQUFXWCxPQUFPO1FBQ3RCWSxNQUFNWCxTQUFTUyxhQUFhVixNQUFNYSxlQUFlSCxhQUFhVixNQUFNUSxPQUFPTSxNQUFNLElBQUksQ0FBQyxFQUFFLElBQUk7UUFDNUZOLE9BQU9SLE1BQU1RLFNBQVM7UUFDdEJPLE1BQU1kLFNBQVNjLFNBQVMsUUFBUSxRQUFRZCxTQUFTYyxTQUFTLGVBQWUsZUFBZTtRQUN4RkMsUUFBUWYsU0FBU2dCLGNBQWM7UUFDL0JDLFVBQVVqQixTQUFTa0IsYUFBYTtJQUNsQyxJQUFJO0lBRUosTUFBTUMsU0FBUztRQUNiQyxTQUFTO1FBQ1RDLGNBQWM7UUFDZEMsWUFBWTtRQUNaQyxTQUFTO1FBQ1RDLE1BQU07WUFDSkosU0FBUztZQUNUSyxXQUFXO1lBQ1hDLFVBQVU7UUFDWjtRQUNBQyxTQUFTO1lBQ1BMLFlBQVk7WUFDWkMsU0FBUztZQUNUQyxNQUFNO1lBQ05JLGVBQWU7WUFDZkMsV0FBVztZQUNYQyxRQUFRO1lBQ1JDLFlBQVk7WUFDWkMsYUFBYTtZQUNiQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsU0FBUztZQUNUQyxNQUFNLDJCQUEyQixjQUFjO1FBQ2pEO0lBQ0Y7SUFFQSxNQUFNQyxZQUFZO1FBQ2hCO1lBQ0VDLFNBQVM7WUFDVEMsT0FBTztnQkFDTDtvQkFBRUMsTUFBTTtvQkFBS0MsT0FBTztvQkFBaUJDLE1BQU14RCxtSUFBSUE7Z0JBQUM7Z0JBQ2hEO29CQUFFc0QsTUFBTTtvQkFBaUJDLE9BQU87b0JBQWlCQyxNQUFNdEQsNElBQWFBO2dCQUFDO2dCQUNyRTtvQkFBRW9ELE1BQU07b0JBQWFDLE9BQU87b0JBQXFCQyxNQUFNakQsdUlBQVFBO2dCQUFDO2dCQUNoRTtvQkFBRStDLE1BQU07b0JBQWNDLE9BQU87b0JBQWNDLE1BQU12RCx3SUFBU0E7Z0JBQUM7Z0JBQzNEO29CQUFFcUQsTUFBTTtvQkFBWUMsT0FBTztvQkFBZUMsTUFBTXJELG9JQUFLQTtnQkFBQzthQUN2RDtRQUNIO1FBQ0E7WUFDRWlELFNBQVM7WUFDVEMsT0FBTztnQkFDTDtvQkFBRUMsTUFBTTtvQkFBYUMsT0FBTztvQkFBWUMsTUFBTXBELG1JQUFJQTtnQkFBQzthQUNwRDtRQUNIO0tBQ0Q7SUFFRCxNQUFNcUQsV0FBVyxDQUFDSDtRQUNoQixJQUFJQSxTQUFTLEtBQUs7WUFDaEIsT0FBTzFDLE9BQU84QyxRQUFRLEtBQUs7UUFDN0I7UUFDQSxPQUFPOUMsT0FBTzhDLFFBQVEsQ0FBQ0MsVUFBVSxDQUFDTDtJQUNwQztJQUVBLHFCQUNFLDhEQUFDTTtRQUFJQyxPQUFPO1lBQ1ZDLFNBQVM7WUFDVEMsV0FBVztZQUNYM0IsWUFBWUgsT0FBT0csVUFBVTtZQUM3QjRCLFNBQVM7WUFDVEMsS0FBSztRQUNQOzswQkFDRSw4REFBQ0M7Z0JBQU1MLE9BQU87b0JBQ1pNLE9BQU87b0JBQ1AvQixZQUFZSCxPQUFPUSxPQUFPLENBQUNMLFVBQVU7b0JBQ3JDZ0MsUUFBUTtvQkFDUkMsY0FBYztvQkFDZFAsU0FBUztvQkFDVFEsZUFBZTtvQkFDZkMsV0FBVyxDQUFDLFdBQVcsRUFBRXRDLE9BQU9RLE9BQU8sQ0FBQ1MsSUFBSSxDQUFDLG1DQUFtQyxDQUFDO29CQUNqRkgsUUFBUSxDQUFDLFVBQVUsRUFBRWQsT0FBT1EsT0FBTyxDQUFDTSxNQUFNLENBQUMsQ0FBQztvQkFDNUN5QixVQUFVO29CQUNWQyxVQUFVO2dCQUNaOztrQ0FFRSw4REFBQ2I7d0JBQUlDLE9BQU87NEJBQ1ZHLFNBQVM7NEJBQ1RGLFNBQVM7NEJBQ1RZLGdCQUFnQjs0QkFDaEJDLFlBQVk7NEJBQ1p2QyxZQUFZSCxPQUFPUSxPQUFPLENBQUNMLFVBQVU7d0JBQ3ZDO2tDQUNFLDRFQUFDd0M7NEJBQ0NDLEtBQUk7NEJBQ0pDLEtBQUk7NEJBQ0pqQixPQUFPO2dDQUNMTyxRQUFRO2dDQUNSRCxPQUFPO2dDQUNQWSxXQUFXOzRCQUNiOzs7Ozs7Ozs7OztrQ0FLSiw4REFBQ25CO3dCQUFJQyxPQUFPOzRCQUNWRyxTQUFTO3dCQUNYO2tDQUNFLDRFQUFDSjs0QkFBSUMsT0FBTztnQ0FDVlksVUFBVTtnQ0FDVlgsU0FBUztnQ0FDVGEsWUFBWTs0QkFDZDs7OENBQ0UsOERBQUNyRSxxSUFBTUE7b0NBQ0wwRSxNQUFNO29DQUNOQyxPQUFPaEQsT0FBT1EsT0FBTyxDQUFDRSxTQUFTO29DQUMvQmtCLE9BQU87d0NBQ0xZLFVBQVU7d0NBQ1ZTLE1BQU07d0NBQ05DLFFBQVE7b0NBQ1Y7Ozs7Ozs4Q0FFRiw4REFBQ0M7b0NBQ0NDLE1BQUs7b0NBQ0xDLGFBQVk7b0NBQ1p6QixPQUFPO3dDQUNMTSxPQUFPO3dDQUNQSCxTQUFTO3dDQUNUakIsUUFBUSxDQUFDLFVBQVUsRUFBRWQsT0FBT1EsT0FBTyxDQUFDTSxNQUFNLENBQUMsQ0FBQzt3Q0FDNUNzQixjQUFjO3dDQUNkakMsWUFBWUgsT0FBT1EsT0FBTyxDQUFDSixPQUFPO3dDQUNsQzRDLE9BQU9oRCxPQUFPUSxPQUFPLENBQUNILElBQUk7d0NBQzFCaUQsVUFBVTt3Q0FDVkMsWUFBWTt3Q0FDWkMsU0FBUzt3Q0FDVEMsWUFBWTtvQ0FDZDtvQ0FDQUMsU0FBUyxDQUFDQzt3Q0FDUkEsRUFBRUMsTUFBTSxDQUFDaEMsS0FBSyxDQUFDaUMsV0FBVyxHQUFHN0QsT0FBT1EsT0FBTyxDQUFDRyxNQUFNO3dDQUNsRGdELEVBQUVDLE1BQU0sQ0FBQ2hDLEtBQUssQ0FBQ3pCLFVBQVUsR0FBR0gsT0FBT1EsT0FBTyxDQUFDTCxVQUFVO29DQUN2RDtvQ0FDQTJELFFBQVEsQ0FBQ0g7d0NBQ1BBLEVBQUVDLE1BQU0sQ0FBQ2hDLEtBQUssQ0FBQ2lDLFdBQVcsR0FBRzdELE9BQU9RLE9BQU8sQ0FBQ00sTUFBTTt3Q0FDbEQ2QyxFQUFFQyxNQUFNLENBQUNoQyxLQUFLLENBQUN6QixVQUFVLEdBQUdILE9BQU9RLE9BQU8sQ0FBQ0osT0FBTztvQ0FDcEQ7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1OLDhEQUFDMkQ7d0JBQUluQyxPQUFPOzRCQUNWRyxTQUFTOzRCQUNUaUMsTUFBTTs0QkFDTm5DLFNBQVM7NEJBQ1RRLGVBQWU7d0JBQ2pCO2tDQUNHbkIsVUFBVStDLEdBQUcsQ0FBQyxDQUFDOUMsU0FBUytDLDZCQUN2Qiw4REFBQ3ZDO2dDQUEwQkMsT0FBTztvQ0FBRXVDLGNBQWNELGVBQWVoRCxVQUFVa0QsTUFBTSxHQUFHLElBQUksU0FBUztnQ0FBSTs7b0NBRWxHRixlQUFlLG1CQUNkLDhEQUFDdkM7d0NBQUlDLE9BQU87NENBQ1ZPLFFBQVE7NENBQ1JoQyxZQUFZLENBQUMsb0NBQW9DLEVBQUVILE9BQU9RLE9BQU8sQ0FBQ1EsT0FBTyxDQUFDLGNBQWMsQ0FBQzs0Q0FDekZxRCxRQUFRO3dDQUNWOzs7Ozs7a0RBSUYsOERBQUMxQzt3Q0FBSUMsT0FBTzs0Q0FDVjBCLFVBQVU7NENBQ1ZDLFlBQVk7NENBQ1pQLE9BQU9oRCxPQUFPUSxPQUFPLENBQUNFLFNBQVM7NENBQy9CNEQsZUFBZTs0Q0FDZkMsZUFBZTs0Q0FDZkosY0FBYzs0Q0FDZEssYUFBYTt3Q0FDZjtrREFDR3JELFFBQVFBLE9BQU87Ozs7OztrREFJbEIsOERBQUNRO3dDQUFJQyxPQUFPOzRDQUFFQyxTQUFTOzRDQUFRUSxlQUFlOzRDQUFVTCxLQUFLO3dDQUFNO2tEQUNoRWIsUUFBUUMsS0FBSyxDQUFDNkMsR0FBRyxDQUFDLENBQUNROzRDQUNsQixNQUFNQyxTQUFTbEQsU0FBU2lELEtBQUtwRCxJQUFJOzRDQUNqQyxxQkFDRSw4REFBQzFELGtEQUFJQTtnREFBaUIwRCxNQUFNb0QsS0FBS3BELElBQUk7Z0RBQUVPLE9BQU87b0RBQUUrQyxnQkFBZ0I7Z0RBQU87MERBQ3JFLDRFQUFDaEQ7b0RBQUlDLE9BQU87d0RBQ1ZDLFNBQVM7d0RBQ1RhLFlBQVk7d0RBQ1pYLFNBQVM7d0RBQ1RLLGNBQWM7d0RBQ2RqQyxZQUFZdUUsU0FBUzFFLE9BQU9RLE9BQU8sQ0FBQ0osT0FBTyxHQUFHO3dEQUM5Q3dFLFFBQVE7d0RBQ1JuQixZQUFZO3dEQUNaakIsVUFBVTtvREFDWjtvREFDQXFDLGNBQWMsQ0FBQ2xCO3dEQUNiLElBQUksQ0FBQ2UsUUFBUTs0REFDWGYsRUFBRW1CLGFBQWEsQ0FBQ2xELEtBQUssQ0FBQ3pCLFVBQVUsR0FBR0gsT0FBT1EsT0FBTyxDQUFDTyxLQUFLO3dEQUN6RDtvREFDRjtvREFDQWdFLGNBQWMsQ0FBQ3BCO3dEQUNiLElBQUksQ0FBQ2UsUUFBUTs0REFDWGYsRUFBRW1CLGFBQWEsQ0FBQ2xELEtBQUssQ0FBQ3pCLFVBQVUsR0FBRzt3REFDckM7b0RBQ0Y7O3dEQUdHdUUsd0JBQ0MsOERBQUMvQzs0REFBSUMsT0FBTztnRUFDVlksVUFBVTtnRUFDVlMsTUFBTTtnRUFDTitCLEtBQUs7Z0VBQ0xDLFdBQVc7Z0VBQ1gvQyxPQUFPO2dFQUNQQyxRQUFRO2dFQUNSaEMsWUFBWUgsT0FBT1EsT0FBTyxDQUFDRyxNQUFNO2dFQUNqQ3lCLGNBQWM7NERBQ2hCOzs7Ozs7c0VBSUYsOERBQUNUOzREQUFJQyxPQUFPO2dFQUNWTSxPQUFPO2dFQUNQQyxRQUFRO2dFQUNSTixTQUFTO2dFQUNUYSxZQUFZO2dFQUNaRCxnQkFBZ0I7Z0VBQ2hCeUMsYUFBYTs0REFDZjtzRUFDRSw0RUFBQ1QsS0FBS2xELElBQUk7Z0VBQ1J3QixNQUFNO2dFQUNOQyxPQUFPMEIsU0FBUzFFLE9BQU9RLE9BQU8sQ0FBQ0csTUFBTSxHQUFHWCxPQUFPUSxPQUFPLENBQUNDLGFBQWE7Z0VBQ3BFMEUsYUFBYTs7Ozs7Ozs7Ozs7c0VBS2pCLDhEQUFDQzs0REFBS3hELE9BQU87Z0VBQ1hvQixPQUFPMEIsU0FBUzFFLE9BQU9RLE9BQU8sQ0FBQ0gsSUFBSSxHQUFHTCxPQUFPUSxPQUFPLENBQUNDLGFBQWE7Z0VBQ2xFNkMsVUFBVTtnRUFDVkMsWUFBWW1CLFNBQVMsUUFBUTtnRUFDN0JILGVBQWU7NERBQ2pCO3NFQUNHRSxLQUFLbkQsS0FBSzs7Ozs7Ozs7Ozs7OytDQTNETm1ELEtBQUtwRCxJQUFJOzs7Ozt3Q0FnRXhCOzs7Ozs7OytCQTVGTUYsUUFBUUEsT0FBTzs7Ozs7Ozs7OztrQ0FtRzdCLDhEQUFDUTt3QkFBSUMsT0FBTzs0QkFDVnlDLFFBQVE7NEJBQ1JsQyxRQUFROzRCQUNSaEMsWUFBWSxDQUFDLG9DQUFvQyxFQUFFSCxPQUFPUSxPQUFPLENBQUNRLE9BQU8sQ0FBQyxjQUFjLENBQUM7d0JBQzNGOzs7Ozs7a0NBR0EsOERBQUNXO3dCQUFJQyxPQUFPOzRCQUNWRyxTQUFTO3dCQUNYO2tDQUNHbkQsUUFBUVcsV0FDUCxvQ0FBb0M7c0NBQ3BDLDhEQUFDNUIsa0RBQUlBOzRCQUFDMEQsTUFBSzs0QkFBWU8sT0FBTztnQ0FBRStDLGdCQUFnQjs0QkFBTztzQ0FDckQsNEVBQUNoRDtnQ0FBSUMsT0FBTztvQ0FDVkMsU0FBUztvQ0FDVGEsWUFBWTtvQ0FDWlYsS0FBSztvQ0FDTEQsU0FBUztvQ0FDVDVCLFlBQVk7b0NBQ1ppQyxjQUFjO29DQUNkd0MsUUFBUTtvQ0FDUm5CLFlBQVk7b0NBQ1ozQyxRQUFRO2dDQUNWO2dDQUNBK0QsY0FBYyxDQUFDbEI7b0NBQ2JBLEVBQUVtQixhQUFhLENBQUNsRCxLQUFLLENBQUN6QixVQUFVLEdBQUdILE9BQU9RLE9BQU8sQ0FBQ08sS0FBSztnQ0FDekQ7Z0NBQ0FnRSxjQUFjLENBQUNwQjtvQ0FDYkEsRUFBRW1CLGFBQWEsQ0FBQ2xELEtBQUssQ0FBQ3pCLFVBQVUsR0FBRztnQ0FDckM7O2tEQUVFLDhEQUFDd0I7d0NBQUlDLE9BQU87NENBQ1ZNLE9BQU87NENBQ1BDLFFBQVE7NENBQ1JoQyxZQUFZWixTQUFTSyxNQUFNLEdBQUcsZ0JBQWdCSSxPQUFPUSxPQUFPLENBQUNKLE9BQU87NENBQ3BFZ0MsY0FBYzs0Q0FDZFAsU0FBUzs0Q0FDVGEsWUFBWTs0Q0FDWkQsZ0JBQWdCOzRDQUNoQkQsVUFBVTs0Q0FDVkQsVUFBVTs0Q0FDVnpCLFFBQVEsQ0FBQyxVQUFVLEVBQUVkLE9BQU9RLE9BQU8sQ0FBQ00sTUFBTSxDQUFDLENBQUM7d0NBQzlDOzs0Q0FDR3ZCLFNBQVNLLE1BQU0saUJBQ2QsOERBQUMrQztnREFDQ0MsS0FBS3JELFNBQVNLLE1BQU07Z0RBQ3BCaUQsS0FBS3RELFNBQVNDLElBQUk7Z0RBQ2xCb0MsT0FBTztvREFDTE0sT0FBTztvREFDUEMsUUFBUTtvREFDUlcsV0FBVztvREFDWFYsY0FBYztnREFDaEI7Z0RBQ0FpRCxTQUFTLENBQUMxQjtvREFDUiwwQ0FBMEM7b0RBQzFDQSxFQUFFbUIsYUFBYSxDQUFDbEQsS0FBSyxDQUFDQyxPQUFPLEdBQUc7b0RBQ2hDOEIsRUFBRW1CLGFBQWEsQ0FBQ1EsYUFBYSxDQUFFMUQsS0FBSyxDQUFDekIsVUFBVSxHQUFHSCxPQUFPUSxPQUFPLENBQUNKLE9BQU87b0RBQ3hFdUQsRUFBRW1CLGFBQWEsQ0FBQ1EsYUFBYSxDQUFFQyxTQUFTLEdBQUcsQ0FBQyxvRUFBb0UsRUFBRXZGLE9BQU9RLE9BQU8sQ0FBQ0MsYUFBYSxDQUFDLDJLQUEySyxDQUFDO2dEQUM3VDs7Ozs7MEVBR0YsOERBQUN0QyxtSUFBSUE7Z0RBQUM0RSxNQUFNO2dEQUFJQyxPQUFPaEQsT0FBT1EsT0FBTyxDQUFDQyxhQUFhO2dEQUFFMEUsYUFBYTs7Ozs7OzBEQUdwRSw4REFBQ3hEO2dEQUFJQyxPQUFPO29EQUNWWSxVQUFVO29EQUNWZ0QsUUFBUTtvREFDUkMsT0FBTztvREFDUHZELE9BQU87b0RBQ1BDLFFBQVE7b0RBQ1JoQyxZQUFZO29EQUNaaUMsY0FBYztvREFDZHRCLFFBQVE7b0RBQ1J3QixXQUFXO2dEQUNiOzs7Ozs7Ozs7Ozs7a0RBRUYsOERBQUNYO3dDQUFJQyxPQUFPOzRDQUFFb0MsTUFBTTt3Q0FBRTs7MERBQ3BCLDhEQUFDckM7Z0RBQUlDLE9BQU87b0RBQ1ZvQixPQUFPaEQsT0FBT1EsT0FBTyxDQUFDSCxJQUFJO29EQUMxQmlELFVBQVU7b0RBQ1ZDLFlBQVk7b0RBQ1ptQyxZQUFZO29EQUNadkIsY0FBYztnREFDaEI7MERBQ0c1RSxTQUFTQyxJQUFJOzs7Ozs7MERBRWhCLDhEQUFDbUM7Z0RBQUlDLE9BQU87b0RBQ1ZvQixPQUFPaEQsT0FBT1EsT0FBTyxDQUFDRSxTQUFTO29EQUMvQjRDLFVBQVU7b0RBQ1ZDLFlBQVk7b0RBQ1ptQyxZQUFZO29EQUNaN0QsU0FBUztvREFDVGEsWUFBWTtvREFDWlYsS0FBSztnREFDUDs7a0VBQ0UsOERBQUNvRDs7NERBQU03RixTQUFTSSxJQUFJOzREQUFDOzs7Ozs7O2tFQUNyQiw4REFBQ3lGO3dEQUFLeEQsT0FBTzs0REFBRW9CLE9BQU9oRCxPQUFPUSxPQUFPLENBQUNHLE1BQU07d0RBQUM7a0VBQUc7Ozs7OztrRUFDL0MsOERBQUN5RTtrRUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FNZCwwQ0FBMEM7c0NBQzFDLDhEQUFDekQ7NEJBQ0NnRSxTQUFTLElBQU0zRyxpQkFBaUI7NEJBQ2hDNEMsT0FBTztnQ0FDTEMsU0FBUztnQ0FDVGEsWUFBWTtnQ0FDWlgsU0FBUztnQ0FDVEssY0FBYztnQ0FDZHdDLFFBQVE7Z0NBQ1JuQixZQUFZO2dDQUNadEQsWUFBWTs0QkFDZDs0QkFDQTBFLGNBQWMsQ0FBQ2xCO2dDQUNiQSxFQUFFbUIsYUFBYSxDQUFDbEQsS0FBSyxDQUFDekIsVUFBVSxHQUFHSCxPQUFPUSxPQUFPLENBQUNPLEtBQUs7NEJBQ3pEOzRCQUNBZ0UsY0FBYyxDQUFDcEI7Z0NBQ2JBLEVBQUVtQixhQUFhLENBQUNsRCxLQUFLLENBQUN6QixVQUFVLEdBQUc7NEJBQ3JDOzs4Q0FHQSw4REFBQ3dCO29DQUFJQyxPQUFPO3dDQUNWTSxPQUFPO3dDQUNQQyxRQUFRO3dDQUNSTixTQUFTO3dDQUNUYSxZQUFZO3dDQUNaRCxnQkFBZ0I7d0NBQ2hCeUMsYUFBYTtvQ0FDZjs4Q0FDRSw0RUFBQzlHLG9JQUFLQTt3Q0FDSjJFLE1BQU07d0NBQ05DLE9BQU9oRCxPQUFPUSxPQUFPLENBQUNDLGFBQWE7d0NBQ25DMEUsYUFBYTs7Ozs7Ozs7Ozs7OENBS2pCLDhEQUFDQztvQ0FBS3hELE9BQU87d0NBQ1hvQixPQUFPaEQsT0FBT1EsT0FBTyxDQUFDQyxhQUFhO3dDQUNuQzZDLFVBQVU7d0NBQ1ZDLFlBQVk7d0NBQ1pnQixlQUFlO29DQUNqQjs4Q0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUVgsOERBQUNxQjtnQkFBS2hFLE9BQU87b0JBQ1hpRSxVQUFVO29CQUNWQyxpQkFBaUI5RixPQUFPSSxPQUFPO29CQUMvQmdDLGNBQWM7b0JBQ2RELFFBQVE7b0JBQ1JLLFVBQVU7b0JBQ1ZGLFdBQVc7b0JBQ1h4QixRQUFRLENBQUMsVUFBVSxFQUFFZCxPQUFPUSxPQUFPLENBQUNNLE1BQU0sQ0FBQyxDQUFDO29CQUM1Q3lCLFVBQVU7Z0JBQ1o7MEJBQ0UsNEVBQUNaO29CQUFJQyxPQUFPO3dCQUNWRyxTQUFTO3dCQUNUSSxRQUFRO3dCQUNSSSxVQUFVO29CQUNaOzhCQUNHN0Q7Ozs7Ozs7Ozs7OzBCQUtMLDhEQUFDRixrREFBU0E7Z0JBQ1J1SCxRQUFRaEg7Z0JBQ1JpSCxTQUFTLElBQU1oSCxpQkFBaUI7Ozs7Ozs7Ozs7OztBQUl4QztBQUVBLGlFQUFlUCxhQUFhQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL2NvbXBvbmVudHMvU2lkZWJhckxheW91dFNpbXBsZS50c3g/NzJhMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuaW1wb3J0IHsgSG9tZSwgQmFyQ2hhcnQzLCBNZXNzYWdlQ2lyY2xlLCBWaWRlbywgVXNlciwgTG9nSW4sIFNlYXJjaCwgQ2FsZW5kYXIgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdXNlVXNlciB9IGZyb20gJy4uL2NvbnRleHRzL1VzZXJDb250ZXh0JztcbmltcG9ydCBBdXRoTW9kYWwgZnJvbSAnLi9BdXRoTW9kYWwnO1xuXG5pbnRlcmZhY2UgU2lkZWJhckxheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuY29uc3QgU2lkZWJhckxheW91dDogUmVhY3QuRkM8U2lkZWJhckxheW91dFByb3BzPiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHsgdXNlciwgcHJvZmlsZSwgbG9hZGluZyB9ID0gdXNlVXNlcigpO1xuICBjb25zdCBbc2hvd0F1dGhNb2RhbCwgc2V0U2hvd0F1dGhNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gRGVidWcgbG9nZ2luZ1xuICBjb25zb2xlLmxvZygnU2lkZWJhciByZW5kZXI6Jywge1xuICAgIHVzZXI6ICEhdXNlcixcbiAgICB1c2VyRW1haWw6IHVzZXI/LmVtYWlsLFxuICAgIHByb2ZpbGU6ICEhcHJvZmlsZSxcbiAgICBwcm9maWxlTmFtZTogcHJvZmlsZT8uZnVsbF9uYW1lLFxuICAgIGxvYWRpbmdcbiAgfSk7XG5cbiAgLy8gVXNlciBkYXRhIC0gb25seSB1c2UgcmVhbCBkYXRhIHdoZW4gYXV0aGVudGljYXRlZFxuICBjb25zdCB1c2VyRGF0YSA9IHVzZXIgPyB7XG4gICAgbmFtZTogcHJvZmlsZT8uZnVsbF9uYW1lIHx8IHVzZXI/LnVzZXJfbWV0YWRhdGE/LmZ1bGxfbmFtZSB8fCB1c2VyPy5lbWFpbD8uc3BsaXQoJ0AnKVswXSB8fCAnVXNlcicsXG4gICAgZW1haWw6IHVzZXI/LmVtYWlsIHx8ICcnLFxuICAgIHBsYW46IHByb2ZpbGU/LnBsYW4gPT09ICdwcm8nID8gJ1BybycgOiBwcm9maWxlPy5wbGFuID09PSAnZW50ZXJwcmlzZScgPyAnRW50ZXJwcmlzZScgOiAnRnJlZScsXG4gICAgYXZhdGFyOiBwcm9maWxlPy5hdmF0YXJfdXJsIHx8IG51bGwsXG4gICAgaXNPbmxpbmU6IHByb2ZpbGU/LmlzX29ubGluZSB8fCBmYWxzZVxuICB9IDogbnVsbDtcblxuICBjb25zdCBjb2xvcnMgPSB7XG4gICAgcHJpbWFyeTogJyNGRjZCMzUnLFxuICAgIHByaW1hcnlMaWdodDogJyNGRjhBNjUnLFxuICAgIGJhY2tncm91bmQ6ICcjRjVGMUVCJywgLy8gQ2hpbGwgYmVpZ2UgYmFja2dyb3VuZFxuICAgIHN1cmZhY2U6ICcjRkZGRkZGJyxcbiAgICB0ZXh0OiB7XG4gICAgICBwcmltYXJ5OiAnIzJEMUIxNCcsXG4gICAgICBzZWNvbmRhcnk6ICcjNUQ0MDM3JyxcbiAgICAgIHRlcnRpYXJ5OiAnIzhENkU2MydcbiAgICB9LFxuICAgIHNpZGViYXI6IHtcbiAgICAgIGJhY2tncm91bmQ6ICcjRkZGRkZGJywgLy8gQ2xlYW4gd2hpdGVcbiAgICAgIHN1cmZhY2U6ICdyZ2JhKDI1NSwgMTA3LCA1MywgMC4wNSknLCAvLyBTdWJ0bGUgb3JhbmdlIHdhc2hcbiAgICAgIHRleHQ6ICcjMkQxQjE0JywgLy8gUmljaCBicm93biB0ZXh0XG4gICAgICB0ZXh0U2Vjb25kYXJ5OiAnIzVENDAzNycsIC8vIE1lZGl1bSBicm93blxuICAgICAgdGV4dE11dGVkOiAnIzhENkU2MycsIC8vIExpZ2h0IGJyb3duXG4gICAgICBhY2NlbnQ6ICcjRkY2QjM1JywgLy8gVmlicmFudCBvcmFuZ2VcbiAgICAgIGFjY2VudFNvZnQ6ICcjRkY4QTY1JywgLy8gU29mdGVyIG9yYW5nZVxuICAgICAgYWNjZW50TGlnaHQ6ICcjRkZGN0Y0JywgLy8gVmVyeSBsaWdodCBvcmFuZ2UgYmFja2dyb3VuZFxuICAgICAgYm9yZGVyOiAncmdiYSgyNTUsIDEwNywgNTMsIDAuMTUpJywgLy8gU3VidGxlIG9yYW5nZSBib3JkZXJcbiAgICAgIGhvdmVyOiAncmdiYSgyNTUsIDEwNywgNTMsIDAuMDgpJywgLy8gTGlnaHQgb3JhbmdlIGhvdmVyXG4gICAgICBkaXZpZGVyOiAncmdiYSgyNTUsIDEwNywgNTMsIDAuMiknLCAvLyBPcmFuZ2UgZGl2aWRlclxuICAgICAgZ2xvdzogJ3JnYmEoMjU1LCAxMDcsIDUzLCAwLjI1KScgLy8gT3JhbmdlIGdsb3dcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgbWVudUl0ZW1zID0gW1xuICAgIHtcbiAgICAgIHNlY3Rpb246ICdXT1JLU1BBQ0UnLFxuICAgICAgaXRlbXM6IFtcbiAgICAgICAgeyBocmVmOiAnLycsIGxhYmVsOiAnQnJpZWZpbmcgUm9vbScsIGljb246IEhvbWUgfSxcbiAgICAgICAgeyBocmVmOiAnL3R3ZWV0LWNlbnRlcicsIGxhYmVsOiAnRHJhZnRpbmcgRGVzaycsIGljb246IE1lc3NhZ2VDaXJjbGUgfSxcbiAgICAgICAgeyBocmVmOiAnL3NjaGVkdWxlJywgbGFiZWw6ICdDb250ZW50IFNjaGVkdWxlcicsIGljb246IENhbGVuZGFyIH0sXG4gICAgICAgIHsgaHJlZjogJy9kYXNoYm9hcmQnLCBsYWJlbDogJ0dyb3d0aCBMYWInLCBpY29uOiBCYXJDaGFydDMgfSxcbiAgICAgICAgeyBocmVmOiAnL21lZXRpbmcnLCBsYWJlbDogJ0FJIE1lZXRpbmdzJywgaWNvbjogVmlkZW8gfVxuICAgICAgXVxuICAgIH0sXG4gICAge1xuICAgICAgc2VjdGlvbjogJ1NFVFRJTkdTJyxcbiAgICAgIGl0ZW1zOiBbXG4gICAgICAgIHsgaHJlZjogJy9zZXR0aW5ncycsIGxhYmVsOiAnU2V0dGluZ3MnLCBpY29uOiBVc2VyIH1cbiAgICAgIF1cbiAgICB9XG4gIF07XG5cbiAgY29uc3QgaXNBY3RpdmUgPSAoaHJlZjogc3RyaW5nKSA9PiB7XG4gICAgaWYgKGhyZWYgPT09ICcvJykge1xuICAgICAgcmV0dXJuIHJvdXRlci5wYXRobmFtZSA9PT0gJy8nO1xuICAgIH1cbiAgICByZXR1cm4gcm91dGVyLnBhdGhuYW1lLnN0YXJ0c1dpdGgoaHJlZik7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICBtaW5IZWlnaHQ6ICcxMDB2aCcsXG4gICAgICBiYWNrZ3JvdW5kOiBjb2xvcnMuYmFja2dyb3VuZCxcbiAgICAgIHBhZGRpbmc6ICcxNnB4JyxcbiAgICAgIGdhcDogJzE2cHgnXG4gICAgfX0+XG4gICAgICA8YXNpZGUgc3R5bGU9e3tcbiAgICAgICAgd2lkdGg6ICcyMDBweCcsIC8vIENsZWFuIGFuZCBzcGFjaW91cyBsaWtlIHJlZmVyZW5jZVxuICAgICAgICBiYWNrZ3JvdW5kOiBjb2xvcnMuc2lkZWJhci5iYWNrZ3JvdW5kLFxuICAgICAgICBoZWlnaHQ6ICdjYWxjKDEwMHZoIC0gMzJweCknLFxuICAgICAgICBib3JkZXJSYWRpdXM6ICcyNHB4JyxcbiAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyxcbiAgICAgICAgYm94U2hhZG93OiBgMCA4cHggMzJweCAke2NvbG9ycy5zaWRlYmFyLmdsb3d9LCAwIDJweCA4cHggcmdiYSgyNTUsIDEwNywgNTMsIDAuMSlgLFxuICAgICAgICBib3JkZXI6IGAxcHggc29saWQgJHtjb2xvcnMuc2lkZWJhci5ib3JkZXJ9YCxcbiAgICAgICAgb3ZlcmZsb3c6ICdoaWRkZW4nLFxuICAgICAgICBwb3NpdGlvbjogJ3JlbGF0aXZlJ1xuICAgICAgfX0+XG4gICAgICAgIHsvKiBMb2dvIFNlY3Rpb24gKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBwYWRkaW5nOiAnMzJweCAyMHB4IDI0cHggMjBweCcsXG4gICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBjb2xvcnMuc2lkZWJhci5iYWNrZ3JvdW5kXG4gICAgICAgIH19PlxuICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgIHNyYz1cImh0dHBzOi8vbmxja2Ftc3JkaXdreXlyeHpudGYuc3VwYWJhc2UuY28vc3RvcmFnZS92MS9vYmplY3Qvc2lnbi9sb2dvcy9lbG9nb3MucG5nP3Rva2VuPWV5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNJc0ltdHBaQ0k2SW5OMGIzSmhaMlV0ZFhKc0xYTnBaMjVwYm1jdGEyVjVYMlJpTlRFMFl6RTVMVGxoTlRRdE5HWmlOeTFoTWpZM0xUSm1Oalk1WkRsaFpqWTFPQ0o5LmV5SjFjbXdpT2lKc2IyZHZjeTlsYkc5bmIzTXVjRzVuSWl3aWFXRjBJam94TnpRNE1qazJOREl5TENKbGVIQWlPakUzTnprNE16STBNako5LkZKbEVSdmJIWVJzbS00WHBVeUZLWTFfeG5GVjk4OEdCNlg5TTN2TWFyakVcIlxuICAgICAgICAgICAgYWx0PVwiRXhpZSBMb2dvXCJcbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGhlaWdodDogJzQycHgnLFxuICAgICAgICAgICAgICB3aWR0aDogJ2F1dG8nLFxuICAgICAgICAgICAgICBvYmplY3RGaXQ6ICdjb250YWluJ1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU2VhcmNoIEJhciAqL31cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIHBhZGRpbmc6ICcwIDE2cHggMTZweCAxNnB4J1xuICAgICAgICB9fT5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBwb3NpdGlvbjogJ3JlbGF0aXZlJyxcbiAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8U2VhcmNoXG4gICAgICAgICAgICAgIHNpemU9ezE0fVxuICAgICAgICAgICAgICBjb2xvcj17Y29sb3JzLnNpZGViYXIudGV4dE11dGVkfVxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgICAgICAgICAgICAgIGxlZnQ6ICcxMnB4JyxcbiAgICAgICAgICAgICAgICB6SW5kZXg6IDFcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaC4uLlwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAnOHB4IDEycHggOHB4IDM2cHgnLFxuICAgICAgICAgICAgICAgIGJvcmRlcjogYDFweCBzb2xpZCAke2NvbG9ycy5zaWRlYmFyLmJvcmRlcn1gLFxuICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogY29sb3JzLnNpZGViYXIuc3VyZmFjZSxcbiAgICAgICAgICAgICAgICBjb2xvcjogY29sb3JzLnNpZGViYXIudGV4dCxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEycHgnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc0MDAnLFxuICAgICAgICAgICAgICAgIG91dGxpbmU6ICdub25lJyxcbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuMnMgZWFzZSdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgb25Gb2N1cz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5ib3JkZXJDb2xvciA9IGNvbG9ycy5zaWRlYmFyLmFjY2VudDtcbiAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gY29sb3JzLnNpZGViYXIuYmFja2dyb3VuZDtcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgb25CbHVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJvcmRlckNvbG9yID0gY29sb3JzLnNpZGViYXIuYm9yZGVyO1xuICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSBjb2xvcnMuc2lkZWJhci5zdXJmYWNlO1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE5hdmlnYXRpb24gKi99XG4gICAgICAgIDxuYXYgc3R5bGU9e3tcbiAgICAgICAgICBwYWRkaW5nOiAnOHB4IDE2cHgnLFxuICAgICAgICAgIGZsZXg6IDEsXG4gICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nXG4gICAgICAgIH19PlxuICAgICAgICAgIHttZW51SXRlbXMubWFwKChzZWN0aW9uLCBzZWN0aW9uSW5kZXgpID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXtzZWN0aW9uLnNlY3Rpb259IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogc2VjdGlvbkluZGV4IDwgbWVudUl0ZW1zLmxlbmd0aCAtIDEgPyAnMjRweCcgOiAnMCcgfX0+XG4gICAgICAgICAgICAgIHsvKiBTdWJ0bGUgZGl2aWRlciBiZWZvcmUgc2V0dGluZ3Mgc2VjdGlvbiAqL31cbiAgICAgICAgICAgICAge3NlY3Rpb25JbmRleCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIGhlaWdodDogJzFweCcsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBgbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCwgJHtjb2xvcnMuc2lkZWJhci5kaXZpZGVyfSwgdHJhbnNwYXJlbnQpYCxcbiAgICAgICAgICAgICAgICAgIG1hcmdpbjogJzE2cHggMTJweCAyMHB4IDEycHgnXG4gICAgICAgICAgICAgICAgfX0gLz5cbiAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICB7LyogU2VjdGlvbiBIZWFkZXIgKi99XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEwcHgnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMuc2lkZWJhci50ZXh0TXV0ZWQsXG4gICAgICAgICAgICAgICAgdGV4dFRyYW5zZm9ybTogJ3VwcGVyY2FzZScsXG4gICAgICAgICAgICAgICAgbGV0dGVyU3BhY2luZzogJzFweCcsXG4gICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMTBweCcsXG4gICAgICAgICAgICAgICAgcGFkZGluZ0xlZnQ6ICcxMnB4J1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICB7c2VjdGlvbi5zZWN0aW9ufVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogU2VjdGlvbiBJdGVtcyAqL31cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLCBnYXA6ICcycHgnIH19PlxuICAgICAgICAgICAgICAgIHtzZWN0aW9uLml0ZW1zLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgICAgICAgICAgY29uc3QgYWN0aXZlID0gaXNBY3RpdmUoaXRlbS5ocmVmKTtcbiAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgIDxMaW5rIGtleT17aXRlbS5ocmVmfSBocmVmPXtpdGVtLmhyZWZ9IHN0eWxlPXt7IHRleHREZWNvcmF0aW9uOiAnbm9uZScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnOHB4IDEycHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGFjdGl2ZSA/IGNvbG9ycy5zaWRlYmFyLnN1cmZhY2UgOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuMnMgZWFzZScsXG4gICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ3JlbGF0aXZlJ1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFhY3RpdmUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSBjb2xvcnMuc2lkZWJhci5ob3ZlcjtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghYWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gJ3RyYW5zcGFyZW50JztcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBBY3RpdmUgaW5kaWNhdG9yICovfVxuICAgICAgICAgICAgICAgICAgICAgICAge2FjdGl2ZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZWZ0OiAnMCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdG9wOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGVZKC01MCUpJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzJweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMTZweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogY29sb3JzLnNpZGViYXIuYWNjZW50LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAgMnB4IDJweCAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIEljb24gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMTZweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzE2cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpblJpZ2h0OiAnMTBweCdcbiAgICAgICAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aXRlbS5pY29uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT17MTR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9e2FjdGl2ZSA/IGNvbG9ycy5zaWRlYmFyLmFjY2VudCA6IGNvbG9ycy5zaWRlYmFyLnRleHRTZWNvbmRhcnl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIExhYmVsICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IGFjdGl2ZSA/IGNvbG9ycy5zaWRlYmFyLnRleHQgOiBjb2xvcnMuc2lkZWJhci50ZXh0U2Vjb25kYXJ5LFxuICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEzcHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiBhY3RpdmUgPyAnNTAwJyA6ICc0MDAnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBsZXR0ZXJTcGFjaW5nOiAnMC4xcHgnXG4gICAgICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvbmF2PlxuXG4gICAgICAgIHsvKiBCb3R0b20gZGl2aWRlciAqL31cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIG1hcmdpbjogJzAgMTZweCAxNnB4IDE2cHgnLFxuICAgICAgICAgIGhlaWdodDogJzFweCcsXG4gICAgICAgICAgYmFja2dyb3VuZDogYGxpbmVhci1ncmFkaWVudCg5MGRlZywgdHJhbnNwYXJlbnQsICR7Y29sb3JzLnNpZGViYXIuZGl2aWRlcn0sIHRyYW5zcGFyZW50KWBcbiAgICAgICAgfX0gLz5cblxuICAgICAgICB7LyogQWNjb3VudCBTZWN0aW9uICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgcGFkZGluZzogJzEycHggMTZweCAyMHB4IDE2cHgnXG4gICAgICAgIH19PlxuICAgICAgICAgIHt1c2VyICYmIHVzZXJEYXRhID8gKFxuICAgICAgICAgICAgLy8gQXV0aGVudGljYXRlZCB1c2VyIC0gc2hvdyBwcm9maWxlXG4gICAgICAgICAgICA8TGluayBocmVmPVwiL3NldHRpbmdzXCIgc3R5bGU9e3sgdGV4dERlY29yYXRpb246ICdub25lJyB9fT5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICBnYXA6ICcxMnB4JyxcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMTJweCcsXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMnB4JyxcbiAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuMnMgZWFzZScsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZSdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gY29sb3JzLnNpZGViYXIuaG92ZXI7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICd0cmFuc3BhcmVudCc7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzM2cHgnLFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMzZweCcsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB1c2VyRGF0YS5hdmF0YXIgPyAndHJhbnNwYXJlbnQnIDogY29sb3JzLnNpZGViYXIuc3VyZmFjZSxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxuICAgICAgICAgICAgICAgICAgb3ZlcmZsb3c6ICdoaWRkZW4nLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiBgMXB4IHNvbGlkICR7Y29sb3JzLnNpZGViYXIuYm9yZGVyfWBcbiAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgIHt1c2VyRGF0YS5hdmF0YXIgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgICBzcmM9e3VzZXJEYXRhLmF2YXRhcn1cbiAgICAgICAgICAgICAgICAgICAgICBhbHQ9e3VzZXJEYXRhLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMTAwJScsXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICcxMDAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIG9iamVjdEZpdDogJ2NvdmVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJSdcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIG9uRXJyb3I9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBGYWxsYmFjayB0byBpY29uIGlmIGltYWdlIGZhaWxzIHRvIGxvYWRcbiAgICAgICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xuICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnBhcmVudEVsZW1lbnQhLnN0eWxlLmJhY2tncm91bmQgPSBjb2xvcnMuc2lkZWJhci5zdXJmYWNlO1xuICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnBhcmVudEVsZW1lbnQhLmlubmVySFRNTCA9IGA8c3ZnIHdpZHRoPVwiMThcIiBoZWlnaHQ9XCIxOFwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cIiR7Y29sb3JzLnNpZGViYXIudGV4dFNlY29uZGFyeX1cIiBzdHJva2Utd2lkdGg9XCIxLjVcIiBzdHJva2UtbGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlLWxpbmVqb2luPVwicm91bmRcIj48cGF0aCBkPVwiTTIwIDIxdi0yYTQgNCAwIDAgMC00LTRIOGE0IDQgMCAwIDAtNCA0djJcIj48L3BhdGg+PGNpcmNsZSBjeD1cIjEyXCIgY3k9XCI3XCIgcj1cIjRcIj48L2NpcmNsZT48L3N2Zz5gO1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8VXNlciBzaXplPXsxOH0gY29sb3I9e2NvbG9ycy5zaWRlYmFyLnRleHRTZWNvbmRhcnl9IHN0cm9rZVdpZHRoPXsxLjV9IC8+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgey8qIE9ubGluZSBzdGF0dXMgZG90IHdpdGggZ3JlZW4gZ2xvdyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgICAgICAgICAgICAgIGJvdHRvbTogJzJweCcsXG4gICAgICAgICAgICAgICAgICAgIHJpZ2h0OiAnMnB4JyxcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICc4cHgnLFxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICc4cHgnLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzEwQjk4MScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJzJweCBzb2xpZCB3aGl0ZScsXG4gICAgICAgICAgICAgICAgICAgIGJveFNoYWRvdzogJzAgMCA4cHggcmdiYSgxNiwgMTg1LCAxMjksIDAuNiksIDAgMCA0cHggcmdiYSgxNiwgMTg1LCAxMjksIDAuNCknXG4gICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZmxleDogMSB9fT5cbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6IGNvbG9ycy5zaWRlYmFyLnRleHQsXG4gICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTNweCcsXG4gICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAnMS4yJyxcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMnB4J1xuICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgIHt1c2VyRGF0YS5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMuc2lkZWJhci50ZXh0TXV0ZWQsXG4gICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTFweCcsXG4gICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc1MDAnLFxuICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAnMS4yJyxcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgZ2FwOiAnNHB4J1xuICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPnt1c2VyRGF0YS5wbGFufSBQbGFuPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBjb2xvcjogY29sb3JzLnNpZGViYXIuYWNjZW50IH19PuKAojwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+U2V0dGluZ3M8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIC8vIE5vdCBhdXRoZW50aWNhdGVkIC0gc2hvdyBzaWduIGluIGJ1dHRvblxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93QXV0aE1vZGFsKHRydWUpfVxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAnOHB4IDEycHgnLFxuICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjJzIGVhc2UnLFxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICd0cmFuc3BhcmVudCdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gY29sb3JzLnNpZGViYXIuaG92ZXI7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICd0cmFuc3BhcmVudCc7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHsvKiBJY29uICovfVxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgd2lkdGg6ICcxNnB4JyxcbiAgICAgICAgICAgICAgICBoZWlnaHQ6ICcxNnB4JyxcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgIG1hcmdpblJpZ2h0OiAnMTBweCdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgPExvZ0luXG4gICAgICAgICAgICAgICAgICBzaXplPXsxNH1cbiAgICAgICAgICAgICAgICAgIGNvbG9yPXtjb2xvcnMuc2lkZWJhci50ZXh0U2Vjb25kYXJ5fVxuICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIExhYmVsICovfVxuICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMuc2lkZWJhci50ZXh0U2Vjb25kYXJ5LFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTNweCcsXG4gICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzQwMCcsXG4gICAgICAgICAgICAgICAgbGV0dGVyU3BhY2luZzogJzAuMXB4J1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICBTaWduIEluXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9hc2lkZT5cblxuICAgICAgPG1haW4gc3R5bGU9e3tcbiAgICAgICAgZmxleEdyb3c6IDEsXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogY29sb3JzLnN1cmZhY2UsXG4gICAgICAgIGJvcmRlclJhZGl1czogJzI0cHgnLFxuICAgICAgICBoZWlnaHQ6ICdjYWxjKDEwMHZoIC0gMzJweCknLFxuICAgICAgICBwb3NpdGlvbjogJ3JlbGF0aXZlJyxcbiAgICAgICAgYm94U2hhZG93OiAnMCA0cHggMjBweCByZ2JhKDI1NSwgMTA3LCA1MywgMC4wOCknLFxuICAgICAgICBib3JkZXI6IGAxcHggc29saWQgJHtjb2xvcnMuc2lkZWJhci5ib3JkZXJ9YCxcbiAgICAgICAgb3ZlcmZsb3c6ICdoaWRkZW4nXG4gICAgICB9fT5cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIHBhZGRpbmc6ICczMnB4JyxcbiAgICAgICAgICBoZWlnaHQ6ICcxMDAlJyxcbiAgICAgICAgICBvdmVyZmxvdzogJ2F1dG8nXG4gICAgICAgIH19PlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L21haW4+XG5cbiAgICAgIHsvKiBBdXRoZW50aWNhdGlvbiBNb2RhbCAqL31cbiAgICAgIDxBdXRoTW9kYWxcbiAgICAgICAgaXNPcGVuPXtzaG93QXV0aE1vZGFsfVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93QXV0aE1vZGFsKGZhbHNlKX1cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBTaWRlYmFyTGF5b3V0O1xuIl0sIm5hbWVzIjpbIkxpbmsiLCJSZWFjdCIsInVzZVN0YXRlIiwidXNlUm91dGVyIiwiSG9tZSIsIkJhckNoYXJ0MyIsIk1lc3NhZ2VDaXJjbGUiLCJWaWRlbyIsIlVzZXIiLCJMb2dJbiIsIlNlYXJjaCIsIkNhbGVuZGFyIiwidXNlVXNlciIsIkF1dGhNb2RhbCIsIlNpZGViYXJMYXlvdXQiLCJjaGlsZHJlbiIsInJvdXRlciIsInVzZXIiLCJwcm9maWxlIiwibG9hZGluZyIsInNob3dBdXRoTW9kYWwiLCJzZXRTaG93QXV0aE1vZGFsIiwiY29uc29sZSIsImxvZyIsInVzZXJFbWFpbCIsImVtYWlsIiwicHJvZmlsZU5hbWUiLCJmdWxsX25hbWUiLCJ1c2VyRGF0YSIsIm5hbWUiLCJ1c2VyX21ldGFkYXRhIiwic3BsaXQiLCJwbGFuIiwiYXZhdGFyIiwiYXZhdGFyX3VybCIsImlzT25saW5lIiwiaXNfb25saW5lIiwiY29sb3JzIiwicHJpbWFyeSIsInByaW1hcnlMaWdodCIsImJhY2tncm91bmQiLCJzdXJmYWNlIiwidGV4dCIsInNlY29uZGFyeSIsInRlcnRpYXJ5Iiwic2lkZWJhciIsInRleHRTZWNvbmRhcnkiLCJ0ZXh0TXV0ZWQiLCJhY2NlbnQiLCJhY2NlbnRTb2Z0IiwiYWNjZW50TGlnaHQiLCJib3JkZXIiLCJob3ZlciIsImRpdmlkZXIiLCJnbG93IiwibWVudUl0ZW1zIiwic2VjdGlvbiIsIml0ZW1zIiwiaHJlZiIsImxhYmVsIiwiaWNvbiIsImlzQWN0aXZlIiwicGF0aG5hbWUiLCJzdGFydHNXaXRoIiwiZGl2Iiwic3R5bGUiLCJkaXNwbGF5IiwibWluSGVpZ2h0IiwicGFkZGluZyIsImdhcCIsImFzaWRlIiwid2lkdGgiLCJoZWlnaHQiLCJib3JkZXJSYWRpdXMiLCJmbGV4RGlyZWN0aW9uIiwiYm94U2hhZG93Iiwib3ZlcmZsb3ciLCJwb3NpdGlvbiIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsImltZyIsInNyYyIsImFsdCIsIm9iamVjdEZpdCIsInNpemUiLCJjb2xvciIsImxlZnQiLCJ6SW5kZXgiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsImZvbnRTaXplIiwiZm9udFdlaWdodCIsIm91dGxpbmUiLCJ0cmFuc2l0aW9uIiwib25Gb2N1cyIsImUiLCJ0YXJnZXQiLCJib3JkZXJDb2xvciIsIm9uQmx1ciIsIm5hdiIsImZsZXgiLCJtYXAiLCJzZWN0aW9uSW5kZXgiLCJtYXJnaW5Cb3R0b20iLCJsZW5ndGgiLCJtYXJnaW4iLCJ0ZXh0VHJhbnNmb3JtIiwibGV0dGVyU3BhY2luZyIsInBhZGRpbmdMZWZ0IiwiaXRlbSIsImFjdGl2ZSIsInRleHREZWNvcmF0aW9uIiwiY3Vyc29yIiwib25Nb3VzZUVudGVyIiwiY3VycmVudFRhcmdldCIsIm9uTW91c2VMZWF2ZSIsInRvcCIsInRyYW5zZm9ybSIsIm1hcmdpblJpZ2h0Iiwic3Ryb2tlV2lkdGgiLCJzcGFuIiwib25FcnJvciIsInBhcmVudEVsZW1lbnQiLCJpbm5lckhUTUwiLCJib3R0b20iLCJyaWdodCIsImxpbmVIZWlnaHQiLCJvbkNsaWNrIiwibWFpbiIsImZsZXhHcm93IiwiYmFja2dyb3VuZENvbG9yIiwiaXNPcGVuIiwib25DbG9zZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/SidebarLayoutSimple.tsx\n");

/***/ }),

/***/ "./contexts/UserContext.tsx":
/*!**********************************!*\
  !*** ./contexts/UserContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"./lib/supabase.ts\");\n\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useUser = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n};\nconst UserProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                await fetchUserProfile(session.user.id);\n                await updateOnlineStatus(true);\n            }\n            setLoading(false);\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state change:\", {\n                event,\n                user: !!session?.user,\n                email: session?.user?.email\n            });\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                console.log(\"User authenticated, fetching profile...\");\n                await fetchUserProfile(session.user.id);\n                await updateOnlineStatus(true);\n            } else {\n                console.log(\"User signed out, clearing profile\");\n                setProfile(null);\n            }\n            setLoading(false);\n        });\n        // Update online status when user leaves\n        const handleBeforeUnload = ()=>{\n            if (user) {\n                updateOnlineStatus(false);\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>{\n            subscription.unsubscribe();\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        user\n    ]);\n    const fetchUserProfile = async (userId)=>{\n        try {\n            console.log(\"Fetching user profile for userId:\", userId);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").select(\"*\").eq(\"user_id\", userId).single();\n            console.log(\"Profile fetch result:\", {\n                data,\n                error\n            });\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error fetching user profile:\", error);\n                return;\n            }\n            if (data) {\n                console.log(\"Setting profile data:\", data);\n                setProfile(data);\n            } else {\n                // Create default profile if it doesn't exist\n                console.log(\"Creating new profile for user:\", userId);\n                const { data: newProfile, error: createError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").insert({\n                    user_id: userId,\n                    full_name: user?.user_metadata?.full_name || \"User\",\n                    plan: \"free\",\n                    subscription_status: \"inactive\"\n                }).select().single();\n                console.log(\"Profile creation result:\", {\n                    newProfile,\n                    createError\n                });\n                if (createError) {\n                    console.error(\"Error creating user profile:\", createError);\n                } else {\n                    console.log(\"Setting new profile:\", newProfile);\n                    setProfile(newProfile);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error in fetchUserProfile:\", error);\n        }\n    };\n    const signIn = async (email, password)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            error\n        };\n    };\n    const signUp = async (email, password, fullName)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    full_name: fullName\n                }\n            }\n        });\n        return {\n            error\n        };\n    };\n    const signOut = async ()=>{\n        await updateOnlineStatus(false);\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) return {\n            error: \"No user logged in\"\n        };\n        console.log(\"Updating profile for user:\", user.id, \"with updates:\", updates);\n        try {\n            // First, try to update the profile\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").update(updates).eq(\"user_id\", user.id).select().single();\n            console.log(\"Update result:\", {\n                data,\n                error\n            });\n            if (error) {\n                // If the profile doesn't exist, try to create it first\n                if (error.code === \"PGRST116\") {\n                    console.log(\"Profile not found, creating new profile...\");\n                    const { data: newProfile, error: createError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").insert({\n                        user_id: user.id,\n                        full_name: user?.user_metadata?.full_name || \"User\",\n                        plan: \"free\",\n                        subscription_status: \"inactive\",\n                        ...updates\n                    }).select().single();\n                    if (createError) {\n                        console.error(\"Error creating profile:\", createError);\n                        return {\n                            error: createError\n                        };\n                    } else {\n                        console.log(\"Profile created successfully:\", newProfile);\n                        setProfile(newProfile);\n                        return {\n                            error: null\n                        };\n                    }\n                } else {\n                    console.error(\"Error updating profile:\", error);\n                    return {\n                        error\n                    };\n                }\n            } else {\n                console.log(\"Profile updated successfully:\", data);\n                setProfile(data);\n                return {\n                    error: null\n                };\n            }\n        } catch (error) {\n            console.error(\"Unexpected error in updateProfile:\", error);\n            return {\n                error\n            };\n        }\n    };\n    const updateOnlineStatus = async (isOnline)=>{\n        if (!user) return;\n        try {\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").update({\n                is_online: isOnline,\n                last_seen: new Date().toISOString()\n            }).eq(\"user_id\", user.id);\n        } catch (error) {\n            console.error(\"Error updating online status:\", error);\n        }\n    };\n    const value = {\n        user,\n        profile,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        updateProfile,\n        updateOnlineStatus\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/contexts/UserContext.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250ZXh0cy9Vc2VyQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBNkU7QUFFaEI7QUFhN0QsTUFBTU0sNEJBQWNMLG9EQUFhQSxDQUE4Qk07QUFFeEQsTUFBTUMsVUFBVTtJQUNyQixNQUFNQyxVQUFVUCxpREFBVUEsQ0FBQ0k7SUFDM0IsSUFBSUcsWUFBWUYsV0FBVztRQUN6QixNQUFNLElBQUlHLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNULEVBQUM7QUFFTSxNQUFNRSxlQUF3RCxDQUFDLEVBQUVDLFFBQVEsRUFBRTtJQUNoRixNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR1YsK0NBQVFBLENBQXNCO0lBQ3RELE1BQU0sQ0FBQ1csU0FBU0MsV0FBVyxHQUFHWiwrQ0FBUUEsQ0FBcUI7SUFDM0QsTUFBTSxDQUFDYSxTQUFTQyxXQUFXLEdBQUdkLCtDQUFRQSxDQUFDO0lBRXZDRCxnREFBU0EsQ0FBQztRQUNSLHNCQUFzQjtRQUN0QixNQUFNZ0Isb0JBQW9CO1lBQ3hCLE1BQU0sRUFBRUMsTUFBTSxFQUFFQyxPQUFPLEVBQUUsRUFBRSxHQUFHLE1BQU1oQixtREFBUUEsQ0FBQ2lCLElBQUksQ0FBQ0MsVUFBVTtZQUM1RFQsUUFBUU8sU0FBU1IsUUFBUTtZQUV6QixJQUFJUSxTQUFTUixNQUFNO2dCQUNqQixNQUFNVyxpQkFBaUJILFFBQVFSLElBQUksQ0FBQ1ksRUFBRTtnQkFDdEMsTUFBTUMsbUJBQW1CO1lBQzNCO1lBRUFSLFdBQVc7UUFDYjtRQUVBQztRQUVBLDBCQUEwQjtRQUMxQixNQUFNLEVBQUVDLE1BQU0sRUFBRU8sWUFBWSxFQUFFLEVBQUUsR0FBR3RCLG1EQUFRQSxDQUFDaUIsSUFBSSxDQUFDTSxpQkFBaUIsQ0FDaEUsT0FBT0MsT0FBT1I7WUFDWlMsUUFBUUMsR0FBRyxDQUFDLHNCQUFzQjtnQkFBRUY7Z0JBQU9oQixNQUFNLENBQUMsQ0FBQ1EsU0FBU1I7Z0JBQU1tQixPQUFPWCxTQUFTUixNQUFNbUI7WUFBTTtZQUM5RmxCLFFBQVFPLFNBQVNSLFFBQVE7WUFFekIsSUFBSVEsU0FBU1IsTUFBTTtnQkFDakJpQixRQUFRQyxHQUFHLENBQUM7Z0JBQ1osTUFBTVAsaUJBQWlCSCxRQUFRUixJQUFJLENBQUNZLEVBQUU7Z0JBQ3RDLE1BQU1DLG1CQUFtQjtZQUMzQixPQUFPO2dCQUNMSSxRQUFRQyxHQUFHLENBQUM7Z0JBQ1pmLFdBQVc7WUFDYjtZQUVBRSxXQUFXO1FBQ2I7UUFHRix3Q0FBd0M7UUFDeEMsTUFBTWUscUJBQXFCO1lBQ3pCLElBQUlwQixNQUFNO2dCQUNSYSxtQkFBbUI7WUFDckI7UUFDRjtRQUVBUSxPQUFPQyxnQkFBZ0IsQ0FBQyxnQkFBZ0JGO1FBRXhDLE9BQU87WUFDTE4sYUFBYVMsV0FBVztZQUN4QkYsT0FBT0csbUJBQW1CLENBQUMsZ0JBQWdCSjtRQUM3QztJQUNGLEdBQUc7UUFBQ3BCO0tBQUs7SUFFVCxNQUFNVyxtQkFBbUIsT0FBT2M7UUFDOUIsSUFBSTtZQUNGUixRQUFRQyxHQUFHLENBQUMscUNBQXFDTztZQUNqRCxNQUFNLEVBQUVsQixJQUFJLEVBQUVtQixLQUFLLEVBQUUsR0FBRyxNQUFNbEMsbURBQVFBLENBQ25DbUMsSUFBSSxDQUFDLGlCQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLFdBQVdKLFFBQ2RLLE1BQU07WUFFVGIsUUFBUUMsR0FBRyxDQUFDLHlCQUF5QjtnQkFBRVg7Z0JBQU1tQjtZQUFNO1lBRW5ELElBQUlBLFNBQVNBLE1BQU1LLElBQUksS0FBSyxZQUFZO2dCQUN0Q2QsUUFBUVMsS0FBSyxDQUFDLGdDQUFnQ0E7Z0JBQzlDO1lBQ0Y7WUFFQSxJQUFJbkIsTUFBTTtnQkFDUlUsUUFBUUMsR0FBRyxDQUFDLHlCQUF5Qlg7Z0JBQ3JDSixXQUFXSTtZQUNiLE9BQU87Z0JBQ0wsNkNBQTZDO2dCQUM3Q1UsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQ087Z0JBQzlDLE1BQU0sRUFBRWxCLE1BQU15QixVQUFVLEVBQUVOLE9BQU9PLFdBQVcsRUFBRSxHQUFHLE1BQU16QyxtREFBUUEsQ0FDNURtQyxJQUFJLENBQUMsaUJBQ0xPLE1BQU0sQ0FBQztvQkFDTkMsU0FBU1Y7b0JBQ1RXLFdBQVdwQyxNQUFNcUMsZUFBZUQsYUFBYTtvQkFDN0NFLE1BQU07b0JBQ05DLHFCQUFxQjtnQkFDdkIsR0FDQ1gsTUFBTSxHQUNORSxNQUFNO2dCQUVUYixRQUFRQyxHQUFHLENBQUMsNEJBQTRCO29CQUFFYztvQkFBWUM7Z0JBQVk7Z0JBRWxFLElBQUlBLGFBQWE7b0JBQ2ZoQixRQUFRUyxLQUFLLENBQUMsZ0NBQWdDTztnQkFDaEQsT0FBTztvQkFDTGhCLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0JjO29CQUNwQzdCLFdBQVc2QjtnQkFDYjtZQUNGO1FBQ0YsRUFBRSxPQUFPTixPQUFPO1lBQ2RULFFBQVFTLEtBQUssQ0FBQyw4QkFBOEJBO1FBQzlDO0lBQ0Y7SUFFQSxNQUFNYyxTQUFTLE9BQU9yQixPQUFlc0I7UUFDbkMsTUFBTSxFQUFFZixLQUFLLEVBQUUsR0FBRyxNQUFNbEMsbURBQVFBLENBQUNpQixJQUFJLENBQUNpQyxrQkFBa0IsQ0FBQztZQUN2RHZCO1lBQ0FzQjtRQUNGO1FBQ0EsT0FBTztZQUFFZjtRQUFNO0lBQ2pCO0lBRUEsTUFBTWlCLFNBQVMsT0FBT3hCLE9BQWVzQixVQUFrQkc7UUFDckQsTUFBTSxFQUFFbEIsS0FBSyxFQUFFLEdBQUcsTUFBTWxDLG1EQUFRQSxDQUFDaUIsSUFBSSxDQUFDa0MsTUFBTSxDQUFDO1lBQzNDeEI7WUFDQXNCO1lBQ0FJLFNBQVM7Z0JBQ1B0QyxNQUFNO29CQUNKNkIsV0FBV1E7Z0JBQ2I7WUFDRjtRQUNGO1FBQ0EsT0FBTztZQUFFbEI7UUFBTTtJQUNqQjtJQUVBLE1BQU1vQixVQUFVO1FBQ2QsTUFBTWpDLG1CQUFtQjtRQUN6QixNQUFNckIsbURBQVFBLENBQUNpQixJQUFJLENBQUNxQyxPQUFPO0lBQzdCO0lBRUEsTUFBTUMsZ0JBQWdCLE9BQU9DO1FBQzNCLElBQUksQ0FBQ2hELE1BQU0sT0FBTztZQUFFMEIsT0FBTztRQUFvQjtRQUUvQ1QsUUFBUUMsR0FBRyxDQUFDLDhCQUE4QmxCLEtBQUtZLEVBQUUsRUFBRSxpQkFBaUJvQztRQUVwRSxJQUFJO1lBQ0YsbUNBQW1DO1lBQ25DLE1BQU0sRUFBRXpDLElBQUksRUFBRW1CLEtBQUssRUFBRSxHQUFHLE1BQU1sQyxtREFBUUEsQ0FDbkNtQyxJQUFJLENBQUMsaUJBQ0xzQixNQUFNLENBQUNELFNBQ1BuQixFQUFFLENBQUMsV0FBVzdCLEtBQUtZLEVBQUUsRUFDckJnQixNQUFNLEdBQ05FLE1BQU07WUFFVGIsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQjtnQkFBRVg7Z0JBQU1tQjtZQUFNO1lBRTVDLElBQUlBLE9BQU87Z0JBQ1QsdURBQXVEO2dCQUN2RCxJQUFJQSxNQUFNSyxJQUFJLEtBQUssWUFBWTtvQkFDN0JkLFFBQVFDLEdBQUcsQ0FBQztvQkFDWixNQUFNLEVBQUVYLE1BQU15QixVQUFVLEVBQUVOLE9BQU9PLFdBQVcsRUFBRSxHQUFHLE1BQU16QyxtREFBUUEsQ0FDNURtQyxJQUFJLENBQUMsaUJBQ0xPLE1BQU0sQ0FBQzt3QkFDTkMsU0FBU25DLEtBQUtZLEVBQUU7d0JBQ2hCd0IsV0FBV3BDLE1BQU1xQyxlQUFlRCxhQUFhO3dCQUM3Q0UsTUFBTTt3QkFDTkMscUJBQXFCO3dCQUNyQixHQUFHUyxPQUFPO29CQUNaLEdBQ0NwQixNQUFNLEdBQ05FLE1BQU07b0JBRVQsSUFBSUcsYUFBYTt3QkFDZmhCLFFBQVFTLEtBQUssQ0FBQywyQkFBMkJPO3dCQUN6QyxPQUFPOzRCQUFFUCxPQUFPTzt3QkFBWTtvQkFDOUIsT0FBTzt3QkFDTGhCLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBaUNjO3dCQUM3QzdCLFdBQVc2Qjt3QkFDWCxPQUFPOzRCQUFFTixPQUFPO3dCQUFLO29CQUN2QjtnQkFDRixPQUFPO29CQUNMVCxRQUFRUyxLQUFLLENBQUMsMkJBQTJCQTtvQkFDekMsT0FBTzt3QkFBRUE7b0JBQU07Z0JBQ2pCO1lBQ0YsT0FBTztnQkFDTFQsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ1g7Z0JBQzdDSixXQUFXSTtnQkFDWCxPQUFPO29CQUFFbUIsT0FBTztnQkFBSztZQUN2QjtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkVCxRQUFRUyxLQUFLLENBQUMsc0NBQXNDQTtZQUNwRCxPQUFPO2dCQUFFQTtZQUFNO1FBQ2pCO0lBQ0Y7SUFFQSxNQUFNYixxQkFBcUIsT0FBT3FDO1FBQ2hDLElBQUksQ0FBQ2xELE1BQU07UUFFWCxJQUFJO1lBQ0YsTUFBTVIsbURBQVFBLENBQ1htQyxJQUFJLENBQUMsaUJBQ0xzQixNQUFNLENBQUM7Z0JBQ05FLFdBQVdEO2dCQUNYRSxXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDbkMsR0FDQ3pCLEVBQUUsQ0FBQyxXQUFXN0IsS0FBS1ksRUFBRTtRQUMxQixFQUFFLE9BQU9jLE9BQU87WUFDZFQsUUFBUVMsS0FBSyxDQUFDLGlDQUFpQ0E7UUFDakQ7SUFDRjtJQUVBLE1BQU02QixRQUFRO1FBQ1p2RDtRQUNBRTtRQUNBRTtRQUNBb0M7UUFDQUc7UUFDQUc7UUFDQUM7UUFDQWxDO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ3BCLFlBQVkrRCxRQUFRO1FBQUNELE9BQU9BO2tCQUMxQnhEOzs7Ozs7QUFHUCxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL2NvbnRleHRzL1VzZXJDb250ZXh0LnRzeD9mNTkwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBVc2VyIGFzIFN1cGFiYXNlVXNlciB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcydcbmltcG9ydCB7IHN1cGFiYXNlLCBVc2VyLCBVc2VyUHJvZmlsZSB9IGZyb20gJy4uL2xpYi9zdXBhYmFzZSdcblxuaW50ZXJmYWNlIFVzZXJDb250ZXh0VHlwZSB7XG4gIHVzZXI6IFN1cGFiYXNlVXNlciB8IG51bGxcbiAgcHJvZmlsZTogVXNlclByb2ZpbGUgfCBudWxsXG4gIGxvYWRpbmc6IGJvb2xlYW5cbiAgc2lnbkluOiAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT4gUHJvbWlzZTx7IGVycm9yOiBhbnkgfT5cbiAgc2lnblVwOiAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZywgZnVsbE5hbWU6IHN0cmluZykgPT4gUHJvbWlzZTx7IGVycm9yOiBhbnkgfT5cbiAgc2lnbk91dDogKCkgPT4gUHJvbWlzZTx2b2lkPlxuICB1cGRhdGVQcm9maWxlOiAodXBkYXRlczogUGFydGlhbDxVc2VyUHJvZmlsZT4pID0+IFByb21pc2U8eyBlcnJvcjogYW55IH0+XG4gIHVwZGF0ZU9ubGluZVN0YXR1czogKGlzT25saW5lOiBib29sZWFuKSA9PiBQcm9taXNlPHZvaWQ+XG59XG5cbmNvbnN0IFVzZXJDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxVc2VyQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZClcblxuZXhwb3J0IGNvbnN0IHVzZVVzZXIgPSAoKSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFVzZXJDb250ZXh0KVxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VVc2VyIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBVc2VyUHJvdmlkZXInKVxuICB9XG4gIHJldHVybiBjb250ZXh0XG59XG5cbmV4cG9ydCBjb25zdCBVc2VyUHJvdmlkZXI6IFJlYWN0LkZDPHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9PiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgY29uc3QgW3VzZXIsIHNldFVzZXJdID0gdXNlU3RhdGU8U3VwYWJhc2VVc2VyIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3Byb2ZpbGUsIHNldFByb2ZpbGVdID0gdXNlU3RhdGU8VXNlclByb2ZpbGUgfCBudWxsPihudWxsKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gR2V0IGluaXRpYWwgc2Vzc2lvblxuICAgIGNvbnN0IGdldEluaXRpYWxTZXNzaW9uID0gYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgeyBkYXRhOiB7IHNlc3Npb24gfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKClcbiAgICAgIHNldFVzZXIoc2Vzc2lvbj8udXNlciA/PyBudWxsKVxuXG4gICAgICBpZiAoc2Vzc2lvbj8udXNlcikge1xuICAgICAgICBhd2FpdCBmZXRjaFVzZXJQcm9maWxlKHNlc3Npb24udXNlci5pZClcbiAgICAgICAgYXdhaXQgdXBkYXRlT25saW5lU3RhdHVzKHRydWUpXG4gICAgICB9XG5cbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuXG4gICAgZ2V0SW5pdGlhbFNlc3Npb24oKVxuXG4gICAgLy8gTGlzdGVuIGZvciBhdXRoIGNoYW5nZXNcbiAgICBjb25zdCB7IGRhdGE6IHsgc3Vic2NyaXB0aW9uIH0gfSA9IHN1cGFiYXNlLmF1dGgub25BdXRoU3RhdGVDaGFuZ2UoXG4gICAgICBhc3luYyAoZXZlbnQsIHNlc3Npb24pID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coJ0F1dGggc3RhdGUgY2hhbmdlOicsIHsgZXZlbnQsIHVzZXI6ICEhc2Vzc2lvbj8udXNlciwgZW1haWw6IHNlc3Npb24/LnVzZXI/LmVtYWlsIH0pXG4gICAgICAgIHNldFVzZXIoc2Vzc2lvbj8udXNlciA/PyBudWxsKVxuXG4gICAgICAgIGlmIChzZXNzaW9uPy51c2VyKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgYXV0aGVudGljYXRlZCwgZmV0Y2hpbmcgcHJvZmlsZS4uLicpXG4gICAgICAgICAgYXdhaXQgZmV0Y2hVc2VyUHJvZmlsZShzZXNzaW9uLnVzZXIuaWQpXG4gICAgICAgICAgYXdhaXQgdXBkYXRlT25saW5lU3RhdHVzKHRydWUpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgc2lnbmVkIG91dCwgY2xlYXJpbmcgcHJvZmlsZScpXG4gICAgICAgICAgc2V0UHJvZmlsZShudWxsKVxuICAgICAgICB9XG5cbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICAgIH1cbiAgICApXG5cbiAgICAvLyBVcGRhdGUgb25saW5lIHN0YXR1cyB3aGVuIHVzZXIgbGVhdmVzXG4gICAgY29uc3QgaGFuZGxlQmVmb3JlVW5sb2FkID0gKCkgPT4ge1xuICAgICAgaWYgKHVzZXIpIHtcbiAgICAgICAgdXBkYXRlT25saW5lU3RhdHVzKGZhbHNlKVxuICAgICAgfVxuICAgIH1cblxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdiZWZvcmV1bmxvYWQnLCBoYW5kbGVCZWZvcmVVbmxvYWQpXG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgc3Vic2NyaXB0aW9uLnVuc3Vic2NyaWJlKClcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdiZWZvcmV1bmxvYWQnLCBoYW5kbGVCZWZvcmVVbmxvYWQpXG4gICAgfVxuICB9LCBbdXNlcl0pXG5cbiAgY29uc3QgZmV0Y2hVc2VyUHJvZmlsZSA9IGFzeW5jICh1c2VySWQ6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnRmV0Y2hpbmcgdXNlciBwcm9maWxlIGZvciB1c2VySWQ6JywgdXNlcklkKVxuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ3VzZXJfcHJvZmlsZXMnKVxuICAgICAgICAuc2VsZWN0KCcqJylcbiAgICAgICAgLmVxKCd1c2VyX2lkJywgdXNlcklkKVxuICAgICAgICAuc2luZ2xlKClcblxuICAgICAgY29uc29sZS5sb2coJ1Byb2ZpbGUgZmV0Y2ggcmVzdWx0OicsIHsgZGF0YSwgZXJyb3IgfSlcblxuICAgICAgaWYgKGVycm9yICYmIGVycm9yLmNvZGUgIT09ICdQR1JTVDExNicpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdXNlciBwcm9maWxlOicsIGVycm9yKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgaWYgKGRhdGEpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1NldHRpbmcgcHJvZmlsZSBkYXRhOicsIGRhdGEpXG4gICAgICAgIHNldFByb2ZpbGUoZGF0YSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIENyZWF0ZSBkZWZhdWx0IHByb2ZpbGUgaWYgaXQgZG9lc24ndCBleGlzdFxuICAgICAgICBjb25zb2xlLmxvZygnQ3JlYXRpbmcgbmV3IHByb2ZpbGUgZm9yIHVzZXI6JywgdXNlcklkKVxuICAgICAgICBjb25zdCB7IGRhdGE6IG5ld1Byb2ZpbGUsIGVycm9yOiBjcmVhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAgICAgLmluc2VydCh7XG4gICAgICAgICAgICB1c2VyX2lkOiB1c2VySWQsXG4gICAgICAgICAgICBmdWxsX25hbWU6IHVzZXI/LnVzZXJfbWV0YWRhdGE/LmZ1bGxfbmFtZSB8fCAnVXNlcicsXG4gICAgICAgICAgICBwbGFuOiAnZnJlZScsXG4gICAgICAgICAgICBzdWJzY3JpcHRpb25fc3RhdHVzOiAnaW5hY3RpdmUnXG4gICAgICAgICAgfSlcbiAgICAgICAgICAuc2VsZWN0KClcbiAgICAgICAgICAuc2luZ2xlKClcblxuICAgICAgICBjb25zb2xlLmxvZygnUHJvZmlsZSBjcmVhdGlvbiByZXN1bHQ6JywgeyBuZXdQcm9maWxlLCBjcmVhdGVFcnJvciB9KVxuXG4gICAgICAgIGlmIChjcmVhdGVFcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHVzZXIgcHJvZmlsZTonLCBjcmVhdGVFcnJvcilcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnU2V0dGluZyBuZXcgcHJvZmlsZTonLCBuZXdQcm9maWxlKVxuICAgICAgICAgIHNldFByb2ZpbGUobmV3UHJvZmlsZSlcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBmZXRjaFVzZXJQcm9maWxlOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHNpZ25JbiA9IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduSW5XaXRoUGFzc3dvcmQoe1xuICAgICAgZW1haWwsXG4gICAgICBwYXNzd29yZCxcbiAgICB9KVxuICAgIHJldHVybiB7IGVycm9yIH1cbiAgfVxuXG4gIGNvbnN0IHNpZ25VcCA9IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nLCBmdWxsTmFtZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduVXAoe1xuICAgICAgZW1haWwsXG4gICAgICBwYXNzd29yZCxcbiAgICAgIG9wdGlvbnM6IHtcbiAgICAgICAgZGF0YToge1xuICAgICAgICAgIGZ1bGxfbmFtZTogZnVsbE5hbWUsXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KVxuICAgIHJldHVybiB7IGVycm9yIH1cbiAgfVxuXG4gIGNvbnN0IHNpZ25PdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgYXdhaXQgdXBkYXRlT25saW5lU3RhdHVzKGZhbHNlKVxuICAgIGF3YWl0IHN1cGFiYXNlLmF1dGguc2lnbk91dCgpXG4gIH1cblxuICBjb25zdCB1cGRhdGVQcm9maWxlID0gYXN5bmMgKHVwZGF0ZXM6IFBhcnRpYWw8VXNlclByb2ZpbGU+KSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm4geyBlcnJvcjogJ05vIHVzZXIgbG9nZ2VkIGluJyB9XG5cbiAgICBjb25zb2xlLmxvZygnVXBkYXRpbmcgcHJvZmlsZSBmb3IgdXNlcjonLCB1c2VyLmlkLCAnd2l0aCB1cGRhdGVzOicsIHVwZGF0ZXMpXG5cbiAgICB0cnkge1xuICAgICAgLy8gRmlyc3QsIHRyeSB0byB1cGRhdGUgdGhlIHByb2ZpbGVcbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKCd1c2VyX3Byb2ZpbGVzJylcbiAgICAgICAgLnVwZGF0ZSh1cGRhdGVzKVxuICAgICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgICAuc2VsZWN0KClcbiAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgIGNvbnNvbGUubG9nKCdVcGRhdGUgcmVzdWx0OicsIHsgZGF0YSwgZXJyb3IgfSlcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIC8vIElmIHRoZSBwcm9maWxlIGRvZXNuJ3QgZXhpc3QsIHRyeSB0byBjcmVhdGUgaXQgZmlyc3RcbiAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09ICdQR1JTVDExNicpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnUHJvZmlsZSBub3QgZm91bmQsIGNyZWF0aW5nIG5ldyBwcm9maWxlLi4uJylcbiAgICAgICAgICBjb25zdCB7IGRhdGE6IG5ld1Byb2ZpbGUsIGVycm9yOiBjcmVhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAgIC5mcm9tKCd1c2VyX3Byb2ZpbGVzJylcbiAgICAgICAgICAgIC5pbnNlcnQoe1xuICAgICAgICAgICAgICB1c2VyX2lkOiB1c2VyLmlkLFxuICAgICAgICAgICAgICBmdWxsX25hbWU6IHVzZXI/LnVzZXJfbWV0YWRhdGE/LmZ1bGxfbmFtZSB8fCAnVXNlcicsXG4gICAgICAgICAgICAgIHBsYW46ICdmcmVlJyxcbiAgICAgICAgICAgICAgc3Vic2NyaXB0aW9uX3N0YXR1czogJ2luYWN0aXZlJyxcbiAgICAgICAgICAgICAgLi4udXBkYXRlc1xuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIC5zZWxlY3QoKVxuICAgICAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgICAgICBpZiAoY3JlYXRlRXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHByb2ZpbGU6JywgY3JlYXRlRXJyb3IpXG4gICAgICAgICAgICByZXR1cm4geyBlcnJvcjogY3JlYXRlRXJyb3IgfVxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygnUHJvZmlsZSBjcmVhdGVkIHN1Y2Nlc3NmdWxseTonLCBuZXdQcm9maWxlKVxuICAgICAgICAgICAgc2V0UHJvZmlsZShuZXdQcm9maWxlKVxuICAgICAgICAgICAgcmV0dXJuIHsgZXJyb3I6IG51bGwgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBwcm9maWxlOicsIGVycm9yKVxuICAgICAgICAgIHJldHVybiB7IGVycm9yIH1cbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1Byb2ZpbGUgdXBkYXRlZCBzdWNjZXNzZnVsbHk6JywgZGF0YSlcbiAgICAgICAgc2V0UHJvZmlsZShkYXRhKVxuICAgICAgICByZXR1cm4geyBlcnJvcjogbnVsbCB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1VuZXhwZWN0ZWQgZXJyb3IgaW4gdXBkYXRlUHJvZmlsZTonLCBlcnJvcilcbiAgICAgIHJldHVybiB7IGVycm9yIH1cbiAgICB9XG4gIH1cblxuICBjb25zdCB1cGRhdGVPbmxpbmVTdGF0dXMgPSBhc3luYyAoaXNPbmxpbmU6IGJvb2xlYW4pID0+IHtcbiAgICBpZiAoIXVzZXIpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKCd1c2VyX3Byb2ZpbGVzJylcbiAgICAgICAgLnVwZGF0ZSh7XG4gICAgICAgICAgaXNfb25saW5lOiBpc09ubGluZSxcbiAgICAgICAgICBsYXN0X3NlZW46IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICB9KVxuICAgICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBvbmxpbmUgc3RhdHVzOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHZhbHVlID0ge1xuICAgIHVzZXIsXG4gICAgcHJvZmlsZSxcbiAgICBsb2FkaW5nLFxuICAgIHNpZ25JbixcbiAgICBzaWduVXAsXG4gICAgc2lnbk91dCxcbiAgICB1cGRhdGVQcm9maWxlLFxuICAgIHVwZGF0ZU9ubGluZVN0YXR1c1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8VXNlckNvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1VzZXJDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwic3VwYWJhc2UiLCJVc2VyQ29udGV4dCIsInVuZGVmaW5lZCIsInVzZVVzZXIiLCJjb250ZXh0IiwiRXJyb3IiLCJVc2VyUHJvdmlkZXIiLCJjaGlsZHJlbiIsInVzZXIiLCJzZXRVc2VyIiwicHJvZmlsZSIsInNldFByb2ZpbGUiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImdldEluaXRpYWxTZXNzaW9uIiwiZGF0YSIsInNlc3Npb24iLCJhdXRoIiwiZ2V0U2Vzc2lvbiIsImZldGNoVXNlclByb2ZpbGUiLCJpZCIsInVwZGF0ZU9ubGluZVN0YXR1cyIsInN1YnNjcmlwdGlvbiIsIm9uQXV0aFN0YXRlQ2hhbmdlIiwiZXZlbnQiLCJjb25zb2xlIiwibG9nIiwiZW1haWwiLCJoYW5kbGVCZWZvcmVVbmxvYWQiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwidW5zdWJzY3JpYmUiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwidXNlcklkIiwiZXJyb3IiLCJmcm9tIiwic2VsZWN0IiwiZXEiLCJzaW5nbGUiLCJjb2RlIiwibmV3UHJvZmlsZSIsImNyZWF0ZUVycm9yIiwiaW5zZXJ0IiwidXNlcl9pZCIsImZ1bGxfbmFtZSIsInVzZXJfbWV0YWRhdGEiLCJwbGFuIiwic3Vic2NyaXB0aW9uX3N0YXR1cyIsInNpZ25JbiIsInBhc3N3b3JkIiwic2lnbkluV2l0aFBhc3N3b3JkIiwic2lnblVwIiwiZnVsbE5hbWUiLCJvcHRpb25zIiwic2lnbk91dCIsInVwZGF0ZVByb2ZpbGUiLCJ1cGRhdGVzIiwidXBkYXRlIiwiaXNPbmxpbmUiLCJpc19vbmxpbmUiLCJsYXN0X3NlZW4iLCJEYXRlIiwidG9JU09TdHJpbmciLCJ2YWx1ZSIsIlByb3ZpZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./contexts/UserContext.tsx\n");

/***/ }),

/***/ "./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nlckamsrdiwkyyrxzntf.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Client-side Supabase client (with RLS)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Server-side Supabase client (bypasses RLS) - only create if service key exists\nconst supabaseAdmin = supabaseServiceKey ? (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWIvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFvRDtBQUVwRCxNQUFNQyxjQUFjQywwQ0FBb0M7QUFDeEQsTUFBTUcsa0JBQWtCSCxrTkFBeUM7QUFDakUsTUFBTUsscUJBQXFCTCxRQUFRQyxHQUFHLENBQUNLLHlCQUF5QjtBQUVoRSx5Q0FBeUM7QUFDbEMsTUFBTUMsV0FBV1QsbUVBQVlBLENBQUNDLGFBQWFJLGlCQUFnQjtBQUVsRSxpRkFBaUY7QUFDMUUsTUFBTUssZ0JBQWdCSCxxQkFDekJQLG1FQUFZQSxDQUFDQyxhQUFhTSxvQkFBb0I7SUFDNUNJLE1BQU07UUFDSkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7SUFDbEI7QUFDRixLQUNBLEtBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLy4vbGliL3N1cGFiYXNlLnRzP2M5OWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuXG5jb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCFcbmNvbnN0IHN1cGFiYXNlQW5vbktleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIVxuY29uc3Qgc3VwYWJhc2VTZXJ2aWNlS2V5ID0gcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWVxuXG4vLyBDbGllbnQtc2lkZSBTdXBhYmFzZSBjbGllbnQgKHdpdGggUkxTKVxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXkpXG5cbi8vIFNlcnZlci1zaWRlIFN1cGFiYXNlIGNsaWVudCAoYnlwYXNzZXMgUkxTKSAtIG9ubHkgY3JlYXRlIGlmIHNlcnZpY2Uga2V5IGV4aXN0c1xuZXhwb3J0IGNvbnN0IHN1cGFiYXNlQWRtaW4gPSBzdXBhYmFzZVNlcnZpY2VLZXlcbiAgPyBjcmVhdGVDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlU2VydmljZUtleSwge1xuICAgICAgYXV0aDoge1xuICAgICAgICBhdXRvUmVmcmVzaFRva2VuOiBmYWxzZSxcbiAgICAgICAgcGVyc2lzdFNlc3Npb246IGZhbHNlXG4gICAgICB9XG4gICAgfSlcbiAgOiBudWxsXG5cbi8vIFR5cGVzIGZvciBvdXIgZGF0YWJhc2VcbmV4cG9ydCBpbnRlcmZhY2UgVXNlciB7XG4gIGlkOiBzdHJpbmdcbiAgZW1haWw6IHN0cmluZ1xuICBmdWxsX25hbWU6IHN0cmluZ1xuICBhdmF0YXJfdXJsPzogc3RyaW5nXG4gIHBsYW46ICdmcmVlJyB8ICdwcm8nIHwgJ2VudGVycHJpc2UnXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbiAgaXNfb25saW5lOiBib29sZWFuXG4gIGxhc3Rfc2Vlbjogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXNlclByb2ZpbGUge1xuICBpZDogc3RyaW5nXG4gIHVzZXJfaWQ6IHN0cmluZ1xuICBmdWxsX25hbWU6IHN0cmluZ1xuICBhdmF0YXJfdXJsPzogc3RyaW5nXG4gIHBsYW46ICdmcmVlJyB8ICdwcm8nIHwgJ2VudGVycHJpc2UnXG4gIHN1YnNjcmlwdGlvbl9zdGF0dXM6ICdhY3RpdmUnIHwgJ2luYWN0aXZlJyB8ICdjYW5jZWxsZWQnXG4gIHN1YnNjcmlwdGlvbl9lbmRfZGF0ZT86IHN0cmluZ1xuICBpc19vbmxpbmU6IGJvb2xlYW5cbiAgbGFzdF9zZWVuOiBzdHJpbmdcbiAgY3JlYXRlZF9hdDogc3RyaW5nXG4gIHVwZGF0ZWRfYXQ6IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEFnZW50RVNldHRpbmdzIHtcbiAgaWQ6IHN0cmluZ1xuICB1c2VyX2lkOiBzdHJpbmdcbiAgcHJvamVjdF9uYW1lOiBzdHJpbmdcbiAgcHJvamVjdF9kZXNjcmlwdGlvbjogc3RyaW5nXG4gIHRhcmdldF9hdWRpZW5jZTogc3RyaW5nXG4gIGJyYW5kX3ZvaWNlOiBzdHJpbmdcbiAgY3VzdG9tX3Byb21wdHM6IEFycmF5PHtcbiAgICBpZDogbnVtYmVyXG4gICAgbmFtZTogc3RyaW5nXG4gICAgcHJvbXB0OiBzdHJpbmdcbiAgfT5cbiAgeF9hcGlfa2V5Pzogc3RyaW5nXG4gIHhfYXBpX3NlY3JldD86IHN0cmluZ1xuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VBbm9uS2V5IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJzdXBhYmFzZVNlcnZpY2VLZXkiLCJTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIiwic3VwYWJhc2UiLCJzdXBhYmFzZUFkbWluIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./lib/supabase.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n\n // Global styles\n\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__.UserProvider, {\n        children: getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n            lineNumber: 21,\n            columnNumber: 18\n        }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQStCLENBQUMsZ0JBQWdCO0FBSU87QUFVdkQsU0FBU0MsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELHFCQUNFLDhEQUFDTCwrREFBWUE7a0JBQ1ZJLHdCQUFVLDhEQUFDRjtZQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7O0FBR3pDO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLy4vcGFnZXMvX2FwcC50c3g/MmZiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcyc7IC8vIEdsb2JhbCBzdHlsZXNcbmltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgdHlwZSB7IFJlYWN0RWxlbWVudCwgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHR5cGUgeyBOZXh0UGFnZSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgVXNlclByb3ZpZGVyIH0gZnJvbSAnLi4vY29udGV4dHMvVXNlckNvbnRleHQnO1xuXG5leHBvcnQgdHlwZSBOZXh0UGFnZVdpdGhMYXlvdXQgPSBOZXh0UGFnZSAmIHtcbiAgZ2V0TGF5b3V0PzogKHBhZ2U6IFJlYWN0RWxlbWVudCkgPT4gUmVhY3ROb2RlO1xufTtcblxuZXhwb3J0IHR5cGUgQXBwUHJvcHNXaXRoTGF5b3V0ID0gQXBwUHJvcHMgJiB7XG4gIENvbXBvbmVudDogTmV4dFBhZ2VXaXRoTGF5b3V0O1xufTtcblxuZnVuY3Rpb24gTXlBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wc1dpdGhMYXlvdXQpIHtcbiAgLy8gVXNlIHRoZSBsYXlvdXQgZGVmaW5lZCBhdCB0aGUgcGFnZSBsZXZlbCwgaWYgYXZhaWxhYmxlXG4gIGNvbnN0IGdldExheW91dCA9IENvbXBvbmVudC5nZXRMYXlvdXQgfHwgKChwYWdlKSA9PiBwYWdlKTtcblxuICByZXR1cm4gKFxuICAgIDxVc2VyUHJvdmlkZXI+XG4gICAgICB7Z2V0TGF5b3V0KDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz4pfVxuICAgIDwvVXNlclByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZGVmYXVsdCBNeUFwcDsiXSwibmFtZXMiOlsiVXNlclByb3ZpZGVyIiwiTXlBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJnZXRMYXlvdXQiLCJwYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/settings.tsx":
/*!****************************!*\
  !*** ./pages/settings.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!lucide-react */ \"__barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/supabase */ \"./lib/supabase.ts\");\n// pages/settings.tsx\n\n\n\n\n\n\n\nconst SettingsPage = ()=>{\n    const { user, profile, updateProfile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"agent-e\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Agent E Settings\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"My AI Project\");\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [targetAudience, setTargetAudience] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [brandVoice, setBrandVoice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"professional\");\n    const [customPrompts, setCustomPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            id: 1,\n            name: \"Product Launch\",\n            prompt: \"Create engaging content for product launches with excitement and clear benefits\"\n        },\n        {\n            id: 2,\n            name: \"Educational\",\n            prompt: \"Write informative content that teaches and provides value to the audience\"\n        }\n    ]);\n    // Profile Settings\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [avatarUrl, setAvatarUrl] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    // X Integration Settings\n    const [xAccountConnected, setXAccountConnected] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [xAccountInfo, setXAccountInfo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [connectingX, setConnectingX] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Load settings from Supabase\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (user) {\n            loadSettings();\n            setFullName(profile?.full_name || user.user_metadata?.full_name || \"\");\n            setEmail(user.email || \"\");\n            setAvatarUrl(profile?.avatar_url || \"\");\n        }\n    }, [\n        user,\n        profile\n    ]);\n    // Load X account status only once when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (user) {\n            loadXAccountStatus();\n        }\n    }, [\n        user?.id\n    ]); // Only depend on user ID, not the full user object\n    // Handle URL parameters for success/error messages\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const urlParams = new URLSearchParams(window.location.search);\n        const success = urlParams.get(\"success\");\n        const error = urlParams.get(\"error\");\n        if (success === \"connected\") {\n            alert(\"X account connected successfully!\");\n            loadXAccountStatus(); // Refresh the status\n            // Clean up URL\n            window.history.replaceState({}, \"\", \"/settings?tab=integrations\");\n        } else if (error) {\n            const errorMessages = {\n                \"store_failed\": \"Failed to save X account connection. Please try again.\",\n                \"oauth_failed\": \"X authorization failed. Please try again.\",\n                \"oauth_denied\": \"X authorization was denied. Please try again if you want to connect your account.\",\n                \"invalid_state\": \"Invalid authorization state. Please try again.\",\n                \"token_exchange_failed\": \"Failed to exchange authorization code. Please try again.\",\n                \"user_fetch_failed\": \"Failed to fetch user information from X. Please try again.\",\n                \"config_error\": \"X API credentials not configured properly. Please contact support.\"\n            };\n            alert(errorMessages[error] || \"An error occurred. Please try again.\");\n            // Clean up URL\n            window.history.replaceState({}, \"\", \"/settings?tab=integrations\");\n        }\n    }, []);\n    const loadSettings = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_5__.supabase.from(\"agent_e_settings\").select(\"*\").eq(\"user_id\", user.id).single();\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error loading settings:\", error);\n                return;\n            }\n            if (data) {\n                setProjectName(data.project_name || \"My AI Project\");\n                setProjectDescription(data.project_description || \"\");\n                setTargetAudience(data.target_audience || \"\");\n                setBrandVoice(data.brand_voice || \"professional\");\n                setCustomPrompts(data.custom_prompts || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading settings:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveAgentESettings = async ()=>{\n        if (!user) return;\n        setSaving(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_5__.supabase.from(\"agent_e_settings\").upsert({\n                user_id: user.id,\n                project_name: projectName,\n                project_description: projectDescription,\n                target_audience: targetAudience,\n                brand_voice: brandVoice,\n                custom_prompts: customPrompts\n            });\n            if (error) {\n                console.error(\"Error saving settings:\", error);\n                alert(\"Error saving settings. Please try again.\");\n            } else {\n                alert(\"Settings saved successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            alert(\"Error saving settings. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const saveProfileSettings = async ()=>{\n        if (!user) {\n            alert(\"You must be logged in to update your profile.\");\n            return;\n        }\n        setSaving(true);\n        try {\n            // Try to update the profile\n            const { error } = await updateProfile({\n                full_name: fullName,\n                avatar_url: avatarUrl || null\n            });\n            if (error) {\n                console.error(\"Error updating profile:\", error);\n                // If the table doesn't exist, try to create it first\n                if (error.code === \"42P01\" || error.message?.includes('relation \"user_profiles\" does not exist')) {\n                    alert(\"Database setup required. Please run the database schema first, then try again.\");\n                } else {\n                    alert(`Error updating profile: ${error.message || \"Please try again.\"}`);\n                }\n            } else {\n                alert(\"Profile updated successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            alert(\"Error updating profile. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const loadXAccountStatus = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(`/api/x/account-status?userId=${user.id}`);\n            const data = await response.json();\n            if (response.ok && data.connected) {\n                setXAccountConnected(true);\n                setXAccountInfo(data.accountInfo);\n            } else {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n            }\n        } catch (error) {\n            console.error(\"Error loading X account status:\", error);\n            setXAccountConnected(false);\n            setXAccountInfo(null);\n        }\n    };\n    const handleConnectX = async ()=>{\n        if (!user) {\n            alert(\"Please log in first\");\n            return;\n        }\n        setConnectingX(true);\n        try {\n            const response = await fetch(\"/api/x/auth-url\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // Redirect directly to X authorization\n                window.location.href = data.authUrl;\n            } else {\n                alert(data.error || \"Failed to initiate X connection\");\n                setConnectingX(false);\n            }\n        } catch (error) {\n            console.error(\"Error connecting to X:\", error);\n            alert(\"Failed to connect to X. Please try again.\");\n            setConnectingX(false);\n        }\n    };\n    const handleDisconnectX = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/disconnect\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            if (response.ok) {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n                alert(\"X account disconnected successfully\");\n            } else {\n                const data = await response.json();\n                alert(data.error || \"Failed to disconnect X account\");\n            }\n        } catch (error) {\n            console.error(\"Error disconnecting X:\", error);\n            alert(\"Failed to disconnect X account. Please try again.\");\n        }\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        surface: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\"\n    };\n    const tabs = [\n        {\n            id: \"agent-e\",\n            label: \"Agent E\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Bot\n        },\n        {\n            id: \"account\",\n            label: \"Account\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User\n        },\n        {\n            id: \"integrations\",\n            label: \"Integrations\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Zap\n        },\n        {\n            id: \"notifications\",\n            label: \"Notifications\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Bell\n        },\n        {\n            id: \"security\",\n            label: \"Security\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Shield\n        }\n    ];\n    const handleSavePrompt = (id, newPrompt)=>{\n        setCustomPrompts((prev)=>prev.map((p)=>p.id === id ? {\n                    ...p,\n                    prompt: newPrompt\n                } : p));\n    };\n    const addNewPrompt = ()=>{\n        const newId = Math.max(...customPrompts.map((p)=>p.id)) + 1;\n        setCustomPrompts((prev)=>[\n                ...prev,\n                {\n                    id: newId,\n                    name: \"New Prompt\",\n                    prompt: \"\"\n                }\n            ]);\n    };\n    const deletePrompt = (id)=>{\n        setCustomPrompts((prev)=>prev.filter((p)=>p.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"ff161281ed666c63\",\n                children: \"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}\"\n            }, void 0, false, void 0, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"40px 60px\",\n                    height: \"100vh\",\n                    overflow: \"auto\",\n                    background: colors.background\n                },\n                className: \"jsx-ff161281ed666c63\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: \"40px\"\n                        },\n                        className: \"jsx-ff161281ed666c63\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    margin: 0,\n                                    fontSize: \"32px\",\n                                    fontWeight: \"300\",\n                                    letterSpacing: \"-1px\",\n                                    marginBottom: \"8px\",\n                                    fontFamily: \"Georgia, serif\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: \"Settings\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: colors.text.secondary,\n                                    margin: 0,\n                                    fontSize: \"16px\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: \"Configure Agent E and manage your account preferences\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"8px\",\n                            marginBottom: \"40px\",\n                            borderBottom: `1px solid ${colors.border}`,\n                            paddingBottom: \"0\"\n                        },\n                        className: \"jsx-ff161281ed666c63\",\n                        children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 20px\",\n                                    background: activeTab === tab.id ? colors.surface : \"transparent\",\n                                    border: activeTab === tab.id ? `1px solid ${colors.border}` : \"1px solid transparent\",\n                                    borderBottom: activeTab === tab.id ? `1px solid ${colors.surface}` : \"1px solid transparent\",\n                                    borderRadius: \"8px 8px 0 0\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    color: activeTab === tab.id ? colors.text.primary : colors.text.secondary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    marginBottom: \"-1px\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    tab.label\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"12px\",\n                            padding: \"32px\",\n                            border: `1px solid ${colors.border}`,\n                            minHeight: \"500px\"\n                        },\n                        className: \"jsx-ff161281ed666c63\",\n                        children: [\n                            activeTab === \"agent-e\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-ff161281ed666c63\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"24px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"24px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Bot, {\n                                                size: 24,\n                                                color: colors.primary\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Agent E Configuration\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"32px\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    marginBottom: \"16px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: \"Project Information\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"grid\",\n                                                    gridTemplateColumns: \"1fr 1fr\",\n                                                    gap: \"20px\",\n                                                    marginBottom: \"20px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: \"block\",\n                                                                    color: colors.text.secondary,\n                                                                    fontSize: \"14px\",\n                                                                    fontWeight: \"500\",\n                                                                    marginBottom: \"8px\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\",\n                                                                children: \"Project Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: projectName,\n                                                                onChange: (e)=>setProjectName(e.target.value),\n                                                                style: {\n                                                                    width: \"100%\",\n                                                                    padding: \"12px 16px\",\n                                                                    border: `1px solid ${colors.border}`,\n                                                                    borderRadius: \"8px\",\n                                                                    fontSize: \"14px\",\n                                                                    background: colors.background,\n                                                                    outline: \"none\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: \"block\",\n                                                                    color: colors.text.secondary,\n                                                                    fontSize: \"14px\",\n                                                                    fontWeight: \"500\",\n                                                                    marginBottom: \"8px\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\",\n                                                                children: \"Brand Voice\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: brandVoice,\n                                                                onChange: (e)=>setBrandVoice(e.target.value),\n                                                                style: {\n                                                                    width: \"100%\",\n                                                                    padding: \"12px 16px\",\n                                                                    border: `1px solid ${colors.border}`,\n                                                                    borderRadius: \"8px\",\n                                                                    fontSize: \"14px\",\n                                                                    background: colors.background,\n                                                                    outline: \"none\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"professional\",\n                                                                        className: \"jsx-ff161281ed666c63\",\n                                                                        children: \"Professional\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"casual\",\n                                                                        className: \"jsx-ff161281ed666c63\",\n                                                                        children: \"Casual & Friendly\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"technical\",\n                                                                        className: \"jsx-ff161281ed666c63\",\n                                                                        children: \"Technical\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"creative\",\n                                                                        className: \"jsx-ff161281ed666c63\",\n                                                                        children: \"Creative & Fun\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"authoritative\",\n                                                                        className: \"jsx-ff161281ed666c63\",\n                                                                        children: \"Authoritative\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                        lineNumber: 456,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginBottom: \"20px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: \"Project Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: projectDescription,\n                                                        onChange: (e)=>setProjectDescription(e.target.value),\n                                                        placeholder: \"Describe your project, product, or service so Agent E can create relevant content...\",\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: `1px solid ${colors.border}`,\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\",\n                                                            minHeight: \"100px\",\n                                                            resize: \"vertical\"\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: \"Target Audience\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: targetAudience,\n                                                        onChange: (e)=>setTargetAudience(e.target.value),\n                                                        placeholder: \"e.g., Tech entrepreneurs, Small business owners, Developers...\",\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: `1px solid ${colors.border}`,\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"32px\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    alignItems: \"center\",\n                                                    marginBottom: \"16px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        style: {\n                                                            color: colors.text.primary,\n                                                            fontSize: \"18px\",\n                                                            fontWeight: \"600\",\n                                                            margin: 0\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: \"Custom Prompts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: addNewPrompt,\n                                                        style: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            gap: \"6px\",\n                                                            padding: \"8px 16px\",\n                                                            background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                                            color: \"white\",\n                                                            border: \"none\",\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Plus, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            \"Add Prompt\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            customPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        border: `1px solid ${colors.border}`,\n                                                        borderRadius: \"8px\",\n                                                        padding: \"16px\",\n                                                        marginBottom: \"12px\",\n                                                        background: colors.background\n                                                    },\n                                                    className: \"jsx-ff161281ed666c63\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                justifyContent: \"space-between\",\n                                                                alignItems: \"center\",\n                                                                marginBottom: \"12px\"\n                                                            },\n                                                            className: \"jsx-ff161281ed666c63\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: prompt.name,\n                                                                    onChange: (e)=>{\n                                                                        setCustomPrompts((prev)=>prev.map((p)=>p.id === prompt.id ? {\n                                                                                    ...p,\n                                                                                    name: e.target.value\n                                                                                } : p));\n                                                                    },\n                                                                    style: {\n                                                                        background: \"transparent\",\n                                                                        border: \"none\",\n                                                                        fontSize: \"16px\",\n                                                                        fontWeight: \"600\",\n                                                                        color: colors.text.primary,\n                                                                        outline: \"none\",\n                                                                        flex: 1\n                                                                    },\n                                                                    className: \"jsx-ff161281ed666c63\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>deletePrompt(prompt.id),\n                                                                    style: {\n                                                                        background: \"none\",\n                                                                        border: \"none\",\n                                                                        color: colors.text.tertiary,\n                                                                        cursor: \"pointer\",\n                                                                        padding: \"4px\"\n                                                                    },\n                                                                    className: \"jsx-ff161281ed666c63\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Trash2, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: prompt.prompt,\n                                                            onChange: (e)=>handleSavePrompt(prompt.id, e.target.value),\n                                                            placeholder: \"Enter your custom prompt for Agent E...\",\n                                                            style: {\n                                                                width: \"100%\",\n                                                                padding: \"12px\",\n                                                                border: `1px solid ${colors.border}`,\n                                                                borderRadius: \"6px\",\n                                                                fontSize: \"14px\",\n                                                                background: colors.surface,\n                                                                outline: \"none\",\n                                                                minHeight: \"80px\",\n                                                                resize: \"vertical\"\n                                                            },\n                                                            className: \"jsx-ff161281ed666c63\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, prompt.id, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: saveAgentESettings,\n                                        disabled: saving || !user,\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            padding: \"12px 24px\",\n                                            background: saving || !user ? colors.text.tertiary : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                            color: \"white\",\n                                            border: \"none\",\n                                            borderRadius: \"8px\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                            boxShadow: saving || !user ? \"none\" : `0 4px 12px ${colors.primary}30`,\n                                            opacity: saving || !user ? 0.6 : 1\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Save, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            saving ? \"Saving...\" : \"Save Agent E Settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, undefined),\n                            activeTab === \"integrations\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-ff161281ed666c63\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"24px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"24px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Zap, {\n                                                size: 24,\n                                                color: colors.primary\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Integrations\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            border: `1px solid ${colors.border}`,\n                                            borderRadius: \"12px\",\n                                            padding: \"24px\",\n                                            marginBottom: \"24px\",\n                                            background: colors.background\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\",\n                                                    marginBottom: \"16px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Twitter, {\n                                                        size: 24,\n                                                        color: colors.primary\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        style: {\n                                                            color: colors.text.primary,\n                                                            fontSize: \"18px\",\n                                                            fontWeight: \"600\",\n                                                            margin: 0\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: \"X (Twitter) Account\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    marginBottom: \"20px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: \"Connect your X account to enable automated posting and content scheduling through our premium service.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            xAccountConnected && xAccountInfo ? // Connected State\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"space-between\",\n                                                    padding: \"16px\",\n                                                    background: \"#F0FDF4\",\n                                                    border: \"1px solid #BBF7D0\",\n                                                    borderRadius: \"8px\",\n                                                    marginBottom: \"16px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            gap: \"12px\"\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: [\n                                                            xAccountInfo.profile_image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: xAccountInfo.profile_image_url,\n                                                                alt: \"Profile\",\n                                                                style: {\n                                                                    width: \"40px\",\n                                                                    height: \"40px\",\n                                                                    borderRadius: \"50%\",\n                                                                    objectFit: \"cover\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-ff161281ed666c63\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            color: \"#065F46\",\n                                                                            fontSize: \"16px\",\n                                                                            fontWeight: \"600\"\n                                                                        },\n                                                                        className: \"jsx-ff161281ed666c63\",\n                                                                        children: xAccountInfo.name || \"Connected Account\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            color: \"#047857\",\n                                                                            fontSize: \"14px\"\n                                                                        },\n                                                                        className: \"jsx-ff161281ed666c63\",\n                                                                        children: [\n                                                                            \"@\",\n                                                                            xAccountInfo.username\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            padding: \"6px 12px\",\n                                                            background: \"#10B981\",\n                                                            color: \"white\",\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"12px\",\n                                                            fontWeight: \"600\"\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: \"Connected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 17\n                                            }, undefined) : // Not Connected State\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    padding: \"16px\",\n                                                    background: \"#FEF3C7\",\n                                                    border: \"1px solid #FDE68A\",\n                                                    borderRadius: \"8px\",\n                                                    marginBottom: \"16px\",\n                                                    textAlign: \"center\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            color: \"#92400E\",\n                                                            fontSize: \"14px\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: \"No X account connected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            color: \"#B45309\",\n                                                            fontSize: \"12px\"\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: \"Connect your account to start scheduling posts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    gap: \"12px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: xAccountConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleDisconnectX,\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"8px\",\n                                                        padding: \"10px 20px\",\n                                                        background: \"#FEE2E2\",\n                                                        color: \"#DC2626\",\n                                                        border: \"1px solid #FECACA\",\n                                                        borderRadius: \"8px\",\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        cursor: \"pointer\"\n                                                    },\n                                                    className: \"jsx-ff161281ed666c63\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Twitter, {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Disconnect Account\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        console.log(\"Connect X button clicked\");\n                                                        handleConnectX();\n                                                    },\n                                                    disabled: connectingX,\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"8px\",\n                                                        padding: \"12px 24px\",\n                                                        background: connectingX ? colors.text.tertiary : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                                        color: \"white\",\n                                                        border: \"none\",\n                                                        borderRadius: \"8px\",\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"600\",\n                                                        cursor: connectingX ? \"not-allowed\" : \"pointer\",\n                                                        opacity: connectingX ? 0.6 : 1,\n                                                        transition: \"all 0.2s ease\",\n                                                        boxShadow: connectingX ? \"none\" : `0 4px 12px ${colors.primary}30`\n                                                    },\n                                                    className: \"jsx-ff161281ed666c63\",\n                                                    children: connectingX ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: \"16px\",\n                                                                    height: \"16px\",\n                                                                    border: \"2px solid transparent\",\n                                                                    borderTop: \"2px solid white\",\n                                                                    borderRadius: \"50%\",\n                                                                    animation: \"spin 1s linear infinite\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 818,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \"Redirecting to X...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Twitter, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \"Connect X Account\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 11\n                            }, undefined),\n                            activeTab === \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-ff161281ed666c63\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"24px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"24px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 24,\n                                                color: colors.primary\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 852,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Account Settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"32px\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    marginBottom: \"16px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: \"Profile Information\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 858,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"grid\",\n                                                    gap: \"16px\",\n                                                    maxWidth: \"400px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: \"block\",\n                                                                    color: colors.text.primary,\n                                                                    fontSize: \"14px\",\n                                                                    fontWeight: \"600\",\n                                                                    marginBottom: \"6px\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\",\n                                                                children: \"Full Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 869,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: fullName,\n                                                                onChange: (e)=>setFullName(e.target.value),\n                                                                style: {\n                                                                    width: \"100%\",\n                                                                    padding: \"12px 16px\",\n                                                                    border: `1px solid ${colors.border}`,\n                                                                    borderRadius: \"8px\",\n                                                                    fontSize: \"14px\",\n                                                                    background: colors.surface,\n                                                                    color: colors.text.primary\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 878,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 868,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: \"block\",\n                                                                    color: colors.text.primary,\n                                                                    fontSize: \"14px\",\n                                                                    fontWeight: \"600\",\n                                                                    marginBottom: \"6px\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\",\n                                                                children: \"Email Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 895,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"email\",\n                                                                value: email,\n                                                                disabled: true,\n                                                                style: {\n                                                                    width: \"100%\",\n                                                                    padding: \"12px 16px\",\n                                                                    border: `1px solid ${colors.border}`,\n                                                                    borderRadius: \"8px\",\n                                                                    fontSize: \"14px\",\n                                                                    background: \"#F9F9F9\",\n                                                                    color: colors.text.secondary,\n                                                                    cursor: \"not-allowed\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: \"block\",\n                                                                    color: colors.text.primary,\n                                                                    fontSize: \"14px\",\n                                                                    fontWeight: \"600\",\n                                                                    marginBottom: \"6px\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\",\n                                                                children: \"Avatar URL (Optional)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"url\",\n                                                                value: avatarUrl,\n                                                                onChange: (e)=>setAvatarUrl(e.target.value),\n                                                                placeholder: \"https://example.com/your-avatar.jpg\",\n                                                                style: {\n                                                                    width: \"100%\",\n                                                                    padding: \"12px 16px\",\n                                                                    border: `1px solid ${colors.border}`,\n                                                                    borderRadius: \"8px\",\n                                                                    fontSize: \"14px\",\n                                                                    background: colors.surface,\n                                                                    color: colors.text.primary\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 931,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    fontSize: \"12px\",\n                                                                    color: colors.text.tertiary,\n                                                                    marginTop: \"4px\",\n                                                                    marginBottom: \"0\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\",\n                                                                children: \"Enter a URL to your profile picture\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 946,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 921,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 857,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"32px\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    marginBottom: \"16px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: \"Subscription\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    background: `linear-gradient(135deg, ${colors.primary}15 0%, ${colors.primaryLight}15 100%)`,\n                                                    border: `1px solid ${colors.primary}30`,\n                                                    borderRadius: \"12px\",\n                                                    padding: \"20px\",\n                                                    maxWidth: \"400px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            gap: \"12px\",\n                                                            marginBottom: \"12px\"\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: \"32px\",\n                                                                    height: \"32px\",\n                                                                    background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                                                    borderRadius: \"8px\",\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    justifyContent: \"center\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Crown, {\n                                                                    size: 16,\n                                                                    color: \"white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                    lineNumber: 991,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 982,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-ff161281ed666c63\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            color: colors.text.primary,\n                                                                            fontSize: \"16px\",\n                                                                            fontWeight: \"600\"\n                                                                        },\n                                                                        className: \"jsx-ff161281ed666c63\",\n                                                                        children: profile?.plan === \"pro\" ? \"Pro Plan\" : profile?.plan === \"enterprise\" ? \"Enterprise Plan\" : \"Free Plan\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                        lineNumber: 994,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            color: colors.text.secondary,\n                                                                            fontSize: \"14px\"\n                                                                        },\n                                                                        className: \"jsx-ff161281ed666c63\",\n                                                                        children: profile?.plan === \"free\" ? \"No subscription\" : profile?.subscription_status === \"active\" ? \"$29/month • Active\" : \"Subscription inactive\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                        lineNumber: 1001,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 993,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            gap: \"12px\",\n                                                            marginTop: \"16px\"\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                style: {\n                                                                    padding: \"8px 16px\",\n                                                                    background: colors.surface,\n                                                                    color: colors.text.primary,\n                                                                    border: `1px solid ${colors.border}`,\n                                                                    borderRadius: \"6px\",\n                                                                    fontSize: \"14px\",\n                                                                    fontWeight: \"500\",\n                                                                    cursor: \"pointer\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\",\n                                                                children: \"Manage Billing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 1015,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                style: {\n                                                                    padding: \"8px 16px\",\n                                                                    background: \"transparent\",\n                                                                    color: colors.text.secondary,\n                                                                    border: `1px solid ${colors.border}`,\n                                                                    borderRadius: \"6px\",\n                                                                    fontSize: \"14px\",\n                                                                    fontWeight: \"500\",\n                                                                    cursor: \"pointer\"\n                                                                },\n                                                                className: \"jsx-ff161281ed666c63\",\n                                                                children: \"Cancel Plan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 1027,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 1010,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 969,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 959,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: saveProfileSettings,\n                                        disabled: saving || !user,\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            padding: \"12px 24px\",\n                                            background: saving || !user ? colors.text.tertiary : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                            color: \"white\",\n                                            border: \"none\",\n                                            borderRadius: \"8px\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                            boxShadow: saving || !user ? \"none\" : `0 4px 12px ${colors.primary}30`,\n                                            opacity: saving || !user ? 0.6 : 1\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Save, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 1063,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            saving ? \"Saving...\" : \"Save Account Settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 1044,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 11\n                            }, undefined),\n                            activeTab !== \"agent-e\" && activeTab !== \"integrations\" && activeTab !== \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: \"center\",\n                                    padding: \"60px 20px\",\n                                    color: colors.text.secondary\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            marginBottom: \"12px\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"Coming Soon\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 1076,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"This section is under development.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 1077,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1071,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\nSettingsPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 1088,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/settings.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-jsx/style");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fsettings&preferredRegion=&absolutePagePath=.%2Fpages%2Fsettings.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();