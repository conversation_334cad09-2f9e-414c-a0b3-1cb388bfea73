"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/test/twitter-auth";
exports.ids = ["pages/api/test/twitter-auth"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "twitter-api-v2":
/*!*********************************!*\
  !*** external "twitter-api-v2" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("twitter-api-v2");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest%2Ftwitter-auth&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Ftest%2Ftwitter-auth.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest%2Ftwitter-auth&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Ftest%2Ftwitter-auth.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_test_twitter_auth_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/test/twitter-auth.ts */ \"(api)/./pages/api/test/twitter-auth.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_test_twitter_auth_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_test_twitter_auth_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/test/twitter-auth\",\n        pathname: \"/api/test/twitter-auth\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_test_twitter_auth_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest%2Ftwitter-auth&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Ftest%2Ftwitter-auth.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/test/twitter-auth.ts":
/*!****************************************!*\
  !*** ./pages/api/test/twitter-auth.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! twitter-api-v2 */ \"twitter-api-v2\");\n/* harmony import */ var twitter_api_v2__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__);\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        // Get Twitter API credentials\n        const twitterApiKey = process.env.TWITTER_API_KEY;\n        const twitterApiSecret = process.env.TWITTER_API_SECRET;\n        console.log(\"Twitter API Key:\", twitterApiKey ? \"Present\" : \"Missing\");\n        console.log(\"Twitter API Secret:\", twitterApiSecret ? \"Present\" : \"Missing\");\n        if (!twitterApiKey || !twitterApiSecret) {\n            return res.status(500).json({\n                error: \"Twitter API credentials not configured\",\n                details: {\n                    apiKey: twitterApiKey ? \"Present\" : \"Missing\",\n                    apiSecret: twitterApiSecret ? \"Present\" : \"Missing\"\n                }\n            });\n        }\n        // Test basic client initialization\n        const twitterClient = new twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__.TwitterApi({\n            appKey: twitterApiKey,\n            appSecret: twitterApiSecret\n        });\n        console.log(\"Twitter client initialized successfully\");\n        // Try to generate auth link with oob\n        try {\n            const { url: authUrl, oauth_token, oauth_token_secret } = await twitterClient.generateAuthLink(\"oob\", {\n                linkMode: \"authorize\"\n            });\n            return res.status(200).json({\n                success: true,\n                message: \"Twitter OAuth flow working\",\n                authUrl: authUrl.substring(0, 50) + \"...\",\n                oauth_token: oauth_token.substring(0, 10) + \"...\",\n                hasSecret: !!oauth_token_secret\n            });\n        } catch (authError) {\n            console.error(\"Auth generation error:\", authError);\n            return res.status(500).json({\n                error: \"Failed to generate auth URL\",\n                details: authError.message || \"Unknown error\",\n                code: authError.code || \"Unknown\"\n            });\n        }\n    } catch (error) {\n        console.error(\"Twitter test error:\", error);\n        return res.status(500).json({\n            error: \"Twitter API test failed\",\n            details: error.message || \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/test/twitter-auth.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest%2Ftwitter-auth&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Ftest%2Ftwitter-auth.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();