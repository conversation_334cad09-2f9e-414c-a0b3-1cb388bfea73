"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/setup/create-user-profiles";
exports.ids = ["pages/api/setup/create-user-profiles"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-user-profiles&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsetup%2Fcreate-user-profiles.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-user-profiles&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsetup%2Fcreate-user-profiles.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_setup_create_user_profiles_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/setup/create-user-profiles.ts */ \"(api)/./pages/api/setup/create-user-profiles.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_setup_create_user_profiles_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_setup_create_user_profiles_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/setup/create-user-profiles\",\n        pathname: \"/api/setup/create-user-profiles\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_setup_create_user_profiles_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-user-profiles&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsetup%2Fcreate-user-profiles.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nlckamsrdiwkyyrxzntf.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./pages/api/setup/create-user-profiles.ts":
/*!*************************************************!*\
  !*** ./pages/api/setup/create-user-profiles.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/supabase */ \"(api)/./lib/supabase.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        console.log(\"Creating user_profiles table...\");\n        // Create the user_profiles table directly using Supabase client\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"user_profiles\").select(\"*\").limit(1);\n        if (error && error.code === \"42P01\") {\n            // Table doesn't exist, we need to create it\n            console.log(\"Table does not exist, need to create it manually in Supabase dashboard\");\n            return res.status(200).json({\n                success: false,\n                message: \"user_profiles table does not exist. Please create it in Supabase dashboard.\",\n                sql: `\nCREATE TABLE user_profiles (\n  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\n  full_name TEXT,\n  avatar_url TEXT,\n  plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'pro', 'enterprise')),\n  subscription_status TEXT DEFAULT 'inactive' CHECK (subscription_status IN ('active', 'inactive', 'cancelled')),\n  is_online BOOLEAN DEFAULT false,\n  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n  x_account_info JSONB,\n  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n  UNIQUE(user_id)\n);\n\n-- Enable Row Level Security\nALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;\n\n-- Create policies\nCREATE POLICY \"Users can view their own profile\" ON user_profiles\n  FOR SELECT USING (auth.uid() = user_id);\n\nCREATE POLICY \"Users can insert their own profile\" ON user_profiles\n  FOR INSERT WITH CHECK (auth.uid() = user_id);\n\nCREATE POLICY \"Users can update their own profile\" ON user_profiles\n  FOR UPDATE USING (auth.uid() = user_id);\n\n-- Create updated_at trigger\nCREATE OR REPLACE FUNCTION update_updated_at_column()\nRETURNS TRIGGER AS $$\nBEGIN\n    NEW.updated_at = NOW();\n    RETURN NEW;\nEND;\n$$ language 'plpgsql';\n\nCREATE TRIGGER update_user_profiles_updated_at \n  BEFORE UPDATE ON user_profiles \n  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();\n        `\n            });\n        } else if (error) {\n            console.error(\"Error checking table:\", error);\n            return res.status(500).json({\n                error: \"Database error\",\n                details: error\n            });\n        } else {\n            console.log(\"Table exists, checking structure...\");\n            return res.status(200).json({\n                success: true,\n                message: \"user_profiles table already exists\",\n                tableExists: true\n            });\n        }\n    } catch (error) {\n        console.error(\"Setup error:\", error);\n        return res.status(500).json({\n            error: \"Setup failed\",\n            details: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/setup/create-user-profiles.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-user-profiles&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsetup%2Fcreate-user-profiles.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();