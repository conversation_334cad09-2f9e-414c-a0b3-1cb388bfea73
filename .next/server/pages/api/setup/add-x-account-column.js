"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/setup/add-x-account-column";
exports.ids = ["pages/api/setup/add-x-account-column"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fadd-x-account-column&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsetup%2Fadd-x-account-column.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fadd-x-account-column&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsetup%2Fadd-x-account-column.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_setup_add_x_account_column_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/setup/add-x-account-column.ts */ \"(api)/./pages/api/setup/add-x-account-column.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_setup_add_x_account_column_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_setup_add_x_account_column_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/setup/add-x-account-column\",\n        pathname: \"/api/setup/add-x-account-column\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_setup_add_x_account_column_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fadd-x-account-column&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsetup%2Fadd-x-account-column.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nlckamsrdiwkyyrxzntf.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./pages/api/setup/add-x-account-column.ts":
/*!*************************************************!*\
  !*** ./pages/api/setup/add-x-account-column.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/supabase */ \"(api)/./lib/supabase.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        // Check if the column already exists\n        const { data: columns, error: columnError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"check_column_exists\", {\n            table_name: \"user_profiles\",\n            column_name: \"x_account_info\"\n        });\n        if (columnError) {\n            console.log(\"Column check failed, proceeding with migration anyway\");\n        }\n        // Add the column if it doesn't exist\n        const migrationSQL = `\n      DO $$ \n      BEGIN \n          IF NOT EXISTS (\n              SELECT 1 \n              FROM information_schema.columns \n              WHERE table_name = 'user_profiles' \n              AND column_name = 'x_account_info'\n          ) THEN\n              ALTER TABLE user_profiles ADD COLUMN x_account_info JSONB;\n              RAISE NOTICE 'Added x_account_info column to user_profiles table';\n          ELSE\n              RAISE NOTICE 'x_account_info column already exists in user_profiles table';\n          END IF;\n      END $$;\n    `;\n        const { error: migrationError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"exec_sql\", {\n            sql: migrationSQL\n        });\n        if (migrationError) {\n            console.error(\"Migration error:\", migrationError);\n            // Try a simpler approach\n            const { error: simpleError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"user_profiles\").select(\"x_account_info\").limit(1);\n            if (simpleError && simpleError.message.includes('column \"x_account_info\" does not exist')) {\n                return res.status(200).json({\n                    success: false,\n                    message: \"Column does not exist. Please run this SQL in your Supabase SQL editor:\",\n                    sql: \"ALTER TABLE user_profiles ADD COLUMN x_account_info JSONB;\"\n                });\n            }\n        }\n        // Test if we can now access the column\n        const { data: testData, error: testError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"user_profiles\").select(\"x_account_info\").limit(1);\n        if (testError) {\n            return res.status(500).json({\n                success: false,\n                error: \"Column migration may have failed\",\n                details: testError.message,\n                sql: \"ALTER TABLE user_profiles ADD COLUMN x_account_info JSONB;\"\n            });\n        }\n        return res.status(200).json({\n            success: true,\n            message: \"x_account_info column is available in user_profiles table\",\n            columnExists: true\n        });\n    } catch (error) {\n        console.error(\"Setup error:\", error);\n        return res.status(500).json({\n            success: false,\n            error: \"Setup failed\",\n            details: error.message,\n            sql: \"ALTER TABLE user_profiles ADD COLUMN x_account_info JSONB;\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/setup/add-x-account-column.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fadd-x-account-column&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsetup%2Fadd-x-account-column.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();