"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/setup/migrate";
exports.ids = ["pages/api/setup/migrate"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fmigrate&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsetup%2Fmigrate.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fmigrate&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsetup%2Fmigrate.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_setup_migrate_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/setup/migrate.ts */ \"(api)/./pages/api/setup/migrate.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_setup_migrate_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_setup_migrate_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/setup/migrate\",\n        pathname: \"/api/setup/migrate\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_setup_migrate_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fmigrate&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsetup%2Fmigrate.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nlckamsrdiwkyyrxzntf.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./pages/api/setup/migrate.ts":
/*!************************************!*\
  !*** ./pages/api/setup/migrate.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/supabase */ \"(api)/./lib/supabase.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        console.log(\"Creating database tables...\");\n        // Create scheduled_posts table\n        const { error: scheduledPostsError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"exec_sql\", {\n            sql: `\n        CREATE TABLE IF NOT EXISTS scheduled_posts (\n          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n          user_id UUID NOT NULL,\n          content TEXT NOT NULL,\n          platform VARCHAR(50) NOT NULL DEFAULT 'twitter',\n          scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,\n          status VARCHAR(20) NOT NULL DEFAULT 'scheduled',\n          platform_post_id VARCHAR(255),\n          error_message TEXT,\n          posted_at TIMESTAMP WITH TIME ZONE,\n          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n        );\n      `\n        });\n        if (scheduledPostsError) {\n            console.error(\"Error creating scheduled_posts:\", scheduledPostsError);\n        }\n        // Create posted_content table\n        const { error: postedContentError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"exec_sql\", {\n            sql: `\n        CREATE TABLE IF NOT EXISTS posted_content (\n          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n          user_id UUID NOT NULL,\n          content TEXT NOT NULL,\n          platform VARCHAR(50) NOT NULL DEFAULT 'twitter',\n          platform_post_id VARCHAR(255) NOT NULL,\n          scheduled_post_id UUID,\n          status VARCHAR(20) NOT NULL DEFAULT 'posted',\n          posted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n        );\n      `\n        });\n        if (postedContentError) {\n            console.error(\"Error creating posted_content:\", postedContentError);\n        }\n        // Create x_accounts table\n        const { error: xAccountsError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"exec_sql\", {\n            sql: `\n        CREATE TABLE IF NOT EXISTS x_accounts (\n          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n          user_id UUID NOT NULL,\n          x_user_id VARCHAR(255) NOT NULL,\n          username VARCHAR(255) NOT NULL,\n          name VARCHAR(255),\n          profile_image_url TEXT,\n          access_token TEXT NOT NULL,\n          access_secret TEXT NOT NULL,\n          is_active BOOLEAN DEFAULT true,\n          connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n          disconnected_at TIMESTAMP WITH TIME ZONE,\n          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n        );\n      `\n        });\n        if (xAccountsError) {\n            console.error(\"Error creating x_accounts:\", xAccountsError);\n        }\n        // Create x_oauth_tokens table\n        const { error: oauthTokensError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"exec_sql\", {\n            sql: `\n        CREATE TABLE IF NOT EXISTS x_oauth_tokens (\n          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n          user_id UUID NOT NULL,\n          oauth_token VARCHAR(255) NOT NULL,\n          oauth_token_secret VARCHAR(255) NOT NULL,\n          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n        );\n      `\n        });\n        if (oauthTokensError) {\n            console.error(\"Error creating x_oauth_tokens:\", oauthTokensError);\n        }\n        return res.status(200).json({\n            success: true,\n            message: \"Database tables created successfully\",\n            errors: {\n                scheduledPosts: scheduledPostsError?.message,\n                postedContent: postedContentError?.message,\n                xAccounts: xAccountsError?.message,\n                oauthTokens: oauthTokensError?.message\n            }\n        });\n    } catch (error) {\n        console.error(\"Migration error:\", error);\n        return res.status(500).json({\n            error: \"Migration failed\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/setup/migrate.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fmigrate&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsetup%2Fmigrate.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();