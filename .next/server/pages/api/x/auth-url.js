"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/x/auth-url";
exports.ids = ["pages/api/x/auth-url"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/x/auth-url.ts */ \"(api)/./pages/api/x/auth-url.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/x/auth-url\",\n        pathname: \"/api/x/auth-url\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnglMkZhdXRoLXVybCZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnBhZ2VzJTJGYXBpJTJGeCUyRmF1dGgtdXJsLnRzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNMO0FBQzFEO0FBQ3NEO0FBQ3REO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyxxREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMscURBQVE7QUFDcEM7QUFDTyx3QkFBd0IsZ0hBQW1CO0FBQ2xEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8/MmIyYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXMvYXBpL3gvYXV0aC11cmwudHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS94L2F1dGgtdXJsXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkveC9hdXRoLXVybFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nlckamsrdiwkyyrxzntf.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Client-side Supabase client (with RLS)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Server-side Supabase client (bypasses RLS) - only create if service key exists\nconst supabaseAdmin = supabaseServiceKey ? (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./pages/api/x/auth-url.ts":
/*!*********************************!*\
  !*** ./pages/api/x/auth-url.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/supabase */ \"(api)/./lib/supabase.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const { userId } = req.body;\n    if (!userId) {\n        return res.status(400).json({\n            error: \"userId is required\"\n        });\n    }\n    try {\n        // Check if supabaseAdmin is available\n        if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n            return res.status(500).json({\n                error: \"Supabase service role key not configured. Please add SUPABASE_SERVICE_ROLE_KEY to your .env.local file.\"\n            });\n        }\n        // Get X OAuth 2.0 credentials\n        const clientId = process.env.X_CLIENT_ID;\n        const clientSecret = process.env.X_CLIENT_SECRET;\n        if (!clientId || !clientSecret) {\n            return res.status(500).json({\n                error: \"X OAuth 2.0 credentials not configured\"\n            });\n        }\n        // Generate state parameter for security\n        const state = crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(32).toString(\"hex\");\n        // Generate code verifier and challenge for PKCE\n        const codeVerifier = crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(32).toString(\"base64url\");\n        const codeChallenge = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(\"sha256\").update(codeVerifier).digest(\"base64url\");\n        // Store OAuth state and code verifier temporarily\n        const { error: storeError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"x_oauth_tokens\").insert({\n            user_id: userId,\n            oauth_token: state,\n            oauth_token_secret: codeVerifier // Using oauth_token_secret field to store code verifier\n        });\n        if (storeError) {\n            console.error(\"Error storing OAuth state:\", storeError);\n            return res.status(500).json({\n                error: \"Failed to store OAuth state\"\n            });\n        }\n        // Build OAuth 2.0 authorization URL\n        const callbackUrl = `${process.env.NEXTAUTH_URL || \"http://localhost:3001\"}/api/x/callback`;\n        const scopes = \"tweet.read tweet.write users.read offline.access\";\n        const authUrl = new URL(\"https://twitter.com/i/oauth2/authorize\");\n        authUrl.searchParams.set(\"response_type\", \"code\");\n        authUrl.searchParams.set(\"client_id\", clientId);\n        authUrl.searchParams.set(\"redirect_uri\", callbackUrl);\n        authUrl.searchParams.set(\"scope\", scopes);\n        authUrl.searchParams.set(\"state\", state);\n        authUrl.searchParams.set(\"code_challenge\", codeChallenge);\n        authUrl.searchParams.set(\"code_challenge_method\", \"S256\");\n        return res.status(200).json({\n            authUrl: authUrl.toString(),\n            message: \"Redirecting to X for authorization...\"\n        });\n    } catch (error) {\n        console.error(\"Error generating auth URL:\", error);\n        return res.status(500).json({\n            error: \"Failed to generate authorization URL\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/x/auth-url.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();