"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/x/auth-url";
exports.ids = ["pages/api/x/auth-url"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/x/auth-url.ts */ \"(api)/./pages/api/x/auth-url.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/x/auth-url\",\n        pathname: \"/api/x/auth-url\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnglMkZhdXRoLXVybCZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnBhZ2VzJTJGYXBpJTJGeCUyRmF1dGgtdXJsLnRzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNMO0FBQzFEO0FBQ3NEO0FBQ3REO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyxxREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMscURBQVE7QUFDcEM7QUFDTyx3QkFBd0IsZ0hBQW1CO0FBQ2xEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8/MmIyYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXMvYXBpL3gvYXV0aC11cmwudHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS94L2F1dGgtdXJsXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkveC9hdXRoLXVybFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nlckamsrdiwkyyrxzntf.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./pages/api/x/auth-url.ts":
/*!*********************************!*\
  !*** ./pages/api/x/auth-url.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/supabase */ \"(api)/./lib/supabase.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const { userId } = req.body;\n    if (!userId) {\n        return res.status(400).json({\n            error: \"userId is required\"\n        });\n    }\n    try {\n        // Get X OAuth 2.0 credentials\n        const clientId = process.env.X_CLIENT_ID;\n        const clientSecret = process.env.X_CLIENT_SECRET;\n        if (!clientId || !clientSecret) {\n            return res.status(500).json({\n                error: \"X OAuth 2.0 credentials not configured\"\n            });\n        }\n        // Generate state parameter for security\n        const state = crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(32).toString(\"hex\");\n        // Generate code verifier and challenge for PKCE\n        const codeVerifier = crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(32).toString(\"base64url\");\n        const codeChallenge = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(\"sha256\").update(codeVerifier).digest(\"base64url\");\n        // Store OAuth state and code verifier temporarily\n        const { error: storeError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"x_oauth_tokens\").insert({\n            user_id: userId,\n            oauth_token: state,\n            oauth_token_secret: codeVerifier // Using oauth_token_secret field to store code verifier\n        });\n        if (storeError) {\n            console.error(\"Error storing OAuth state:\", storeError);\n            return res.status(500).json({\n                error: \"Failed to store OAuth state\"\n            });\n        }\n        // Build OAuth 2.0 authorization URL\n        const callbackUrl = `${process.env.NEXTAUTH_URL || \"http://localhost:3001\"}/api/x/callback`;\n        const scopes = \"tweet.read tweet.write users.read offline.access\";\n        const authUrl = new URL(\"https://twitter.com/i/oauth2/authorize\");\n        authUrl.searchParams.set(\"response_type\", \"code\");\n        authUrl.searchParams.set(\"client_id\", clientId);\n        authUrl.searchParams.set(\"redirect_uri\", callbackUrl);\n        authUrl.searchParams.set(\"scope\", scopes);\n        authUrl.searchParams.set(\"state\", state);\n        authUrl.searchParams.set(\"code_challenge\", codeChallenge);\n        authUrl.searchParams.set(\"code_challenge_method\", \"S256\");\n        return res.status(200).json({\n            authUrl: authUrl.toString(),\n            message: \"Redirecting to X for authorization...\"\n        });\n    } catch (error) {\n        console.error(\"Error generating auth URL:\", error);\n        return res.status(500).json({\n            error: \"Failed to generate authorization URL\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/x/auth-url.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();