"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/x/auth-url";
exports.ids = ["pages/api/x/auth-url"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "twitter-api-v2":
/*!*********************************!*\
  !*** external "twitter-api-v2" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("twitter-api-v2");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/x/auth-url.ts */ \"(api)/./pages/api/x/auth-url.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/x/auth-url\",\n        pathname: \"/api/x/auth-url\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnglMkZhdXRoLXVybCZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnBhZ2VzJTJGYXBpJTJGeCUyRmF1dGgtdXJsLnRzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNMO0FBQzFEO0FBQ3NEO0FBQ3REO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyxxREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMscURBQVE7QUFDcEM7QUFDTyx3QkFBd0IsZ0hBQW1CO0FBQ2xEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8/MmIyYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXMvYXBpL3gvYXV0aC11cmwudHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS94L2F1dGgtdXJsXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkveC9hdXRoLXVybFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/x/auth-url.ts":
/*!*********************************!*\
  !*** ./pages/api/x/auth-url.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! twitter-api-v2 */ \"twitter-api-v2\");\n/* harmony import */ var twitter_api_v2__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__);\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const { userId } = req.body;\n    if (!userId) {\n        return res.status(400).json({\n            error: \"userId is required\"\n        });\n    }\n    try {\n        // Get Twitter API credentials\n        const twitterApiKey = process.env.TWITTER_API_KEY;\n        const twitterApiSecret = process.env.TWITTER_API_SECRET;\n        if (!twitterApiKey || !twitterApiSecret) {\n            return res.status(500).json({\n                error: \"Twitter API credentials not configured\"\n            });\n        }\n        // Initialize Twitter client for OAuth\n        const twitterClient = new twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__.TwitterApi({\n            appKey: twitterApiKey,\n            appSecret: twitterApiSecret\n        });\n        // Generate OAuth URL - use 'oob' for desktop apps\n        const { url: authUrl, oauth_token, oauth_token_secret } = await twitterClient.generateAuthLink(\"oob\", {\n            linkMode: \"authorize\"\n        });\n        // For now, we'll return the oauth_token_secret to the client\n        // In production, this should be stored securely server-side\n        return res.status(200).json({\n            authUrl,\n            oauth_token,\n            oauth_token_secret,\n            message: \"After authorizing, you will receive a PIN code. Please enter it to complete the connection.\"\n        });\n    } catch (error) {\n        console.error(\"Error generating auth URL:\", error);\n        return res.status(500).json({\n            error: \"Failed to generate authorization URL\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9wYWdlcy9hcGkveC9hdXRoLXVybC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDNEM7QUFHN0IsZUFBZUMsUUFBUUMsR0FBbUIsRUFBRUMsR0FBb0I7SUFDN0UsSUFBSUQsSUFBSUUsTUFBTSxLQUFLLFFBQVE7UUFDekIsT0FBT0QsSUFBSUUsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztZQUFFQyxPQUFPO1FBQXFCO0lBQzVEO0lBRUEsTUFBTSxFQUFFQyxNQUFNLEVBQUUsR0FBR04sSUFBSU8sSUFBSTtJQUUzQixJQUFJLENBQUNELFFBQVE7UUFDWCxPQUFPTCxJQUFJRSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO1lBQUVDLE9BQU87UUFBcUI7SUFDNUQ7SUFFQSxJQUFJO1FBQ0YsOEJBQThCO1FBQzlCLE1BQU1HLGdCQUFnQkMsUUFBUUMsR0FBRyxDQUFDQyxlQUFlO1FBQ2pELE1BQU1DLG1CQUFtQkgsUUFBUUMsR0FBRyxDQUFDRyxrQkFBa0I7UUFFdkQsSUFBSSxDQUFDTCxpQkFBaUIsQ0FBQ0ksa0JBQWtCO1lBQ3ZDLE9BQU9YLElBQUlFLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7Z0JBQzFCQyxPQUFPO1lBQ1Q7UUFDRjtRQUVBLHNDQUFzQztRQUN0QyxNQUFNUyxnQkFBZ0IsSUFBSWhCLHNEQUFVQSxDQUFDO1lBQ25DaUIsUUFBUVA7WUFDUlEsV0FBV0o7UUFDYjtRQUVBLGtEQUFrRDtRQUNsRCxNQUFNLEVBQUVLLEtBQUtDLE9BQU8sRUFBRUMsV0FBVyxFQUFFQyxrQkFBa0IsRUFBRSxHQUFHLE1BQU1OLGNBQWNPLGdCQUFnQixDQUM1RixPQUNBO1lBQUVDLFVBQVU7UUFBWTtRQUcxQiw2REFBNkQ7UUFDN0QsNERBQTREO1FBRTVELE9BQU9yQixJQUFJRSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO1lBQzFCYztZQUNBQztZQUNBQztZQUNBRyxTQUFTO1FBQ1g7SUFFRixFQUFFLE9BQU9sQixPQUFPO1FBQ2RtQixRQUFRbkIsS0FBSyxDQUFDLDhCQUE4QkE7UUFDNUMsT0FBT0osSUFBSUUsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztZQUFFQyxPQUFPO1FBQXVDO0lBQzlFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLy4vcGFnZXMvYXBpL3gvYXV0aC11cmwudHM/YjM1NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0QXBpUmVxdWVzdCwgTmV4dEFwaVJlc3BvbnNlIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyBUd2l0dGVyQXBpIH0gZnJvbSAndHdpdHRlci1hcGktdjInO1xuaW1wb3J0IHsgc3VwYWJhc2UgfSBmcm9tICcuLi8uLi8uLi9saWIvc3VwYWJhc2UnO1xuXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBoYW5kbGVyKHJlcTogTmV4dEFwaVJlcXVlc3QsIHJlczogTmV4dEFwaVJlc3BvbnNlKSB7XG4gIGlmIChyZXEubWV0aG9kICE9PSAnUE9TVCcpIHtcbiAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDUpLmpzb24oeyBlcnJvcjogJ01ldGhvZCBub3QgYWxsb3dlZCcgfSk7XG4gIH1cblxuICBjb25zdCB7IHVzZXJJZCB9ID0gcmVxLmJvZHk7XG5cbiAgaWYgKCF1c2VySWQpIHtcbiAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDApLmpzb24oeyBlcnJvcjogJ3VzZXJJZCBpcyByZXF1aXJlZCcgfSk7XG4gIH1cblxuICB0cnkge1xuICAgIC8vIEdldCBUd2l0dGVyIEFQSSBjcmVkZW50aWFsc1xuICAgIGNvbnN0IHR3aXR0ZXJBcGlLZXkgPSBwcm9jZXNzLmVudi5UV0lUVEVSX0FQSV9LRVk7XG4gICAgY29uc3QgdHdpdHRlckFwaVNlY3JldCA9IHByb2Nlc3MuZW52LlRXSVRURVJfQVBJX1NFQ1JFVDtcblxuICAgIGlmICghdHdpdHRlckFwaUtleSB8fCAhdHdpdHRlckFwaVNlY3JldCkge1xuICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoNTAwKS5qc29uKHtcbiAgICAgICAgZXJyb3I6ICdUd2l0dGVyIEFQSSBjcmVkZW50aWFscyBub3QgY29uZmlndXJlZCdcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIEluaXRpYWxpemUgVHdpdHRlciBjbGllbnQgZm9yIE9BdXRoXG4gICAgY29uc3QgdHdpdHRlckNsaWVudCA9IG5ldyBUd2l0dGVyQXBpKHtcbiAgICAgIGFwcEtleTogdHdpdHRlckFwaUtleSxcbiAgICAgIGFwcFNlY3JldDogdHdpdHRlckFwaVNlY3JldCxcbiAgICB9KTtcblxuICAgIC8vIEdlbmVyYXRlIE9BdXRoIFVSTCAtIHVzZSAnb29iJyBmb3IgZGVza3RvcCBhcHBzXG4gICAgY29uc3QgeyB1cmw6IGF1dGhVcmwsIG9hdXRoX3Rva2VuLCBvYXV0aF90b2tlbl9zZWNyZXQgfSA9IGF3YWl0IHR3aXR0ZXJDbGllbnQuZ2VuZXJhdGVBdXRoTGluayhcbiAgICAgICdvb2InLFxuICAgICAgeyBsaW5rTW9kZTogJ2F1dGhvcml6ZScgfVxuICAgICk7XG5cbiAgICAvLyBGb3Igbm93LCB3ZSdsbCByZXR1cm4gdGhlIG9hdXRoX3Rva2VuX3NlY3JldCB0byB0aGUgY2xpZW50XG4gICAgLy8gSW4gcHJvZHVjdGlvbiwgdGhpcyBzaG91bGQgYmUgc3RvcmVkIHNlY3VyZWx5IHNlcnZlci1zaWRlXG5cbiAgICByZXR1cm4gcmVzLnN0YXR1cygyMDApLmpzb24oe1xuICAgICAgYXV0aFVybCxcbiAgICAgIG9hdXRoX3Rva2VuLFxuICAgICAgb2F1dGhfdG9rZW5fc2VjcmV0LFxuICAgICAgbWVzc2FnZTogJ0FmdGVyIGF1dGhvcml6aW5nLCB5b3Ugd2lsbCByZWNlaXZlIGEgUElOIGNvZGUuIFBsZWFzZSBlbnRlciBpdCB0byBjb21wbGV0ZSB0aGUgY29ubmVjdGlvbi4nXG4gICAgfSk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZW5lcmF0aW5nIGF1dGggVVJMOicsIGVycm9yKTtcbiAgICByZXR1cm4gcmVzLnN0YXR1cyg1MDApLmpzb24oeyBlcnJvcjogJ0ZhaWxlZCB0byBnZW5lcmF0ZSBhdXRob3JpemF0aW9uIFVSTCcgfSk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJUd2l0dGVyQXBpIiwiaGFuZGxlciIsInJlcSIsInJlcyIsIm1ldGhvZCIsInN0YXR1cyIsImpzb24iLCJlcnJvciIsInVzZXJJZCIsImJvZHkiLCJ0d2l0dGVyQXBpS2V5IiwicHJvY2VzcyIsImVudiIsIlRXSVRURVJfQVBJX0tFWSIsInR3aXR0ZXJBcGlTZWNyZXQiLCJUV0lUVEVSX0FQSV9TRUNSRVQiLCJ0d2l0dGVyQ2xpZW50IiwiYXBwS2V5IiwiYXBwU2VjcmV0IiwidXJsIiwiYXV0aFVybCIsIm9hdXRoX3Rva2VuIiwib2F1dGhfdG9rZW5fc2VjcmV0IiwiZ2VuZXJhdGVBdXRoTGluayIsImxpbmtNb2RlIiwibWVzc2FnZSIsImNvbnNvbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./pages/api/x/auth-url.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();