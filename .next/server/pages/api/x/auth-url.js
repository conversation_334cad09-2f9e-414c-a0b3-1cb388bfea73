"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/x/auth-url";
exports.ids = ["pages/api/x/auth-url"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "twitter-api-v2":
/*!*********************************!*\
  !*** external "twitter-api-v2" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("twitter-api-v2");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/x/auth-url.ts */ \"(api)/./pages/api/x/auth-url.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/x/auth-url\",\n        pathname: \"/api/x/auth-url\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_x_auth_url_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnglMkZhdXRoLXVybCZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnBhZ2VzJTJGYXBpJTJGeCUyRmF1dGgtdXJsLnRzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNMO0FBQzFEO0FBQ3NEO0FBQ3REO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyxxREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMscURBQVE7QUFDcEM7QUFDTyx3QkFBd0IsZ0hBQW1CO0FBQ2xEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8/MmIyYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXMvYXBpL3gvYXV0aC11cmwudHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS94L2F1dGgtdXJsXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkveC9hdXRoLXVybFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nlckamsrdiwkyyrxzntf.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./pages/api/x/auth-url.ts":
/*!*********************************!*\
  !*** ./pages/api/x/auth-url.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! twitter-api-v2 */ \"twitter-api-v2\");\n/* harmony import */ var twitter_api_v2__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/supabase */ \"(api)/./lib/supabase.ts\");\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const { userId } = req.body;\n    if (!userId) {\n        return res.status(400).json({\n            error: \"userId is required\"\n        });\n    }\n    try {\n        // Get Twitter API credentials\n        const twitterApiKey = process.env.X_API_KEY;\n        const twitterApiSecret = process.env.X_API_SECRET_KEY;\n        if (!twitterApiKey || !twitterApiSecret) {\n            return res.status(500).json({\n                error: \"Twitter API credentials not configured\"\n            });\n        }\n        // Initialize Twitter client for OAuth\n        const twitterClient = new twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__.TwitterApi({\n            appKey: twitterApiKey,\n            appSecret: twitterApiSecret\n        });\n        // Generate OAuth URL with callback URL\n        const callbackUrl = `${process.env.NEXTAUTH_URL || \"http://localhost:3002\"}/api/x/callback`;\n        const { url: authUrl, oauth_token, oauth_token_secret } = await twitterClient.generateAuthLink(callbackUrl, {\n            linkMode: \"authorize\"\n        });\n        // Store OAuth tokens in database temporarily\n        const { error: storeError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"x_oauth_tokens\").insert({\n            user_id: userId,\n            oauth_token,\n            oauth_token_secret\n        });\n        if (storeError) {\n            console.error(\"Error storing OAuth tokens:\", storeError);\n            return res.status(500).json({\n                error: \"Failed to store OAuth tokens\"\n            });\n        }\n        return res.status(200).json({\n            authUrl,\n            message: \"Redirecting to X for authorization...\"\n        });\n    } catch (error) {\n        console.error(\"Error generating auth URL:\", error);\n        return res.status(500).json({\n            error: \"Failed to generate authorization URL\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/x/auth-url.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fauth-url&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fauth-url.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();