"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/x/callback";
exports.ids = ["pages/api/x/callback"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fcallback&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fcallback.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fcallback&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fcallback.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_x_callback_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/x/callback.ts */ \"(api)/./pages/api/x/callback.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_callback_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_callback_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/x/callback\",\n        pathname: \"/api/x/callback\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_x_callback_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnglMkZjYWxsYmFjayZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnBhZ2VzJTJGYXBpJTJGeCUyRmNhbGxiYWNrLnRzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNMO0FBQzFEO0FBQ3NEO0FBQ3REO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyxxREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMscURBQVE7QUFDcEM7QUFDTyx3QkFBd0IsZ0hBQW1CO0FBQ2xEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8/NjQ0MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXMvYXBpL3gvY2FsbGJhY2sudHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS94L2NhbGxiYWNrXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkveC9jYWxsYmFja1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fcallback&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fcallback.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nlckamsrdiwkyyrxzntf.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Client-side Supabase client (with RLS)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Server-side Supabase client (bypasses RLS) - only create if service key exists\nconst supabaseAdmin = supabaseServiceKey ? (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./pages/api/x/callback.ts":
/*!*********************************!*\
  !*** ./pages/api/x/callback.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/supabase */ \"(api)/./lib/supabase.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const { code, state, error: oauthError } = req.query;\n    // Handle OAuth errors\n    if (oauthError) {\n        console.error(\"OAuth error:\", oauthError);\n        return res.redirect(\"/settings?tab=integrations&error=oauth_denied\");\n    }\n    if (!code || !state) {\n        return res.redirect(\"/settings?tab=integrations&error=oauth_failed\");\n    }\n    try {\n        // Check if supabaseAdmin is available\n        if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n            console.error(\"Supabase service role key not configured\");\n            return res.redirect(\"/settings?tab=integrations&error=config_error\");\n        }\n        // Get the stored OAuth state and code verifier\n        const { data: oauthData, error: fetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"x_oauth_tokens\").select(\"*\").eq(\"oauth_token\", state) // state is stored in oauth_token field\n        .single();\n        if (fetchError || !oauthData) {\n            console.error(\"OAuth state not found:\", fetchError);\n            return res.redirect(\"/settings?tab=integrations&error=invalid_state\");\n        }\n        // Get X OAuth 2.0 credentials\n        const clientId = process.env.X_CLIENT_ID;\n        const clientSecret = process.env.X_CLIENT_SECRET;\n        if (!clientId || !clientSecret) {\n            return res.redirect(\"/settings?tab=integrations&error=config_error\");\n        }\n        // Exchange authorization code for access token\n        const callbackUrl = `${process.env.NEXTAUTH_URL || \"http://localhost:3001\"}/api/x/callback`;\n        const codeVerifier = oauthData.oauth_token_secret; // code verifier is stored in oauth_token_secret field\n        const tokenResponse = await fetch(\"https://api.twitter.com/2/oauth2/token\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\",\n                \"Authorization\": `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString(\"base64\")}`\n            },\n            body: new URLSearchParams({\n                grant_type: \"authorization_code\",\n                code: code,\n                redirect_uri: callbackUrl,\n                code_verifier: codeVerifier\n            })\n        });\n        if (!tokenResponse.ok) {\n            const errorData = await tokenResponse.text();\n            console.error(\"Token exchange failed:\", errorData);\n            return res.redirect(\"/settings?tab=integrations&error=token_exchange_failed\");\n        }\n        const tokenData = await tokenResponse.json();\n        const { access_token, refresh_token } = tokenData;\n        // Get user information using the access token\n        const userResponse = await fetch(\"https://api.twitter.com/2/users/me?user.fields=name,username,profile_image_url\", {\n            headers: {\n                \"Authorization\": `Bearer ${access_token}`\n            }\n        });\n        if (!userResponse.ok) {\n            console.error(\"Failed to fetch user info\");\n            return res.redirect(\"/settings?tab=integrations&error=user_fetch_failed\");\n        }\n        const userData = await userResponse.json();\n        const userInfo = userData.data;\n        // Store the user's X account information\n        const { error: storeError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"x_accounts\").upsert({\n            user_id: oauthData.user_id,\n            x_user_id: userInfo.id,\n            username: userInfo.username,\n            name: userInfo.name,\n            profile_image_url: userInfo.profile_image_url,\n            access_token: access_token,\n            access_secret: refresh_token || \"\",\n            is_active: true\n        });\n        if (storeError) {\n            console.error(\"Error storing X account:\", storeError);\n            return res.redirect(\"/settings?tab=integrations&error=store_failed\");\n        }\n        // Clean up OAuth tokens\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"x_oauth_tokens\").delete().eq(\"oauth_token\", state);\n        // Redirect back to settings with success\n        return res.redirect(\"/settings?tab=integrations&success=connected\");\n    } catch (error) {\n        console.error(\"OAuth callback error:\", error);\n        return res.redirect(\"/settings?tab=integrations&error=oauth_failed\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/x/callback.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fcallback&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fcallback.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();