"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/x/verify-pin";
exports.ids = ["pages/api/x/verify-pin"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "twitter-api-v2":
/*!*********************************!*\
  !*** external "twitter-api-v2" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("twitter-api-v2");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fverify-pin&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fverify-pin.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fverify-pin&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fverify-pin.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_x_verify_pin_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/x/verify-pin.ts */ \"(api)/./pages/api/x/verify-pin.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_verify_pin_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_verify_pin_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/x/verify-pin\",\n        pathname: \"/api/x/verify-pin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_x_verify_pin_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnglMkZ2ZXJpZnktcGluJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGcGFnZXMlMkZhcGklMkZ4JTJGdmVyaWZ5LXBpbi50cyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDTDtBQUMxRDtBQUN3RDtBQUN4RDtBQUNBLGlFQUFlLHdFQUFLLENBQUMsdURBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLHVEQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLGdIQUFtQjtBQUNsRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL2V4aWUtYWkvP2FmYzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3BhZ2VzL2FwaS94L3ZlcmlmeS1waW4udHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS94L3ZlcmlmeS1waW5cIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS94L3ZlcmlmeS1waW5cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIlxuICAgIH0sXG4gICAgdXNlcmxhbmRcbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYWdlcy1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fverify-pin&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fverify-pin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nlckamsrdiwkyyrxzntf.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./pages/api/x/verify-pin.ts":
/*!***********************************!*\
  !*** ./pages/api/x/verify-pin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! twitter-api-v2 */ \"twitter-api-v2\");\n/* harmony import */ var twitter_api_v2__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/supabase */ \"(api)/./lib/supabase.ts\");\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const { oauth_token, oauth_token_secret, pin, userId } = req.body;\n    if (!oauth_token || !oauth_token_secret || !pin || !userId) {\n        return res.status(400).json({\n            error: \"oauth_token, oauth_token_secret, pin, and userId are required\"\n        });\n    }\n    try {\n        // Get Twitter API credentials\n        const twitterApiKey = process.env.TWITTER_API_KEY;\n        const twitterApiSecret = process.env.TWITTER_API_SECRET;\n        if (!twitterApiKey || !twitterApiSecret) {\n            return res.status(500).json({\n                error: \"Twitter API credentials not configured\"\n            });\n        }\n        // Initialize Twitter client with OAuth tokens\n        const twitterClient = new twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__.TwitterApi({\n            appKey: twitterApiKey,\n            appSecret: twitterApiSecret,\n            accessToken: oauth_token,\n            accessSecret: oauth_token_secret\n        });\n        // Complete the OAuth process with PIN\n        const { accessToken, accessSecret } = await twitterClient.login(pin);\n        // Create authenticated client\n        const authenticatedClient = new twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__.TwitterApi({\n            appKey: twitterApiKey,\n            appSecret: twitterApiSecret,\n            accessToken,\n            accessSecret\n        });\n        // Get user information\n        const userInfo = await authenticatedClient.v2.me({\n            \"user.fields\": [\n                \"name\",\n                \"username\",\n                \"profile_image_url\"\n            ]\n        });\n        // Store the account info in user_profiles table\n        const xAccountData = {\n            x_user_id: userInfo.data.id,\n            username: userInfo.data.username,\n            name: userInfo.data.name,\n            profile_image_url: userInfo.data.profile_image_url,\n            access_token: accessToken,\n            access_secret: accessSecret,\n            connected_at: new Date().toISOString(),\n            is_active: true\n        };\n        console.log(\"Attempting to store X account data:\", {\n            userId,\n            username: xAccountData.username,\n            hasAccessToken: !!xAccountData.access_token\n        });\n        try {\n            const { data: storeData, error: storeError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"user_profiles\").upsert({\n                user_id: userId,\n                x_account_info: xAccountData\n            }).select();\n            console.log(\"Store result:\", {\n                storeData,\n                storeError\n            });\n            if (storeError) {\n                console.error(\"Error storing X account:\", storeError);\n                return res.status(500).json({\n                    error: \"Failed to save X account connection\",\n                    details: storeError.message\n                });\n            }\n            // Verify the data was saved by reading it back\n            const { data: verifyData, error: verifyError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"user_profiles\").select(\"x_account_info\").eq(\"user_id\", userId).single();\n            console.log(\"Verification read:\", {\n                verifyData,\n                verifyError\n            });\n            if (verifyError || !verifyData?.x_account_info) {\n                console.error(\"Failed to verify X account was saved:\", verifyError);\n                return res.status(500).json({\n                    error: \"X account connection could not be verified\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error storing X account:\", error);\n            return res.status(500).json({\n                error: \"Failed to save X account connection\",\n                details: error.message\n            });\n        }\n        return res.status(200).json({\n            success: true,\n            message: \"X account connected successfully\",\n            accountInfo: {\n                username: userInfo.data.username,\n                name: userInfo.data.name,\n                profile_image_url: userInfo.data.profile_image_url\n            }\n        });\n    } catch (error) {\n        console.error(\"PIN verification error:\", error);\n        if (error.code === 401) {\n            return res.status(400).json({\n                error: \"Invalid PIN code. Please try again.\"\n            });\n        }\n        return res.status(500).json({\n            error: \"Failed to verify PIN code\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/x/verify-pin.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fverify-pin&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fverify-pin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();