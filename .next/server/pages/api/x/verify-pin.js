"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/x/verify-pin";
exports.ids = ["pages/api/x/verify-pin"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "twitter-api-v2":
/*!*********************************!*\
  !*** external "twitter-api-v2" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("twitter-api-v2");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fverify-pin&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fverify-pin.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fverify-pin&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fverify-pin.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_x_verify_pin_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/x/verify-pin.ts */ \"(api)/./pages/api/x/verify-pin.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_verify_pin_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_x_verify_pin_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/x/verify-pin\",\n        pathname: \"/api/x/verify-pin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_x_verify_pin_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnglMkZ2ZXJpZnktcGluJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGcGFnZXMlMkZhcGklMkZ4JTJGdmVyaWZ5LXBpbi50cyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDTDtBQUMxRDtBQUN3RDtBQUN4RDtBQUNBLGlFQUFlLHdFQUFLLENBQUMsdURBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLHVEQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLGdIQUFtQjtBQUNsRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL2V4aWUtYWkvP2FmYzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3BhZ2VzL2FwaS94L3ZlcmlmeS1waW4udHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS94L3ZlcmlmeS1waW5cIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS94L3ZlcmlmeS1waW5cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIlxuICAgIH0sXG4gICAgdXNlcmxhbmRcbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYWdlcy1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fverify-pin&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fverify-pin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nlckamsrdiwkyyrxzntf.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sY2thbXNyZGl3a3l5cnh6bnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDEzMjEsImV4cCI6MjA2Mzc3NzMyMX0.y6NZnzjb_6kX0UVq2Y616IV8MfWL8Zlg8Nvz_vq7nlw\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./pages/api/x/verify-pin.ts":
/*!***********************************!*\
  !*** ./pages/api/x/verify-pin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! twitter-api-v2 */ \"twitter-api-v2\");\n/* harmony import */ var twitter_api_v2__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/supabase */ \"(api)/./lib/supabase.ts\");\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const { oauth_token, oauth_token_secret, pin, userId } = req.body;\n    if (!oauth_token || !oauth_token_secret || !pin || !userId) {\n        return res.status(400).json({\n            error: \"oauth_token, oauth_token_secret, pin, and userId are required\"\n        });\n    }\n    try {\n        // Get Twitter API credentials\n        const twitterApiKey = process.env.TWITTER_API_KEY;\n        const twitterApiSecret = process.env.TWITTER_API_SECRET;\n        if (!twitterApiKey || !twitterApiSecret) {\n            return res.status(500).json({\n                error: \"Twitter API credentials not configured\"\n            });\n        }\n        // Initialize Twitter client with OAuth tokens\n        const twitterClient = new twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__.TwitterApi({\n            appKey: twitterApiKey,\n            appSecret: twitterApiSecret,\n            accessToken: oauth_token,\n            accessSecret: oauth_token_secret\n        });\n        // Complete the OAuth process with PIN\n        const { accessToken, accessSecret } = await twitterClient.login(pin);\n        // Create authenticated client\n        const authenticatedClient = new twitter_api_v2__WEBPACK_IMPORTED_MODULE_0__.TwitterApi({\n            appKey: twitterApiKey,\n            appSecret: twitterApiSecret,\n            accessToken,\n            accessSecret\n        });\n        // Get user information\n        const userInfo = await authenticatedClient.v2.me({\n            \"user.fields\": [\n                \"name\",\n                \"username\",\n                \"profile_image_url\"\n            ]\n        });\n        // For now, we'll store the account info in user_profiles table\n        // In production, this should be in a dedicated x_accounts table\n        try {\n            const { error: storeError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"user_profiles\").upsert({\n                user_id: userId,\n                x_account_info: {\n                    x_user_id: userInfo.data.id,\n                    username: userInfo.data.username,\n                    name: userInfo.data.name,\n                    profile_image_url: userInfo.data.profile_image_url,\n                    access_token: accessToken,\n                    access_secret: accessSecret,\n                    connected_at: new Date().toISOString(),\n                    is_active: true\n                }\n            });\n            if (storeError) {\n                console.error(\"Error storing X account:\", storeError);\n            // Continue anyway - we'll return the account info to the client\n            }\n        } catch (error) {\n            console.error(\"Error storing X account:\", error);\n        // Continue anyway\n        }\n        return res.status(200).json({\n            success: true,\n            message: \"X account connected successfully\",\n            accountInfo: {\n                username: userInfo.data.username,\n                name: userInfo.data.name,\n                profile_image_url: userInfo.data.profile_image_url\n            }\n        });\n    } catch (error) {\n        console.error(\"PIN verification error:\", error);\n        if (error.code === 401) {\n            return res.status(400).json({\n                error: \"Invalid PIN code. Please try again.\"\n            });\n        }\n        return res.status(500).json({\n            error: \"Failed to verify PIN code\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/x/verify-pin.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fx%2Fverify-pin&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fx%2Fverify-pin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();