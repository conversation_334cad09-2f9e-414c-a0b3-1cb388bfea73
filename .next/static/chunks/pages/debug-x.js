/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/debug-x"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fdebug-x.tsx&page=%2Fdebug-x!":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fdebug-x.tsx&page=%2Fdebug-x! ***!
  \******************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/debug-x\",\n      function () {\n        return __webpack_require__(/*! ./pages/debug-x.tsx */ \"./pages/debug-x.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/debug-x\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD0lMkZVc2VycyUyRmNhbGVsYW5lJTJGRG9jdW1lbnRzJTJGR2l0SHViJTJGRXhpZSUyRnBhZ2VzJTJGZGVidWcteC50c3gmcGFnZT0lMkZkZWJ1Zy14ISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLGdEQUFxQjtBQUM1QztBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/ZjcwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL2RlYnVnLXhcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3BhZ2VzL2RlYnVnLXgudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9kZWJ1Zy14XCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fdebug-x.tsx&page=%2Fdebug-x!\n"));

/***/ }),

/***/ "./pages/debug-x.tsx":
/*!***************************!*\
  !*** ./pages/debug-x.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst DebugX = ()=>{\n    _s();\n    const { user } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const [debugInfo, setDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const checkXAccount = async ()=>{\n        if (!user) {\n            setDebugInfo({\n                error: \"No user logged in\"\n            });\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/debug/check-x-account?userId=\".concat(user.id));\n            const data = await response.json();\n            setDebugInfo(data);\n        } catch (error) {\n            setDebugInfo({\n                error: error.message\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const checkAccountStatus = async ()=>{\n        if (!user) {\n            setDebugInfo({\n                error: \"No user logged in\"\n            });\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/x/account-status?userId=\".concat(user.id));\n            const data = await response.json();\n            setDebugInfo({\n                accountStatus: data\n            });\n        } catch (error) {\n            setDebugInfo({\n                error: error.message\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px\",\n            maxWidth: \"800px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"X Account Debug\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/debug-x.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"User:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/debug-x.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined),\n                    \" \",\n                    user ? user.email : \"Not logged in\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/debug-x.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"User ID:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/debug-x.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    \" \",\n                    user ? user.id : \"N/A\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/debug-x.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: checkXAccount,\n                        disabled: loading || !user,\n                        style: {\n                            padding: \"12px 24px\",\n                            background: \"#1DA1F2\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"8px\",\n                            cursor: \"pointer\",\n                            marginRight: \"10px\"\n                        },\n                        children: loading ? \"Loading...\" : \"Check X Account in DB\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/debug-x.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: checkAccountStatus,\n                        disabled: loading || !user,\n                        style: {\n                            padding: \"12px 24px\",\n                            background: \"#10B981\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"8px\",\n                            cursor: \"pointer\"\n                        },\n                        children: loading ? \"Loading...\" : \"Check Account Status API\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/debug-x.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/debug-x.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            debugInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"20px\",\n                    background: \"#f5f5f5\",\n                    borderRadius: \"8px\",\n                    marginTop: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Debug Info:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/debug-x.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"15px\",\n                            borderRadius: \"4px\",\n                            overflow: \"auto\",\n                            fontSize: \"12px\"\n                        },\n                        children: JSON.stringify(debugInfo, null, 2)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/debug-x.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/debug-x.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/debug-x.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DebugX, \"MeIHMTTo6nhpko0B2x2nxoNEaGE=\", false, function() {\n    return [\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__.useUser\n    ];\n});\n_c = DebugX;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DebugX);\nvar _c;\n$RefreshReg$(_c, \"DebugX\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/debug-x.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fdebug-x.tsx&page=%2Fdebug-x!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);