/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/setup-database"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fsetup-database.tsx&page=%2Fsetup-database!":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fsetup-database.tsx&page=%2Fsetup-database! ***!
  \********************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/setup-database\",\n      function () {\n        return __webpack_require__(/*! ./pages/setup-database.tsx */ \"./pages/setup-database.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/setup-database\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD0lMkZVc2VycyUyRmNhbGVsYW5lJTJGRG9jdW1lbnRzJTJGR2l0SHViJTJGRXhpZSUyRnBhZ2VzJTJGc2V0dXAtZGF0YWJhc2UudHN4JnBhZ2U9JTJGc2V0dXAtZGF0YWJhc2UhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsOERBQTRCO0FBQ25EO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz81NzZlIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvc2V0dXAtZGF0YWJhc2VcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3BhZ2VzL3NldHVwLWRhdGFiYXNlLnRzeFwiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvc2V0dXAtZGF0YWJhc2VcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fsetup-database.tsx&page=%2Fsetup-database!\n"));

/***/ }),

/***/ "./pages/setup-database.tsx":
/*!**********************************!*\
  !*** ./pages/setup-database.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst SetupDatabase = ()=>{\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const runMigration = async ()=>{\n        setLoading(true);\n        setResult(\"\");\n        try {\n            const response = await fetch(\"/api/setup/add-x-account-column\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setResult(\"✅ Success! X account column is ready.\");\n            } else {\n                setResult(\"❌ Migration needed. Please run this SQL in your Supabase SQL editor:\\n\\n\".concat(data.sql));\n            }\n        } catch (error) {\n            setResult(\"❌ Error: \".concat(error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px\",\n            maxWidth: \"800px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Database Setup\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"This will add the missing \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                children: \"x_account_info\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 38\n                            }, undefined),\n                            \" column to the \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                children: \"user_profiles\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 80\n                            }, undefined),\n                            \" table.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This column is required for X account connections to persist properly.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: runMigration,\n                disabled: loading,\n                style: {\n                    padding: \"12px 24px\",\n                    background: \"#10B981\",\n                    color: \"white\",\n                    border: \"none\",\n                    borderRadius: \"8px\",\n                    cursor: \"pointer\",\n                    fontSize: \"16px\"\n                },\n                children: loading ? \"Running Migration...\" : \"Add X Account Column\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"20px\",\n                    background: result.includes(\"✅\") ? \"#F0FDF4\" : \"#FEF2F2\",\n                    border: \"1px solid \".concat(result.includes(\"✅\") ? \"#BBF7D0\" : \"#FECACA\"),\n                    borderRadius: \"8px\",\n                    marginTop: \"20px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    style: {\n                        margin: 0,\n                        whiteSpace: \"pre-wrap\",\n                        fontFamily: \"monospace\"\n                    },\n                    children: result\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: \"40px\",\n                    padding: \"20px\",\n                    background: \"#F8FAFC\",\n                    borderRadius: \"8px\",\n                    border: \"1px solid #E2E8F0\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Manual Setup (if needed)\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"If the automatic migration doesn't work, run this SQL in your Supabase SQL editor:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        style: {\n                            background: \"#1F2937\",\n                            color: \"#F9FAFB\",\n                            padding: \"15px\",\n                            borderRadius: \"6px\",\n                            overflow: \"auto\"\n                        },\n                        children: \"ALTER TABLE user_profiles ADD COLUMN x_account_info JSONB;\\n\\n-- Optional: Add index for better performance\\nCREATE INDEX IF NOT EXISTS idx_user_profiles_x_account_info \\nON user_profiles USING GIN (x_account_info);\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-database.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SetupDatabase, \"+f+5BVLsSkcBSMc6rpBNO90CVC0=\");\n_c = SetupDatabase;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SetupDatabase);\nvar _c;\n$RefreshReg$(_c, \"SetupDatabase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/setup-database.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fsetup-database.tsx&page=%2Fsetup-database!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);