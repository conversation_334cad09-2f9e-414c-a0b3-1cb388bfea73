/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/setup-x-tables"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fsetup-x-tables.tsx&page=%2Fsetup-x-tables!":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fsetup-x-tables.tsx&page=%2Fsetup-x-tables! ***!
  \********************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/setup-x-tables\",\n      function () {\n        return __webpack_require__(/*! ./pages/setup-x-tables.tsx */ \"./pages/setup-x-tables.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/setup-x-tables\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD0lMkZVc2VycyUyRmNhbGVsYW5lJTJGRG9jdW1lbnRzJTJGR2l0SHViJTJGRXhpZSUyRnBhZ2VzJTJGc2V0dXAteC10YWJsZXMudHN4JnBhZ2U9JTJGc2V0dXAteC10YWJsZXMhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsOERBQTRCO0FBQ25EO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz9jNGVkIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvc2V0dXAteC10YWJsZXNcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3BhZ2VzL3NldHVwLXgtdGFibGVzLnRzeFwiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvc2V0dXAteC10YWJsZXNcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fsetup-x-tables.tsx&page=%2Fsetup-x-tables!\n"));

/***/ }),

/***/ "./pages/setup-x-tables.tsx":
/*!**********************************!*\
  !*** ./pages/setup-x-tables.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst SetupXTables = ()=>{\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const createTables = async ()=>{\n        setLoading(true);\n        setResult(\"Creating X account tables...\");\n        try {\n            const response = await fetch(\"/api/setup/create-x-tables\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setResult(\"✅ Success! X account tables created successfully.\");\n            } else {\n                setResult(\"❌ Error: \".concat(data.error, \"\\n\\nPlease run this SQL manually in Supabase:\\n\\n\").concat(data.sql));\n            }\n        } catch (error) {\n            setResult(\"❌ Error: \".concat(error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px\",\n            maxWidth: \"800px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Setup X Account Tables\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This will create the proper database tables for X account connections:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"x_accounts\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" - Store connected X account info\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"x_oauth_tokens\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" - Temporary OAuth tokens\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: createTables,\n                disabled: loading,\n                style: {\n                    padding: \"12px 24px\",\n                    background: \"#1DA1F2\",\n                    color: \"white\",\n                    border: \"none\",\n                    borderRadius: \"8px\",\n                    cursor: \"pointer\",\n                    fontSize: \"16px\"\n                },\n                children: loading ? \"Creating Tables...\" : \"Create X Account Tables\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"20px\",\n                    background: result.includes(\"✅\") ? \"#F0FDF4\" : \"#FEF2F2\",\n                    border: \"1px solid \".concat(result.includes(\"✅\") ? \"#BBF7D0\" : \"#FECACA\"),\n                    borderRadius: \"8px\",\n                    marginTop: \"20px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    style: {\n                        margin: 0,\n                        whiteSpace: \"pre-wrap\",\n                        fontFamily: \"monospace\"\n                    },\n                    children: result\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: \"40px\",\n                    padding: \"20px\",\n                    background: \"#F8FAFC\",\n                    borderRadius: \"8px\",\n                    border: \"1px solid #E2E8F0\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Manual Setup (if needed)\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"If the automatic setup doesn't work, run this SQL in your Supabase SQL editor:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        style: {\n                            background: \"#1F2937\",\n                            color: \"#F9FAFB\",\n                            padding: \"15px\",\n                            borderRadius: \"6px\",\n                            overflow: \"auto\",\n                            fontSize: \"12px\"\n                        },\n                        children: '-- Create X account connection tables\\nCREATE TABLE IF NOT EXISTS x_accounts (\\n  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\\n  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\\n  x_user_id VARCHAR(255) NOT NULL,\\n  username VARCHAR(255) NOT NULL,\\n  name VARCHAR(255),\\n  profile_image_url TEXT,\\n  access_token TEXT NOT NULL,\\n  access_secret TEXT NOT NULL,\\n  is_active BOOLEAN DEFAULT true,\\n  connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\\n  disconnected_at TIMESTAMP WITH TIME ZONE,\\n  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\\n  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\\n  UNIQUE(user_id, x_user_id)\\n);\\n\\n-- Create temporary OAuth tokens table\\nCREATE TABLE IF NOT EXISTS x_oauth_tokens (\\n  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\\n  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\\n  oauth_token VARCHAR(255) NOT NULL,\\n  oauth_token_secret VARCHAR(255) NOT NULL,\\n  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\\n  UNIQUE(oauth_token)\\n);\\n\\n-- Enable Row Level Security\\nALTER TABLE x_accounts ENABLE ROW LEVEL SECURITY;\\nALTER TABLE x_oauth_tokens ENABLE ROW LEVEL SECURITY;\\n\\n-- Create policies\\nCREATE POLICY \"Users can manage their own X accounts\" ON x_accounts\\n  FOR ALL USING (auth.uid() = user_id);\\n\\nCREATE POLICY \"Users can manage their own OAuth tokens\" ON x_oauth_tokens\\n  FOR ALL USING (auth.uid() = user_id);'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/setup-x-tables.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SetupXTables, \"+f+5BVLsSkcBSMc6rpBNO90CVC0=\");\n_c = SetupXTables;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SetupXTables);\nvar _c;\n$RefreshReg$(_c, \"SetupXTables\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/setup-x-tables.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fsetup-x-tables.tsx&page=%2Fsetup-x-tables!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);