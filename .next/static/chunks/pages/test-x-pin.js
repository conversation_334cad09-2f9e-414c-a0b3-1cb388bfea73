/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/test-x-pin"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Ftest-x-pin.tsx&page=%2Ftest-x-pin!":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Ftest-x-pin.tsx&page=%2Ftest-x-pin! ***!
  \************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/test-x-pin\",\n      function () {\n        return __webpack_require__(/*! ./pages/test-x-pin.tsx */ \"./pages/test-x-pin.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/test-x-pin\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD0lMkZVc2VycyUyRmNhbGVsYW5lJTJGRG9jdW1lbnRzJTJGR2l0SHViJTJGRXhpZSUyRnBhZ2VzJTJGdGVzdC14LXBpbi50c3gmcGFnZT0lMkZ0ZXN0LXgtcGluISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLHNEQUF3QjtBQUMvQztBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/OTJjYSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL3Rlc3QteC1waW5cIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3BhZ2VzL3Rlc3QteC1waW4udHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi90ZXN0LXgtcGluXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Ftest-x-pin.tsx&page=%2Ftest-x-pin!\n"));

/***/ }),

/***/ "./pages/test-x-pin.tsx":
/*!******************************!*\
  !*** ./pages/test-x-pin.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst TestXPin = ()=>{\n    _s();\n    const { user } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const [pin, setPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [oauthToken, setOauthToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [oauthTokenSecret, setOauthTokenSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleGetAuthUrl = async ()=>{\n        if (!user) {\n            setResult(\"Please log in first\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/x/auth-url\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                setOauthToken(data.oauth_token);\n                setOauthTokenSecret(data.oauth_token_secret);\n                setResult(\"Auth URL generated. OAuth Token: \".concat(data.oauth_token.substring(0, 20), \"...\"));\n                window.open(data.authUrl, \"_blank\");\n            } else {\n                setResult(\"Error: \".concat(data.error));\n            }\n        } catch (error) {\n            setResult(\"Error: \".concat(error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVerifyPin = async ()=>{\n        if (!user || !pin || !oauthToken || !oauthTokenSecret) {\n            setResult(\"Missing required data\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/x/verify-pin\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    oauth_token: oauthToken,\n                    oauth_token_secret: oauthTokenSecret,\n                    pin: pin.trim(),\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                setResult(\"Success! Connected account: @\".concat(data.accountInfo.username));\n            } else {\n                setResult(\"Error: \".concat(data.error));\n            }\n        } catch (error) {\n            setResult(\"Error: \".concat(error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px\",\n            maxWidth: \"600px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Test X Account Connection\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"User:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    \" \",\n                    user ? user.email : \"Not logged in\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"20px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleGetAuthUrl,\n                    disabled: loading || !user,\n                    style: {\n                        padding: \"12px 24px\",\n                        background: \"#1DA1F2\",\n                        color: \"white\",\n                        border: \"none\",\n                        borderRadius: \"8px\",\n                        cursor: \"pointer\",\n                        marginRight: \"10px\"\n                    },\n                    children: loading ? \"Loading...\" : \"Get X Auth URL\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined),\n            oauthToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: \"10px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"OAuth Token:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, undefined),\n                            \" \",\n                            oauthToken.substring(0, 20),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: \"10px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Enter PIN from X:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: pin,\n                                    onChange: (e)=>setPin(e.target.value),\n                                    placeholder: \"Enter PIN code\",\n                                    style: {\n                                        padding: \"8px\",\n                                        fontSize: \"16px\",\n                                        border: \"1px solid #ccc\",\n                                        borderRadius: \"4px\",\n                                        width: \"200px\",\n                                        marginTop: \"5px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleVerifyPin,\n                        disabled: loading || !pin,\n                        style: {\n                            padding: \"12px 24px\",\n                            background: \"#10B981\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"8px\",\n                            cursor: \"pointer\"\n                        },\n                        children: loading ? \"Verifying...\" : \"Verify PIN\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, undefined),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"15px\",\n                    background: result.includes(\"Error\") ? \"#FEE2E2\" : \"#F0FDF4\",\n                    border: \"1px solid \".concat(result.includes(\"Error\") ? \"#FECACA\" : \"#BBF7D0\"),\n                    borderRadius: \"8px\",\n                    marginTop: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Result:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, undefined),\n                    \" \",\n                    result\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/test-x-pin.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TestXPin, \"tcR9DIb1gzBnNy4wBB3DwAAV5wo=\", false, function() {\n    return [\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_2__.useUser\n    ];\n});\n_c = TestXPin;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TestXPin);\nvar _c;\n$RefreshReg$(_c, \"TestXPin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/test-x-pin.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Ftest-x-pin.tsx&page=%2Ftest-x-pin!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);