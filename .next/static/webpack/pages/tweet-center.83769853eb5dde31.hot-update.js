"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!lucide-react */ \"__barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_AgentEChatSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/AgentEChatSimple */ \"./components/AgentEChatSimple.tsx\");\n// pages/tweet-center.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst TweetCenterPage = ()=>{\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAgentE, setShowAgentE] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"single\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        borderHover: \"#E8D5C4\",\n        surface: \"#FFFFFF\",\n        surfaceHover: \"#F9F7F4\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Real AI prediction based on content context\n    const generateContextualSuggestion = (text)=>{\n        if (text.length < 10) return \"\";\n        try {\n            var _text_split_pop;\n            // Simple prediction logic based on sentence patterns\n            const lastSentence = ((_text_split_pop = text.split(\".\").pop()) === null || _text_split_pop === void 0 ? void 0 : _text_split_pop.trim()) || text;\n            // If sentence seems incomplete, suggest completion\n            if (lastSentence.length > 0) {\n                // Sports context\n                if (lastSentence.toLowerCase().includes(\"sports\") || lastSentence.toLowerCase().includes(\"game\") || lastSentence.toLowerCase().includes(\"team\")) {\n                    const sportsSuggestions = [\n                        \" requires dedication and consistent practice\",\n                        \" teaches us valuable life lessons\",\n                        \" brings people together like nothing else\",\n                        \" is more than just competition\"\n                    ];\n                    return sportsSuggestions[Math.floor(Math.random() * sportsSuggestions.length)];\n                }\n                // Tech context\n                if (lastSentence.toLowerCase().includes(\"technology\") || lastSentence.toLowerCase().includes(\"coding\") || lastSentence.toLowerCase().includes(\"software\")) {\n                    const techSuggestions = [\n                        \" is evolving faster than ever before\",\n                        \" has the power to solve real problems\",\n                        \" requires continuous learning and adaptation\",\n                        \" should be accessible to everyone\"\n                    ];\n                    return techSuggestions[Math.floor(Math.random() * techSuggestions.length)];\n                }\n                // Business context\n                if (lastSentence.toLowerCase().includes(\"business\") || lastSentence.toLowerCase().includes(\"startup\") || lastSentence.toLowerCase().includes(\"entrepreneur\")) {\n                    const businessSuggestions = [\n                        \" is about solving problems for people\",\n                        \" requires patience and persistence\",\n                        \" success comes from understanding your customers\",\n                        \" failure is just feedback in disguise\"\n                    ];\n                    return businessSuggestions[Math.floor(Math.random() * businessSuggestions.length)];\n                }\n                // General sentence completion based on common patterns\n                if (lastSentence.endsWith(\"I think\") || lastSentence.endsWith(\"I believe\")) {\n                    return \" that consistency beats perfection every time\";\n                }\n                if (lastSentence.includes(\"The key to\")) {\n                    return \" success is taking action despite uncertainty\";\n                }\n                if (lastSentence.includes(\"What I learned\")) {\n                    return \" is that small steps lead to big changes\";\n                }\n                // Default contextual completions\n                const generalSuggestions = [\n                    \" and here's why that matters\",\n                    \" - let me explain\",\n                    \" in my experience\",\n                    \" based on what I've seen\",\n                    \" and the results speak for themselves\"\n                ];\n                return generalSuggestions[Math.floor(Math.random() * generalSuggestions.length)];\n            }\n            return \"\";\n        } catch (error) {\n            console.error(\"Error generating suggestion:\", error);\n            return \"\";\n        }\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    const autoFormat = ()=>{\n        if (content.length > 280) {\n            // Convert to thread format\n            const sentences = content.split(\". \");\n            const threads = [];\n            let currentThread = \"\";\n            sentences.forEach((sentence)=>{\n                if ((currentThread + sentence + \". \").length <= 280) {\n                    currentThread += sentence + \". \";\n                } else {\n                    if (currentThread) threads.push(currentThread.trim());\n                    currentThread = sentence + \". \";\n                }\n            });\n            if (currentThread) threads.push(currentThread.trim());\n            setContent(threads.join(\"\\n\\n\"));\n            setFormatMode(\"thread\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.paper,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"800px\",\n                    marginBottom: \"40px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"12px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            gap: \"16px\",\n                            marginTop: \"24px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"6px 12px\",\n                                    background: aiEnabled ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(aiEnabled ? colors.primary : colors.border),\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onClick: ()=>setAiEnabled(!aiEnabled),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sparkles, {\n                                        size: 14,\n                                        color: aiEnabled ? colors.primary : colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: aiEnabled ? colors.primary : colors.text.tertiary\n                                        },\n                                        children: \"AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: autoFormat,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: colors.surface,\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"500\",\n                                    color: colors.text.secondary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Type, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Auto Format\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: formatMode === \"thread\" ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(formatMode === \"thread\" ? colors.primary : colors.border)\n                                },\n                                children: [\n                                    formatMode === \"thread\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.AlignLeft, {\n                                        size: 14,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 40\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.AlignCenter, {\n                                        size: 14,\n                                        color: colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 89\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: formatMode === \"thread\" ? colors.primary : colors.text.tertiary\n                                        },\n                                        children: formatMode === \"thread\" ? \"Thread\" : \"Single\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAgentE(true),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    border: \"none\",\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"600\",\n                                    color: \"white\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    boxShadow: \"0 2px 8px \".concat(colors.primary, \"30\")\n                                },\n                                onMouseEnter: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(-1px)\";\n                                    target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"40\");\n                                },\n                                onMouseLeave: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(0)\";\n                                    target.style.boxShadow = \"0 2px 8px \".concat(colors.primary, \"30\");\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Agent E\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    background: \"transparent\",\n                    position: \"relative\",\n                    minHeight: \"500px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            position: \"relative\",\n                            padding: \"40px 60px\",\n                            minHeight: \"450px\"\n                        },\n                        children: [\n                            showSuggestion && aiEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"40px\",\n                                    left: \"60px\",\n                                    right: \"60px\",\n                                    bottom: \"40px\",\n                                    pointerEvents: \"none\",\n                                    zIndex: 1,\n                                    overflow: \"hidden\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sf-pro\",\n                                    style: {\n                                        fontSize: \"20px\",\n                                        lineHeight: \"1.7\",\n                                        color: \"transparent\",\n                                        whiteSpace: \"pre-wrap\",\n                                        wordWrap: \"break-word\",\n                                        letterSpacing: \"0.3px\",\n                                        fontWeight: \"400\"\n                                    },\n                                    children: [\n                                        content,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#9CA3AF\",\n                                                opacity: 0.6,\n                                                fontStyle: \"normal\"\n                                            },\n                                            children: aiSuggestion\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: content,\n                                onChange: (e)=>setContent(e.target.value),\n                                onKeyDown: handleTabPress,\n                                placeholder: aiEnabled ? \"Start writing and I'll help you continue...\" : \"What's on your mind?\",\n                                className: \"sf-pro\",\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    minHeight: \"400px\",\n                                    padding: \"0\",\n                                    border: \"none\",\n                                    background: \"transparent\",\n                                    fontSize: \"20px\",\n                                    lineHeight: \"1.7\",\n                                    color: colors.text.primary,\n                                    resize: \"none\",\n                                    outline: \"none\",\n                                    letterSpacing: \"0.3px\",\n                                    position: \"relative\",\n                                    zIndex: 2,\n                                    fontWeight: \"400\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            padding: \"0 60px 40px\",\n                            marginTop: \"20px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sf-pro\",\n                                style: {\n                                    fontSize: \"14px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"400\",\n                                    opacity: 0.7\n                                },\n                                children: [\n                                    content.length,\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setContent(\"\"),\n                                        className: \"sf-pro\",\n                                        style: {\n                                            padding: \"12px 24px\",\n                                            background: \"transparent\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            borderRadius: \"10px\",\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = colors.surfaceHover;\n                                            target.style.borderColor = colors.borderHover;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = \"transparent\";\n                                            target.style.borderColor = colors.border;\n                                        },\n                                        children: \"Save Draft\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // Publish logic here\n                                            console.log(\"Publishing:\", content);\n                                        },\n                                        className: \"sf-pro\",\n                                        style: {\n                                            padding: \"12px 28px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            border: \"none\",\n                                            borderRadius: \"10px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(-1px)\";\n                                            target.style.boxShadow = \"0 6px 16px \".concat(colors.primary, \"40\");\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(0)\";\n                                            target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"30\");\n                                        },\n                                        children: \"Publish\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentEChatSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAgentE,\n                onClose: ()=>setShowAgentE(false),\n                currentContent: content\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 453,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TweetCenterPage, \"S2hSYkr+qjKwHCWuNQ4A310UMuY=\");\n_c = TweetCenterPage;\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 464,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (TweetCenterPage);\nvar _c;\n$RefreshReg$(_c, \"TweetCenterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n"));

/***/ })

});