"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!lucide-react */ \"__barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n// pages/tweet-center.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst TweetCenterPage = ()=>{\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showAgentEInput, setShowAgentEInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [agentEPrompt, setAgentEPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        borderHover: \"#E8D5C4\",\n        surface: \"#FFFFFF\",\n        surfaceHover: \"#F9F7F4\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Real AI prediction based on content context\n    const generateContextualSuggestion = (text)=>{\n        if (text.length < 10) return \"\";\n        try {\n            var _text_split_pop;\n            // Simple prediction logic based on sentence patterns\n            const lastSentence = ((_text_split_pop = text.split(\".\").pop()) === null || _text_split_pop === void 0 ? void 0 : _text_split_pop.trim()) || text;\n            // If sentence seems incomplete, suggest completion\n            if (lastSentence.length > 0) {\n                // Sports context\n                if (lastSentence.toLowerCase().includes(\"sports\") || lastSentence.toLowerCase().includes(\"game\") || lastSentence.toLowerCase().includes(\"team\")) {\n                    const sportsSuggestions = [\n                        \" requires dedication and consistent practice\",\n                        \" teaches us valuable life lessons\",\n                        \" brings people together like nothing else\",\n                        \" is more than just competition\"\n                    ];\n                    return sportsSuggestions[Math.floor(Math.random() * sportsSuggestions.length)];\n                }\n                // Tech context\n                if (lastSentence.toLowerCase().includes(\"technology\") || lastSentence.toLowerCase().includes(\"coding\") || lastSentence.toLowerCase().includes(\"software\")) {\n                    const techSuggestions = [\n                        \" is evolving faster than ever before\",\n                        \" has the power to solve real problems\",\n                        \" requires continuous learning and adaptation\",\n                        \" should be accessible to everyone\"\n                    ];\n                    return techSuggestions[Math.floor(Math.random() * techSuggestions.length)];\n                }\n                // Business context\n                if (lastSentence.toLowerCase().includes(\"business\") || lastSentence.toLowerCase().includes(\"startup\") || lastSentence.toLowerCase().includes(\"entrepreneur\")) {\n                    const businessSuggestions = [\n                        \" is about solving problems for people\",\n                        \" requires patience and persistence\",\n                        \" success comes from understanding your customers\",\n                        \" failure is just feedback in disguise\"\n                    ];\n                    return businessSuggestions[Math.floor(Math.random() * businessSuggestions.length)];\n                }\n                // General sentence completion based on common patterns\n                if (lastSentence.endsWith(\"I think\") || lastSentence.endsWith(\"I believe\")) {\n                    return \" that consistency beats perfection every time\";\n                }\n                if (lastSentence.includes(\"The key to\")) {\n                    return \" success is taking action despite uncertainty\";\n                }\n                if (lastSentence.includes(\"What I learned\")) {\n                    return \" is that small steps lead to big changes\";\n                }\n                // Default contextual completions\n                const generalSuggestions = [\n                    \" and here's why that matters\",\n                    \" - let me explain\",\n                    \" in my experience\",\n                    \" based on what I've seen\",\n                    \" and the results speak for themselves\"\n                ];\n                return generalSuggestions[Math.floor(Math.random() * generalSuggestions.length)];\n            }\n            return \"\";\n        } catch (error) {\n            console.error(\"Error generating suggestion:\", error);\n            return \"\";\n        }\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    const autoFormat = ()=>{\n        if (content.length > 280) {\n            // Convert to thread format\n            const sentences = content.split(\". \");\n            const threads = [];\n            let currentThread = \"\";\n            sentences.forEach((sentence)=>{\n                if ((currentThread + sentence + \". \").length <= 280) {\n                    currentThread += sentence + \". \";\n                } else {\n                    if (currentThread) threads.push(currentThread.trim());\n                    currentThread = sentence + \". \";\n                }\n            });\n            if (currentThread) threads.push(currentThread.trim());\n            setContent(threads.join(\"\\n\\n\"));\n            setFormatMode(\"thread\");\n        }\n    };\n    const handleAgentESubmit = (e)=>{\n        e.preventDefault();\n        if (!agentEPrompt.trim()) return;\n        // Generate content based on the prompt\n        const generatedContent = generateContentFromPrompt(agentEPrompt);\n        setContent(generatedContent);\n        setAgentEPrompt(\"\");\n        setShowAgentEInput(false);\n    };\n    const generateContentFromPrompt = (prompt)=>{\n        const lowerPrompt = prompt.toLowerCase();\n        // Different content types based on prompt\n        if (lowerPrompt.includes(\"thread\") || lowerPrompt.includes(\"twitter thread\")) {\n            return \"Here's a thread about \".concat(prompt.replace(/thread|twitter thread/gi, \"\").trim(), \":\\n\\n1/ The key to understanding this topic is...\\n\\n2/ Most people think...\\n\\n3/ But here's what actually works...\");\n        }\n        if (lowerPrompt.includes(\"tips\") || lowerPrompt.includes(\"advice\")) {\n            return \"\".concat(prompt.charAt(0).toUpperCase() + prompt.slice(1), \":\\n\\n• Focus on the fundamentals first\\n• Consistency beats perfection\\n• Learn from others who've succeeded\\n• Take action despite uncertainty\");\n        }\n        if (lowerPrompt.includes(\"story\") || lowerPrompt.includes(\"experience\")) {\n            return \"Here's my experience with \".concat(prompt.replace(/story|experience/gi, \"\").trim(), \":\\n\\nIt started when I realized that most advice online was generic. I needed something that actually worked in the real world...\");\n        }\n        // Default content generation\n        return \"\".concat(prompt.charAt(0).toUpperCase() + prompt.slice(1), \".\\n\\nHere's what I've learned from years of experience: the biggest difference between success and failure isn't talent or luck—it's consistency.\\n\\nMost people give up right before they would have succeeded.\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.paper,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n        },\n        className: \"jsx-3f45dd3580662bfa\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"800px\",\n                    marginBottom: \"40px\",\n                    textAlign: \"center\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"12px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            gap: \"16px\",\n                            marginTop: \"24px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"6px 12px\",\n                                    background: aiEnabled ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(aiEnabled ? colors.primary : colors.border),\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onClick: ()=>setAiEnabled(!aiEnabled),\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sparkles, {\n                                        size: 14,\n                                        color: aiEnabled ? colors.primary : colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: aiEnabled ? colors.primary : colors.text.tertiary\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: \"AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: autoFormat,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: colors.surface,\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"500\",\n                                    color: colors.text.secondary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Type, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Auto Format\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: formatMode === \"thread\" ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(formatMode === \"thread\" ? colors.primary : colors.border)\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    formatMode === \"thread\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.AlignLeft, {\n                                        size: 14,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 40\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.AlignCenter, {\n                                        size: 14,\n                                        color: colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 89\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: formatMode === \"thread\" ? colors.primary : colors.text.tertiary\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: formatMode === \"thread\" ? \"Thread\" : \"Single\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAgentEInput(true),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    border: \"none\",\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"600\",\n                                    color: \"white\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    boxShadow: \"0 2px 8px \".concat(colors.primary, \"30\")\n                                },\n                                onMouseEnter: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(-1px)\";\n                                    target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"40\");\n                                },\n                                onMouseLeave: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(0)\";\n                                    target.style.boxShadow = \"0 2px 8px \".concat(colors.primary, \"30\");\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Agent E\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    background: \"transparent\",\n                    position: \"relative\",\n                    minHeight: \"500px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            position: \"relative\",\n                            padding: \"40px 60px\",\n                            minHeight: \"450px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            showSuggestion && aiEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"40px\",\n                                    left: \"60px\",\n                                    right: \"60px\",\n                                    bottom: \"40px\",\n                                    pointerEvents: \"none\",\n                                    zIndex: 1,\n                                    overflow: \"hidden\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"20px\",\n                                        lineHeight: \"1.7\",\n                                        color: \"transparent\",\n                                        whiteSpace: \"pre-wrap\",\n                                        wordWrap: \"break-word\",\n                                        letterSpacing: \"0.3px\",\n                                        fontWeight: \"400\"\n                                    },\n                                    className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                    children: [\n                                        content,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#9CA3AF\",\n                                                opacity: 0.6,\n                                                fontStyle: \"normal\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: aiSuggestion\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: content,\n                                onChange: (e)=>setContent(e.target.value),\n                                onKeyDown: handleTabPress,\n                                placeholder: aiEnabled ? \"Start writing...\" : \"What's on your mind?\",\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    minHeight: \"400px\",\n                                    padding: \"0\",\n                                    border: \"none\",\n                                    background: \"transparent\",\n                                    fontSize: \"20px\",\n                                    lineHeight: \"1.7\",\n                                    color: colors.text.primary,\n                                    resize: \"none\",\n                                    outline: \"none\",\n                                    letterSpacing: \"0.3px\",\n                                    position: \"relative\",\n                                    zIndex: 2,\n                                    fontWeight: \"400\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            padding: \"0 60px 40px\",\n                            marginTop: \"20px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"400\",\n                                    opacity: 0.7\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                children: [\n                                    content.length,\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setContent(\"\"),\n                                        style: {\n                                            padding: \"12px 24px\",\n                                            background: \"transparent\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            borderRadius: \"10px\",\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = colors.surfaceHover;\n                                            target.style.borderColor = colors.borderHover;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = \"transparent\";\n                                            target.style.borderColor = colors.border;\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Save Draft\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // Publish logic here\n                                            console.log(\"Publishing:\", content);\n                                        },\n                                        style: {\n                                            padding: \"12px 28px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            border: \"none\",\n                                            borderRadius: \"10px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(-1px)\";\n                                            target.style.boxShadow = \"0 6px 16px \".concat(colors.primary, \"40\");\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(0)\";\n                                            target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"30\");\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Publish\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, undefined),\n                    showAgentEInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"fixed\",\n                            bottom: \"24px\",\n                            left: \"50%\",\n                            transform: \"translateX(-50%)\",\n                            width: \"600px\",\n                            height: \"48px\",\n                            background: \"white\",\n                            borderRadius: \"24px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            padding: \"0 20px\",\n                            boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05)\",\n                            zIndex: 1000,\n                            border: \"2px solid \".concat(colors.primary)\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                size: 18,\n                                color: colors.primary,\n                                style: {\n                                    marginRight: \"12px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleAgentESubmit,\n                                style: {\n                                    flex: 1,\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: agentEPrompt,\n                                        onChange: (e)=>setAgentEPrompt(e.target.value),\n                                        placeholder: \"Ask Agent E to write something for you...\",\n                                        autoFocus: true,\n                                        style: {\n                                            flex: 1,\n                                            border: \"none\",\n                                            outline: \"none\",\n                                            fontSize: \"14px\",\n                                            color: colors.text.primary,\n                                            background: \"transparent\",\n                                            fontWeight: \"400\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        style: {\n                                            background: \"none\",\n                                            border: \"none\",\n                                            cursor: \"pointer\",\n                                            padding: \"4px\",\n                                            marginLeft: \"8px\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: \"↵\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAgentEInput(false),\n                                style: {\n                                    background: \"none\",\n                                    border: \"none\",\n                                    cursor: \"pointer\",\n                                    padding: \"4px\",\n                                    marginLeft: \"8px\",\n                                    color: colors.text.tertiary\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"3f45dd3580662bfa\",\n                children: \"@-webkit-keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);transform:scale(1.2)}}@-moz-keyframes pulse{0%,100%{opacity:.8;-moz-transform:scale(1);transform:scale(1)}50%{opacity:1;-moz-transform:scale(1.2);transform:scale(1.2)}}@-o-keyframes pulse{0%,100%{opacity:.8;-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-o-transform:scale(1.2);transform:scale(1.2)}}@keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-o-transform:scale(1.2);transform:scale(1.2)}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TweetCenterPage, \"fvjzMXO1RduMHNvnwkDLqfLeOaw=\");\n_c = TweetCenterPage;\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 562,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (TweetCenterPage);\nvar _c;\n$RefreshReg$(_c, \"TweetCenterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n"));

/***/ })

});