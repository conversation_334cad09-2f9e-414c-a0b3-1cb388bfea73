"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot!=!lucide-react */ \"__barrel_optimize__?names=Bot!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n// pages/tweet-center.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst TweetCenterPage = ()=>{\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showAgentEInput, setShowAgentEInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [agentEPrompt, setAgentEPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showOptionsDropdown, setShowOptionsDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [autoRunMode, setAutoRunMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        borderHover: \"#E8D5C4\",\n        surface: \"#FFFFFF\",\n        surfaceHover: \"#F9F7F4\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Real AI prediction based on content context\n    const generateContextualSuggestion = (text)=>{\n        if (text.length < 10) return \"\";\n        try {\n            var _text_split_pop;\n            // Simple prediction logic based on sentence patterns\n            const lastSentence = ((_text_split_pop = text.split(\".\").pop()) === null || _text_split_pop === void 0 ? void 0 : _text_split_pop.trim()) || text;\n            // If sentence seems incomplete, suggest completion\n            if (lastSentence.length > 0) {\n                // Sports context\n                if (lastSentence.toLowerCase().includes(\"sports\") || lastSentence.toLowerCase().includes(\"game\") || lastSentence.toLowerCase().includes(\"team\")) {\n                    const sportsSuggestions = [\n                        \" requires dedication and consistent practice\",\n                        \" teaches us valuable life lessons\",\n                        \" brings people together like nothing else\",\n                        \" is more than just competition\"\n                    ];\n                    return sportsSuggestions[Math.floor(Math.random() * sportsSuggestions.length)];\n                }\n                // Tech context\n                if (lastSentence.toLowerCase().includes(\"technology\") || lastSentence.toLowerCase().includes(\"coding\") || lastSentence.toLowerCase().includes(\"software\")) {\n                    const techSuggestions = [\n                        \" is evolving faster than ever before\",\n                        \" has the power to solve real problems\",\n                        \" requires continuous learning and adaptation\",\n                        \" should be accessible to everyone\"\n                    ];\n                    return techSuggestions[Math.floor(Math.random() * techSuggestions.length)];\n                }\n                // Business context\n                if (lastSentence.toLowerCase().includes(\"business\") || lastSentence.toLowerCase().includes(\"startup\") || lastSentence.toLowerCase().includes(\"entrepreneur\")) {\n                    const businessSuggestions = [\n                        \" is about solving problems for people\",\n                        \" requires patience and persistence\",\n                        \" success comes from understanding your customers\",\n                        \" failure is just feedback in disguise\"\n                    ];\n                    return businessSuggestions[Math.floor(Math.random() * businessSuggestions.length)];\n                }\n                // General sentence completion based on common patterns\n                if (lastSentence.endsWith(\"I think\") || lastSentence.endsWith(\"I believe\")) {\n                    return \" that consistency beats perfection every time\";\n                }\n                if (lastSentence.includes(\"The key to\")) {\n                    return \" success is taking action despite uncertainty\";\n                }\n                if (lastSentence.includes(\"What I learned\")) {\n                    return \" is that small steps lead to big changes\";\n                }\n                // Default contextual completions\n                const generalSuggestions = [\n                    \" and here's why that matters\",\n                    \" - let me explain\",\n                    \" in my experience\",\n                    \" based on what I've seen\",\n                    \" and the results speak for themselves\"\n                ];\n                return generalSuggestions[Math.floor(Math.random() * generalSuggestions.length)];\n            }\n            return \"\";\n        } catch (error) {\n            console.error(\"Error generating suggestion:\", error);\n            return \"\";\n        }\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    const handleAgentESubmit = (e)=>{\n        e.preventDefault();\n        if (!agentEPrompt.trim()) return;\n        // Generate content based on the prompt\n        const generatedContent = generateContentFromPrompt(agentEPrompt);\n        setContent(generatedContent);\n        setAgentEPrompt(\"\");\n        setShowAgentEInput(false);\n    };\n    const generateContentFromPrompt = (prompt)=>{\n        const lowerPrompt = prompt.toLowerCase();\n        // Different content types based on prompt\n        if (lowerPrompt.includes(\"thread\") || lowerPrompt.includes(\"twitter thread\")) {\n            return \"Here's a thread about \".concat(prompt.replace(/thread|twitter thread/gi, \"\").trim(), \":\\n\\n1/ The key to understanding this topic is...\\n\\n2/ Most people think...\\n\\n3/ But here's what actually works...\");\n        }\n        if (lowerPrompt.includes(\"tips\") || lowerPrompt.includes(\"advice\")) {\n            return \"\".concat(prompt.charAt(0).toUpperCase() + prompt.slice(1), \":\\n\\n• Focus on the fundamentals first\\n• Consistency beats perfection\\n• Learn from others who've succeeded\\n• Take action despite uncertainty\");\n        }\n        if (lowerPrompt.includes(\"story\") || lowerPrompt.includes(\"experience\")) {\n            return \"Here's my experience with \".concat(prompt.replace(/story|experience/gi, \"\").trim(), \":\\n\\nIt started when I realized that most advice online was generic. I needed something that actually worked in the real world...\");\n        }\n        // Default content generation\n        return \"\".concat(prompt.charAt(0).toUpperCase() + prompt.slice(1), \".\\n\\nHere's what I've learned from years of experience: the biggest difference between success and failure isn't talent or luck—it's consistency.\\n\\nMost people give up right before they would have succeeded.\");\n    };\n    // Auto-run mode - automatically generates tweets\n    const generateAutoTweet = ()=>{\n        const topics = [\n            \"productivity tips for entrepreneurs\",\n            \"lessons learned from building a startup\",\n            \"the importance of consistency in business\",\n            \"how to stay motivated during tough times\",\n            \"building habits that stick\",\n            \"the power of compound growth\",\n            \"why most people give up too early\",\n            \"simple strategies for better focus\"\n        ];\n        const randomTopic = topics[Math.floor(Math.random() * topics.length)];\n        const generatedContent = generateContentFromPrompt(randomTopic);\n        setContent(generatedContent);\n    };\n    // Auto-run effect\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (autoRunMode && !content) {\n            const timer = setTimeout(()=>{\n                generateAutoTweet();\n            }, 1000);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        autoRunMode,\n        content\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.paper,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n        },\n        className: \"jsx-3f45dd3580662bfa\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    marginBottom: \"40px\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.4px\",\n                            fontFamily: \"SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowOptionsDropdown(!showOptionsDropdown),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"8px 16px\",\n                                    background: colors.surface,\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"12px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    color: colors.text.primary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    \"Options\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            transform: showOptionsDropdown ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                            transition: \"transform 0.2s ease\",\n                                            fontSize: \"12px\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: \"▼\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, undefined),\n                            showOptionsDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"100%\",\n                                    right: 0,\n                                    marginTop: \"8px\",\n                                    background: \"white\",\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"12px\",\n                                    boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.12)\",\n                                    padding: \"12px\",\n                                    minWidth: \"220px\",\n                                    zIndex: 1000\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            padding: \"8px 0\",\n                                            borderBottom: \"1px solid \".concat(colors.border)\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.primary\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: \"AI Predictions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setAiEnabled(!aiEnabled),\n                                                style: {\n                                                    width: \"40px\",\n                                                    height: \"20px\",\n                                                    borderRadius: \"10px\",\n                                                    border: \"none\",\n                                                    background: aiEnabled ? colors.primary : colors.border,\n                                                    position: \"relative\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"white\",\n                                                        position: \"absolute\",\n                                                        top: \"2px\",\n                                                        left: aiEnabled ? \"22px\" : \"2px\",\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    className: \"jsx-3f45dd3580662bfa\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            padding: \"8px 0\",\n                                            borderBottom: \"1px solid \".concat(colors.border)\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            color: colors.text.primary,\n                                                            fontWeight: \"500\"\n                                                        },\n                                                        className: \"jsx-3f45dd3580662bfa\",\n                                                        children: \"Auto-Run Mode ✨\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"12px\",\n                                                            color: colors.text.tertiary,\n                                                            marginTop: \"2px\"\n                                                        },\n                                                        className: \"jsx-3f45dd3580662bfa\",\n                                                        children: \"Automatically generates tweets\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setAutoRunMode(!autoRunMode);\n                                                    if (!autoRunMode) {\n                                                        setContent(\"\"); // Clear content to trigger auto-generation\n                                                    }\n                                                },\n                                                style: {\n                                                    width: \"40px\",\n                                                    height: \"20px\",\n                                                    borderRadius: \"10px\",\n                                                    border: \"none\",\n                                                    background: autoRunMode ? \"#10B981\" : colors.border,\n                                                    position: \"relative\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\",\n                                                    boxShadow: autoRunMode ? \"0 0 12px rgba(16, 185, 129, 0.4)\" : \"none\"\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"white\",\n                                                        position: \"absolute\",\n                                                        top: \"2px\",\n                                                        left: autoRunMode ? \"22px\" : \"2px\",\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    className: \"jsx-3f45dd3580662bfa\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            padding: \"8px 0\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.primary\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: \"Thread Mode\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setFormatMode(formatMode === \"thread\" ? \"single\" : \"thread\"),\n                                                style: {\n                                                    width: \"40px\",\n                                                    height: \"20px\",\n                                                    borderRadius: \"10px\",\n                                                    border: \"none\",\n                                                    background: formatMode === \"thread\" ? colors.primary : colors.border,\n                                                    position: \"relative\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"white\",\n                                                        position: \"absolute\",\n                                                        top: \"2px\",\n                                                        left: formatMode === \"thread\" ? \"22px\" : \"2px\",\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    className: \"jsx-3f45dd3580662bfa\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    background: \"transparent\",\n                    position: \"relative\",\n                    minHeight: \"500px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            position: \"relative\",\n                            padding: \"40px 60px\",\n                            minHeight: \"450px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            showSuggestion && aiEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"40px\",\n                                    left: \"60px\",\n                                    right: \"60px\",\n                                    bottom: \"40px\",\n                                    pointerEvents: \"none\",\n                                    zIndex: 1,\n                                    overflow: \"hidden\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"20px\",\n                                        lineHeight: \"1.7\",\n                                        color: \"transparent\",\n                                        whiteSpace: \"pre-wrap\",\n                                        wordWrap: \"break-word\",\n                                        letterSpacing: \"0.3px\",\n                                        fontWeight: \"400\"\n                                    },\n                                    className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                    children: [\n                                        content,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#9CA3AF\",\n                                                opacity: 0.6,\n                                                fontStyle: \"normal\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: aiSuggestion\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: content,\n                                onChange: (e)=>setContent(e.target.value),\n                                onKeyDown: handleTabPress,\n                                placeholder: aiEnabled ? \"Start writing...\" : \"What's on your mind?\",\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    minHeight: \"400px\",\n                                    padding: \"0\",\n                                    border: \"none\",\n                                    background: \"transparent\",\n                                    fontSize: \"20px\",\n                                    lineHeight: \"1.7\",\n                                    color: colors.text.primary,\n                                    resize: \"none\",\n                                    outline: \"none\",\n                                    letterSpacing: \"0.3px\",\n                                    position: \"relative\",\n                                    zIndex: 2,\n                                    fontWeight: \"400\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            padding: \"0 60px 40px\",\n                            marginTop: \"20px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"400\",\n                                    opacity: 0.7\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                children: [\n                                    content.length,\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setContent(\"\"),\n                                        style: {\n                                            padding: \"12px 24px\",\n                                            background: \"transparent\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            borderRadius: \"10px\",\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = colors.surfaceHover;\n                                            target.style.borderColor = colors.borderHover;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = \"transparent\";\n                                            target.style.borderColor = colors.border;\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Save Draft\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // Publish logic here\n                                            console.log(\"Publishing:\", content);\n                                        },\n                                        style: {\n                                            padding: \"12px 28px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            border: \"none\",\n                                            borderRadius: \"10px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(-1px)\";\n                                            target.style.boxShadow = \"0 6px 16px \".concat(colors.primary, \"40\");\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(0)\";\n                                            target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"30\");\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Publish\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 9\n                    }, undefined),\n                    showAgentEInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"fixed\",\n                            bottom: \"24px\",\n                            left: \"50%\",\n                            transform: \"translateX(-50%)\",\n                            width: \"600px\",\n                            height: \"48px\",\n                            background: \"white\",\n                            borderRadius: \"24px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            padding: \"0 20px\",\n                            boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05)\",\n                            zIndex: 1000,\n                            border: \"2px solid \".concat(colors.primary)\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                size: 18,\n                                color: colors.primary,\n                                style: {\n                                    marginRight: \"12px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleAgentESubmit,\n                                style: {\n                                    flex: 1,\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: agentEPrompt,\n                                        onChange: (e)=>setAgentEPrompt(e.target.value),\n                                        placeholder: \"Ask Agent E to write something for you...\",\n                                        autoFocus: true,\n                                        style: {\n                                            flex: 1,\n                                            border: \"none\",\n                                            outline: \"none\",\n                                            fontSize: \"14px\",\n                                            color: colors.text.primary,\n                                            background: \"transparent\",\n                                            fontWeight: \"400\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        style: {\n                                            background: \"none\",\n                                            border: \"none\",\n                                            cursor: \"pointer\",\n                                            padding: \"4px\",\n                                            marginLeft: \"8px\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: \"↵\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAgentEInput(false),\n                                style: {\n                                    background: \"none\",\n                                    border: \"none\",\n                                    cursor: \"pointer\",\n                                    padding: \"4px\",\n                                    marginLeft: \"8px\",\n                                    color: colors.text.tertiary\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 557,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"3f45dd3580662bfa\",\n                children: \"@-webkit-keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);transform:scale(1.2)}}@-moz-keyframes pulse{0%,100%{opacity:.8;-moz-transform:scale(1);transform:scale(1)}50%{opacity:1;-moz-transform:scale(1.2);transform:scale(1.2)}}@-o-keyframes pulse{0%,100%{opacity:.8;-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-o-transform:scale(1.2);transform:scale(1.2)}}@keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-o-transform:scale(1.2);transform:scale(1.2)}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TweetCenterPage, \"7QuKRY1laqlm1+LZN0E84hfCivE=\");\n_c = TweetCenterPage;\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 635,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (TweetCenterPage);\nvar _c;\n$RefreshReg$(_c, \"TweetCenterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n"));

/***/ })

});