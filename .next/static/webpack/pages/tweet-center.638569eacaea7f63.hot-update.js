"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!lucide-react */ \"__barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_AgentEChatSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/AgentEChatSimple */ \"./components/AgentEChatSimple.tsx\");\n// pages/tweet-center.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst TweetCenterPage = ()=>{\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAgentE, setShowAgentE] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"single\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        borderHover: \"#E8D5C4\",\n        surface: \"#FFFFFF\",\n        surfaceHover: \"#F9F7F4\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Real AI prediction based on content context\n    const generateContextualSuggestion = async (text)=>{\n        if (text.length < 10) return \"\";\n        try {\n            var _text_split_pop;\n            // Get the last few words to predict the next part\n            const words = text.trim().split(\" \");\n            const lastWords = words.slice(-5).join(\" \"); // Last 5 words for context\n            // Simple prediction logic based on sentence patterns\n            const lastSentence = ((_text_split_pop = text.split(\".\").pop()) === null || _text_split_pop === void 0 ? void 0 : _text_split_pop.trim()) || text;\n            // If sentence seems incomplete, suggest completion\n            if (lastSentence.length > 0) {\n                // Sports context\n                if (lastSentence.toLowerCase().includes(\"sports\") || lastSentence.toLowerCase().includes(\"game\") || lastSentence.toLowerCase().includes(\"team\")) {\n                    const sportsSuggestions = [\n                        \" requires dedication and consistent practice\",\n                        \" teaches us valuable life lessons\",\n                        \" brings people together like nothing else\",\n                        \" is more than just competition\"\n                    ];\n                    return sportsSuggestions[Math.floor(Math.random() * sportsSuggestions.length)];\n                }\n                // Tech context\n                if (lastSentence.toLowerCase().includes(\"technology\") || lastSentence.toLowerCase().includes(\"coding\") || lastSentence.toLowerCase().includes(\"software\")) {\n                    const techSuggestions = [\n                        \" is evolving faster than ever before\",\n                        \" has the power to solve real problems\",\n                        \" requires continuous learning and adaptation\",\n                        \" should be accessible to everyone\"\n                    ];\n                    return techSuggestions[Math.floor(Math.random() * techSuggestions.length)];\n                }\n                // Business context\n                if (lastSentence.toLowerCase().includes(\"business\") || lastSentence.toLowerCase().includes(\"startup\") || lastSentence.toLowerCase().includes(\"entrepreneur\")) {\n                    const businessSuggestions = [\n                        \" is about solving problems for people\",\n                        \" requires patience and persistence\",\n                        \" success comes from understanding your customers\",\n                        \" failure is just feedback in disguise\"\n                    ];\n                    return businessSuggestions[Math.floor(Math.random() * businessSuggestions.length)];\n                }\n                // General sentence completion based on common patterns\n                if (lastSentence.endsWith(\"I think\") || lastSentence.endsWith(\"I believe\")) {\n                    return \" that consistency beats perfection every time\";\n                }\n                if (lastSentence.includes(\"The key to\")) {\n                    return \" success is taking action despite uncertainty\";\n                }\n                if (lastSentence.includes(\"What I learned\")) {\n                    return \" is that small steps lead to big changes\";\n                }\n                // Default contextual completions\n                const generalSuggestions = [\n                    \" and here's why that matters\",\n                    \" - let me explain\",\n                    \" in my experience\",\n                    \" based on what I've seen\",\n                    \" and the results speak for themselves\"\n                ];\n                return generalSuggestions[Math.floor(Math.random() * generalSuggestions.length)];\n            }\n            return \"\";\n        } catch (error) {\n            console.error(\"Error generating suggestion:\", error);\n            return \"\";\n        }\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    const autoFormat = ()=>{\n        if (content.length > 280) {\n            // Convert to thread format\n            const sentences = content.split(\". \");\n            const threads = [];\n            let currentThread = \"\";\n            sentences.forEach((sentence)=>{\n                if ((currentThread + sentence + \". \").length <= 280) {\n                    currentThread += sentence + \". \";\n                } else {\n                    if (currentThread) threads.push(currentThread.trim());\n                    currentThread = sentence + \". \";\n                }\n            });\n            if (currentThread) threads.push(currentThread.trim());\n            setContent(threads.join(\"\\n\\n\"));\n            setFormatMode(\"thread\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.paper,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"800px\",\n                    marginBottom: \"40px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"12px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            gap: \"16px\",\n                            marginTop: \"24px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"6px 12px\",\n                                    background: aiEnabled ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(aiEnabled ? colors.primary : colors.border),\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onClick: ()=>setAiEnabled(!aiEnabled),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sparkles, {\n                                        size: 14,\n                                        color: aiEnabled ? colors.primary : colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: aiEnabled ? colors.primary : colors.text.tertiary\n                                        },\n                                        children: \"AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: autoFormat,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: colors.surface,\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"500\",\n                                    color: colors.text.secondary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Type, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Auto Format\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: formatMode === \"thread\" ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(formatMode === \"thread\" ? colors.primary : colors.border)\n                                },\n                                children: [\n                                    formatMode === \"thread\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.AlignLeft, {\n                                        size: 14,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 40\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.AlignCenter, {\n                                        size: 14,\n                                        color: colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 89\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: formatMode === \"thread\" ? colors.primary : colors.text.tertiary\n                                        },\n                                        children: formatMode === \"thread\" ? \"Thread\" : \"Single\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAgentE(true),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    border: \"none\",\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"600\",\n                                    color: \"white\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    boxShadow: \"0 2px 8px \".concat(colors.primary, \"30\")\n                                },\n                                onMouseEnter: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(-1px)\";\n                                    target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"40\");\n                                },\n                                onMouseLeave: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(0)\";\n                                    target.style.boxShadow = \"0 2px 8px \".concat(colors.primary, \"30\");\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Agent E\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    background: \"transparent\",\n                    position: \"relative\",\n                    minHeight: \"500px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            position: \"relative\",\n                            padding: \"40px 60px\",\n                            minHeight: \"450px\"\n                        },\n                        children: [\n                            showSuggestion && aiEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"40px\",\n                                    left: \"60px\",\n                                    right: \"60px\",\n                                    bottom: \"40px\",\n                                    pointerEvents: \"none\",\n                                    zIndex: 1,\n                                    overflow: \"hidden\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sf-pro\",\n                                    style: {\n                                        fontSize: \"20px\",\n                                        lineHeight: \"1.7\",\n                                        color: \"transparent\",\n                                        whiteSpace: \"pre-wrap\",\n                                        wordWrap: \"break-word\",\n                                        letterSpacing: \"0.3px\",\n                                        fontWeight: \"400\"\n                                    },\n                                    children: [\n                                        content,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#9CA3AF\",\n                                                opacity: 0.6,\n                                                fontStyle: \"normal\"\n                                            },\n                                            children: aiSuggestion\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: content,\n                                onChange: (e)=>setContent(e.target.value),\n                                onKeyDown: handleTabPress,\n                                placeholder: aiEnabled ? \"Start writing and I'll help you continue...\" : \"What's on your mind?\",\n                                className: \"sf-pro\",\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    minHeight: \"400px\",\n                                    padding: \"0\",\n                                    border: \"none\",\n                                    background: \"transparent\",\n                                    fontSize: \"20px\",\n                                    lineHeight: \"1.7\",\n                                    color: colors.text.primary,\n                                    resize: \"none\",\n                                    outline: \"none\",\n                                    letterSpacing: \"0.3px\",\n                                    position: \"relative\",\n                                    zIndex: 2,\n                                    fontWeight: \"400\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            padding: \"0 60px 40px\",\n                            marginTop: \"20px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sf-pro\",\n                                style: {\n                                    fontSize: \"14px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"400\",\n                                    opacity: 0.7\n                                },\n                                children: [\n                                    content.length,\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setContent(\"\"),\n                                        className: \"sf-pro\",\n                                        style: {\n                                            padding: \"12px 24px\",\n                                            background: \"transparent\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            borderRadius: \"10px\",\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = colors.surfaceHover;\n                                            target.style.borderColor = colors.borderHover;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = \"transparent\";\n                                            target.style.borderColor = colors.border;\n                                        },\n                                        children: \"Save Draft\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // Publish logic here\n                                            console.log(\"Publishing:\", content);\n                                        },\n                                        className: \"sf-pro\",\n                                        style: {\n                                            padding: \"12px 28px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            border: \"none\",\n                                            borderRadius: \"10px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(-1px)\";\n                                            target.style.boxShadow = \"0 6px 16px \".concat(colors.primary, \"40\");\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(0)\";\n                                            target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"30\");\n                                        },\n                                        children: \"Publish\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentEChatSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAgentE,\n                onClose: ()=>setShowAgentE(false),\n                currentContent: content\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 457,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TweetCenterPage, \"S2hSYkr+qjKwHCWuNQ4A310UMuY=\");\n_c = TweetCenterPage;\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 468,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (TweetCenterPage);\nvar _c;\n$RefreshReg$(_c, \"TweetCenterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n"));

/***/ })

});