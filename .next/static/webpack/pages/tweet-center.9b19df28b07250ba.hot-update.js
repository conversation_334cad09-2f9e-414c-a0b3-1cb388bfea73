"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!lucide-react */ \"__barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n// pages/tweet-center.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst TweetCenterPage = ()=>{\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showAgentE, setShowAgentE] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showAgentEInput, setShowAgentEInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [agentEPrompt, setAgentEPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        borderHover: \"#E8D5C4\",\n        surface: \"#FFFFFF\",\n        surfaceHover: \"#F9F7F4\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Real AI prediction based on content context\n    const generateContextualSuggestion = (text)=>{\n        if (text.length < 10) return \"\";\n        try {\n            var _text_split_pop;\n            // Simple prediction logic based on sentence patterns\n            const lastSentence = ((_text_split_pop = text.split(\".\").pop()) === null || _text_split_pop === void 0 ? void 0 : _text_split_pop.trim()) || text;\n            // If sentence seems incomplete, suggest completion\n            if (lastSentence.length > 0) {\n                // Sports context\n                if (lastSentence.toLowerCase().includes(\"sports\") || lastSentence.toLowerCase().includes(\"game\") || lastSentence.toLowerCase().includes(\"team\")) {\n                    const sportsSuggestions = [\n                        \" requires dedication and consistent practice\",\n                        \" teaches us valuable life lessons\",\n                        \" brings people together like nothing else\",\n                        \" is more than just competition\"\n                    ];\n                    return sportsSuggestions[Math.floor(Math.random() * sportsSuggestions.length)];\n                }\n                // Tech context\n                if (lastSentence.toLowerCase().includes(\"technology\") || lastSentence.toLowerCase().includes(\"coding\") || lastSentence.toLowerCase().includes(\"software\")) {\n                    const techSuggestions = [\n                        \" is evolving faster than ever before\",\n                        \" has the power to solve real problems\",\n                        \" requires continuous learning and adaptation\",\n                        \" should be accessible to everyone\"\n                    ];\n                    return techSuggestions[Math.floor(Math.random() * techSuggestions.length)];\n                }\n                // Business context\n                if (lastSentence.toLowerCase().includes(\"business\") || lastSentence.toLowerCase().includes(\"startup\") || lastSentence.toLowerCase().includes(\"entrepreneur\")) {\n                    const businessSuggestions = [\n                        \" is about solving problems for people\",\n                        \" requires patience and persistence\",\n                        \" success comes from understanding your customers\",\n                        \" failure is just feedback in disguise\"\n                    ];\n                    return businessSuggestions[Math.floor(Math.random() * businessSuggestions.length)];\n                }\n                // General sentence completion based on common patterns\n                if (lastSentence.endsWith(\"I think\") || lastSentence.endsWith(\"I believe\")) {\n                    return \" that consistency beats perfection every time\";\n                }\n                if (lastSentence.includes(\"The key to\")) {\n                    return \" success is taking action despite uncertainty\";\n                }\n                if (lastSentence.includes(\"What I learned\")) {\n                    return \" is that small steps lead to big changes\";\n                }\n                // Default contextual completions\n                const generalSuggestions = [\n                    \" and here's why that matters\",\n                    \" - let me explain\",\n                    \" in my experience\",\n                    \" based on what I've seen\",\n                    \" and the results speak for themselves\"\n                ];\n                return generalSuggestions[Math.floor(Math.random() * generalSuggestions.length)];\n            }\n            return \"\";\n        } catch (error) {\n            console.error(\"Error generating suggestion:\", error);\n            return \"\";\n        }\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    const autoFormat = ()=>{\n        if (content.length > 280) {\n            // Convert to thread format\n            const sentences = content.split(\". \");\n            const threads = [];\n            let currentThread = \"\";\n            sentences.forEach((sentence)=>{\n                if ((currentThread + sentence + \". \").length <= 280) {\n                    currentThread += sentence + \". \";\n                } else {\n                    if (currentThread) threads.push(currentThread.trim());\n                    currentThread = sentence + \". \";\n                }\n            });\n            if (currentThread) threads.push(currentThread.trim());\n            setContent(threads.join(\"\\n\\n\"));\n            setFormatMode(\"thread\");\n        }\n    };\n    const handleAgentESubmit = (e)=>{\n        e.preventDefault();\n        if (!agentEPrompt.trim()) return;\n        // Generate content based on the prompt\n        const generatedContent = generateContentFromPrompt(agentEPrompt);\n        setContent(generatedContent);\n        setAgentEPrompt(\"\");\n        setShowAgentEInput(false);\n    };\n    const generateContentFromPrompt = (prompt)=>{\n        const lowerPrompt = prompt.toLowerCase();\n        // Different content types based on prompt\n        if (lowerPrompt.includes(\"thread\") || lowerPrompt.includes(\"twitter thread\")) {\n            return \"Here's a thread about \".concat(prompt.replace(/thread|twitter thread/gi, \"\").trim(), \":\\n\\n1/ The key to understanding this topic is...\\n\\n2/ Most people think...\\n\\n3/ But here's what actually works...\");\n        }\n        if (lowerPrompt.includes(\"tips\") || lowerPrompt.includes(\"advice\")) {\n            return \"\".concat(prompt.charAt(0).toUpperCase() + prompt.slice(1), \":\\n\\n• Focus on the fundamentals first\\n• Consistency beats perfection\\n• Learn from others who've succeeded\\n• Take action despite uncertainty\");\n        }\n        if (lowerPrompt.includes(\"story\") || lowerPrompt.includes(\"experience\")) {\n            return \"Here's my experience with \".concat(prompt.replace(/story|experience/gi, \"\").trim(), \":\\n\\nIt started when I realized that most advice online was generic. I needed something that actually worked in the real world...\");\n        }\n        // Default content generation\n        return \"\".concat(prompt.charAt(0).toUpperCase() + prompt.slice(1), \".\\n\\nHere's what I've learned from years of experience: the biggest difference between success and failure isn't talent or luck—it's consistency.\\n\\nMost people give up right before they would have succeeded.\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.paper,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n        },\n        className: \"jsx-3f45dd3580662bfa\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"800px\",\n                    marginBottom: \"40px\",\n                    textAlign: \"center\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"12px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            gap: \"16px\",\n                            marginTop: \"24px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"6px 12px\",\n                                    background: aiEnabled ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(aiEnabled ? colors.primary : colors.border),\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onClick: ()=>setAiEnabled(!aiEnabled),\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sparkles, {\n                                        size: 14,\n                                        color: aiEnabled ? colors.primary : colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: aiEnabled ? colors.primary : colors.text.tertiary\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: \"AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: autoFormat,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: colors.surface,\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"500\",\n                                    color: colors.text.secondary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Type, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Auto Format\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: formatMode === \"thread\" ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(formatMode === \"thread\" ? colors.primary : colors.border)\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    formatMode === \"thread\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.AlignLeft, {\n                                        size: 14,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 40\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.AlignCenter, {\n                                        size: 14,\n                                        color: colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 89\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: formatMode === \"thread\" ? colors.primary : colors.text.tertiary\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: formatMode === \"thread\" ? \"Thread\" : \"Single\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAgentEInput(true),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    border: \"none\",\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"600\",\n                                    color: \"white\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    boxShadow: \"0 2px 8px \".concat(colors.primary, \"30\")\n                                },\n                                onMouseEnter: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(-1px)\";\n                                    target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"40\");\n                                },\n                                onMouseLeave: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(0)\";\n                                    target.style.boxShadow = \"0 2px 8px \".concat(colors.primary, \"30\");\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Agent E\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    background: \"transparent\",\n                    position: \"relative\",\n                    minHeight: \"500px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            position: \"relative\",\n                            padding: \"40px 60px\",\n                            minHeight: \"450px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            showSuggestion && aiEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"40px\",\n                                    left: \"60px\",\n                                    right: \"60px\",\n                                    bottom: \"40px\",\n                                    pointerEvents: \"none\",\n                                    zIndex: 1,\n                                    overflow: \"hidden\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"20px\",\n                                        lineHeight: \"1.7\",\n                                        color: \"transparent\",\n                                        whiteSpace: \"pre-wrap\",\n                                        wordWrap: \"break-word\",\n                                        letterSpacing: \"0.3px\",\n                                        fontWeight: \"400\"\n                                    },\n                                    className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                    children: [\n                                        content,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#9CA3AF\",\n                                                opacity: 0.6,\n                                                fontStyle: \"normal\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: aiSuggestion\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: content,\n                                onChange: (e)=>setContent(e.target.value),\n                                onKeyDown: handleTabPress,\n                                placeholder: aiEnabled ? \"Start writing...\" : \"What's on your mind?\",\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    minHeight: \"400px\",\n                                    padding: \"0\",\n                                    border: \"none\",\n                                    background: \"transparent\",\n                                    fontSize: \"20px\",\n                                    lineHeight: \"1.7\",\n                                    color: colors.text.primary,\n                                    resize: \"none\",\n                                    outline: \"none\",\n                                    letterSpacing: \"0.3px\",\n                                    position: \"relative\",\n                                    zIndex: 2,\n                                    fontWeight: \"400\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            padding: \"0 60px 40px\",\n                            marginTop: \"20px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"400\",\n                                    opacity: 0.7\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                children: [\n                                    content.length,\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setContent(\"\"),\n                                        style: {\n                                            padding: \"12px 24px\",\n                                            background: \"transparent\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            borderRadius: \"10px\",\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = colors.surfaceHover;\n                                            target.style.borderColor = colors.borderHover;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = \"transparent\";\n                                            target.style.borderColor = colors.border;\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Save Draft\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // Publish logic here\n                                            console.log(\"Publishing:\", content);\n                                        },\n                                        style: {\n                                            padding: \"12px 28px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            border: \"none\",\n                                            borderRadius: \"10px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(-1px)\";\n                                            target.style.boxShadow = \"0 6px 16px \".concat(colors.primary, \"40\");\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(0)\";\n                                            target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"30\");\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Publish\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 9\n                    }, undefined),\n                    showAgentEInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"fixed\",\n                            bottom: \"24px\",\n                            left: \"50%\",\n                            transform: \"translateX(-50%)\",\n                            width: \"600px\",\n                            height: \"48px\",\n                            background: \"white\",\n                            borderRadius: \"24px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            padding: \"0 20px\",\n                            boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05)\",\n                            zIndex: 1000,\n                            border: \"2px solid \".concat(colors.primary)\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                size: 18,\n                                color: colors.primary,\n                                style: {\n                                    marginRight: \"12px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleAgentESubmit,\n                                style: {\n                                    flex: 1,\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: agentEPrompt,\n                                        onChange: (e)=>setAgentEPrompt(e.target.value),\n                                        placeholder: \"Ask Agent E to write something for you...\",\n                                        autoFocus: true,\n                                        style: {\n                                            flex: 1,\n                                            border: \"none\",\n                                            outline: \"none\",\n                                            fontSize: \"14px\",\n                                            color: colors.text.primary,\n                                            background: \"transparent\",\n                                            fontWeight: \"400\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        style: {\n                                            background: \"none\",\n                                            border: \"none\",\n                                            cursor: \"pointer\",\n                                            padding: \"4px\",\n                                            marginLeft: \"8px\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: \"↵\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAgentEInput(false),\n                                style: {\n                                    background: \"none\",\n                                    border: \"none\",\n                                    cursor: \"pointer\",\n                                    padding: \"4px\",\n                                    marginLeft: \"8px\",\n                                    color: colors.text.tertiary\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"3f45dd3580662bfa\",\n                children: \"@-webkit-keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);transform:scale(1.2)}}@-moz-keyframes pulse{0%,100%{opacity:.8;-moz-transform:scale(1);transform:scale(1)}50%{opacity:1;-moz-transform:scale(1.2);transform:scale(1.2)}}@-o-keyframes pulse{0%,100%{opacity:.8;-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-o-transform:scale(1.2);transform:scale(1.2)}}@keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-o-transform:scale(1.2);transform:scale(1.2)}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TweetCenterPage, \"wJrcYQKhK6mHhasX9nKwi3Fz+xY=\");\n_c = TweetCenterPage;\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 563,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (TweetCenterPage);\nvar _c;\n$RefreshReg$(_c, \"TweetCenterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n"));

/***/ })

});