"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/settings",{

/***/ "./pages/settings.tsx":
/*!****************************!*\
  !*** ./pages/settings.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!lucide-react */ \"__barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/supabase */ \"./lib/supabase.ts\");\n// pages/settings.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SettingsPage = ()=>{\n    _s();\n    const { user, profile, updateProfile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"agent-e\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Agent E Settings\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"My AI Project\");\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [targetAudience, setTargetAudience] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [brandVoice, setBrandVoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"professional\");\n    const [customPrompts, setCustomPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: \"Product Launch\",\n            prompt: \"Create engaging content for product launches with excitement and clear benefits\"\n        },\n        {\n            id: 2,\n            name: \"Educational\",\n            prompt: \"Write informative content that teaches and provides value to the audience\"\n        }\n    ]);\n    // Profile Settings\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [avatarUrl, setAvatarUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // X Integration Settings\n    const [xAccountConnected, setXAccountConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [xAccountInfo, setXAccountInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connectingX, setConnectingX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPinModal, setShowPinModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [oauthToken, setOauthToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [oauthTokenSecret, setOauthTokenSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [pin, setPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [verifyingPin, setVerifyingPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check for pending X connection on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const pendingOAuthToken = localStorage.getItem(\"pending_x_oauth_token\");\n        const pendingOAuthSecret = localStorage.getItem(\"pending_x_oauth_secret\");\n        if (pendingOAuthToken && pendingOAuthSecret) {\n            setOauthToken(pendingOAuthToken);\n            setOauthTokenSecret(pendingOAuthSecret);\n            setShowPinModal(true);\n        }\n    }, []);\n    // Load settings from Supabase\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            var _user_user_metadata;\n            loadSettings();\n            loadXAccountStatus();\n            setFullName((profile === null || profile === void 0 ? void 0 : profile.full_name) || ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"\");\n            setEmail(user.email || \"\");\n            setAvatarUrl((profile === null || profile === void 0 ? void 0 : profile.avatar_url) || \"\");\n        }\n    }, [\n        user,\n        profile\n    ]);\n    const loadSettings = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"agent_e_settings\").select(\"*\").eq(\"user_id\", user.id).single();\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error loading settings:\", error);\n                return;\n            }\n            if (data) {\n                setProjectName(data.project_name || \"My AI Project\");\n                setProjectDescription(data.project_description || \"\");\n                setTargetAudience(data.target_audience || \"\");\n                setBrandVoice(data.brand_voice || \"professional\");\n                setCustomPrompts(data.custom_prompts || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading settings:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveAgentESettings = async ()=>{\n        if (!user) return;\n        setSaving(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"agent_e_settings\").upsert({\n                user_id: user.id,\n                project_name: projectName,\n                project_description: projectDescription,\n                target_audience: targetAudience,\n                brand_voice: brandVoice,\n                custom_prompts: customPrompts\n            });\n            if (error) {\n                console.error(\"Error saving settings:\", error);\n                alert(\"Error saving settings. Please try again.\");\n            } else {\n                alert(\"Settings saved successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            alert(\"Error saving settings. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const saveProfileSettings = async ()=>{\n        if (!user) {\n            alert(\"You must be logged in to update your profile.\");\n            return;\n        }\n        setSaving(true);\n        try {\n            // Try to update the profile\n            const { error } = await updateProfile({\n                full_name: fullName,\n                avatar_url: avatarUrl || null\n            });\n            if (error) {\n                var _error_message;\n                console.error(\"Error updating profile:\", error);\n                // If the table doesn't exist, try to create it first\n                if (error.code === \"42P01\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('relation \"user_profiles\" does not exist'))) {\n                    alert(\"Database setup required. Please run the database schema first, then try again.\");\n                } else {\n                    alert(\"Error updating profile: \".concat(error.message || \"Please try again.\"));\n                }\n            } else {\n                alert(\"Profile updated successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            alert(\"Error updating profile. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const loadXAccountStatus = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/account-status?userId=\".concat(user.id));\n            const data = await response.json();\n            console.log(\"X Account Status Response:\", {\n                status: response.status,\n                connected: data.connected,\n                accountInfo: data.accountInfo,\n                error: data.error\n            });\n            if (response.ok && data.connected) {\n                setXAccountConnected(true);\n                setXAccountInfo(data.accountInfo);\n            } else {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n            }\n        } catch (error) {\n            console.error(\"Error loading X account status:\", error);\n            setXAccountConnected(false);\n            setXAccountInfo(null);\n        }\n    };\n    const handleConnectX = async ()=>{\n        if (!user) {\n            alert(\"Please log in first\");\n            return;\n        }\n        setConnectingX(true);\n        try {\n            const response = await fetch(\"/api/x/auth-url\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // Redirect directly to X authorization\n                window.location.href = data.authUrl;\n            } else {\n                alert(data.error || \"Failed to initiate X connection\");\n                setConnectingX(false);\n            }\n        } catch (error) {\n            console.error(\"Error connecting to X:\", error);\n            alert(\"Failed to connect to X. Please try again.\");\n            setConnectingX(false);\n        }\n    };\n    const handleVerifyPin = async ()=>{\n        if (!user || !pin.trim() || !oauthToken || !oauthTokenSecret) return;\n        setVerifyingPin(true);\n        try {\n            const response = await fetch(\"/api/x/verify-pin\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    oauth_token: oauthToken,\n                    oauth_token_secret: oauthTokenSecret,\n                    pin: pin.trim(),\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // Clear localStorage tokens\n                localStorage.removeItem(\"pending_x_oauth_token\");\n                localStorage.removeItem(\"pending_x_oauth_secret\");\n                // Update state\n                setXAccountConnected(true);\n                setXAccountInfo(data.accountInfo);\n                setShowPinModal(false);\n                setPin(\"\");\n                setOauthToken(\"\");\n                setOauthTokenSecret(\"\");\n                // Reload account status to make sure it's saved properly\n                await loadXAccountStatus();\n                alert(\"X account connected successfully!\");\n            } else {\n                alert(data.error || \"Failed to verify PIN\");\n            }\n        } catch (error) {\n            console.error(\"Error verifying PIN:\", error);\n            alert(\"Failed to verify PIN. Please try again.\");\n        } finally{\n            setVerifyingPin(false);\n        }\n    };\n    const handleDisconnectX = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/disconnect\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            if (response.ok) {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n                alert(\"X account disconnected successfully\");\n            } else {\n                const data = await response.json();\n                alert(data.error || \"Failed to disconnect X account\");\n            }\n        } catch (error) {\n            console.error(\"Error disconnecting X:\", error);\n            alert(\"Failed to disconnect X account. Please try again.\");\n        }\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        surface: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\"\n    };\n    const tabs = [\n        {\n            id: \"agent-e\",\n            label: \"Agent E\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot\n        },\n        {\n            id: \"account\",\n            label: \"Account\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User\n        },\n        {\n            id: \"integrations\",\n            label: \"Integrations\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap\n        },\n        {\n            id: \"notifications\",\n            label: \"Notifications\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bell\n        },\n        {\n            id: \"security\",\n            label: \"Security\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Shield\n        }\n    ];\n    const handleSavePrompt = (id, newPrompt)=>{\n        setCustomPrompts((prev)=>prev.map((p)=>p.id === id ? {\n                    ...p,\n                    prompt: newPrompt\n                } : p));\n    };\n    const addNewPrompt = ()=>{\n        const newId = Math.max(...customPrompts.map((p)=>p.id)) + 1;\n        setCustomPrompts((prev)=>[\n                ...prev,\n                {\n                    id: newId,\n                    name: \"New Prompt\",\n                    prompt: \"\"\n                }\n            ]);\n    };\n    const deletePrompt = (id)=>{\n        setCustomPrompts((prev)=>prev.filter((p)=>p.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.background\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"40px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"8px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        children: \"Settings\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            margin: 0,\n                            fontSize: \"16px\"\n                        },\n                        children: \"Configure Agent E and manage your account preferences\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    gap: \"8px\",\n                    marginBottom: \"40px\",\n                    borderBottom: \"1px solid \".concat(colors.border),\n                    paddingBottom: \"0\"\n                },\n                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(tab.id),\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"8px\",\n                            padding: \"12px 20px\",\n                            background: activeTab === tab.id ? colors.surface : \"transparent\",\n                            border: activeTab === tab.id ? \"1px solid \".concat(colors.border) : \"1px solid transparent\",\n                            borderBottom: activeTab === tab.id ? \"1px solid \".concat(colors.surface) : \"1px solid transparent\",\n                            borderRadius: \"8px 8px 0 0\",\n                            fontSize: \"14px\",\n                            fontWeight: \"500\",\n                            color: activeTab === tab.id ? colors.text.primary : colors.text.secondary,\n                            cursor: \"pointer\",\n                            transition: \"all 0.2s ease\",\n                            marginBottom: \"-1px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, undefined),\n                            tab.label\n                        ]\n                    }, tab.id, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: colors.surface,\n                    borderRadius: \"12px\",\n                    padding: \"32px\",\n                    border: \"1px solid \".concat(colors.border),\n                    minHeight: \"500px\"\n                },\n                children: [\n                    activeTab === \"agent-e\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Agent E Configuration\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Project Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gridTemplateColumns: \"1fr 1fr\",\n                                            gap: \"20px\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: \"Project Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: projectName,\n                                                        onChange: (e)=>setProjectName(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: \"Brand Voice\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: brandVoice,\n                                                        onChange: (e)=>setBrandVoice(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"professional\",\n                                                                children: \"Professional\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"casual\",\n                                                                children: \"Casual & Friendly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"technical\",\n                                                                children: \"Technical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"creative\",\n                                                                children: \"Creative & Fun\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"authoritative\",\n                                                                children: \"Authoritative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"Project Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: projectDescription,\n                                                onChange: (e)=>setProjectDescription(e.target.value),\n                                                placeholder: \"Describe your project, product, or service so Agent E can create relevant content...\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    padding: \"12px 16px\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    background: colors.background,\n                                                    outline: \"none\",\n                                                    minHeight: \"100px\",\n                                                    resize: \"vertical\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"Target Audience\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: targetAudience,\n                                                onChange: (e)=>setTargetAudience(e.target.value),\n                                                placeholder: \"e.g., Tech entrepreneurs, Small business owners, Developers...\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    padding: \"12px 16px\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    background: colors.background,\n                                                    outline: \"none\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    margin: 0\n                                                },\n                                                children: \"Custom Prompts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewPrompt,\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"6px\",\n                                                    padding: \"8px 16px\",\n                                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    cursor: \"pointer\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Plus, {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Add Prompt\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    customPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                border: \"1px solid \".concat(colors.border),\n                                                borderRadius: \"8px\",\n                                                padding: \"16px\",\n                                                marginBottom: \"12px\",\n                                                background: colors.background\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        alignItems: \"center\",\n                                                        marginBottom: \"12px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: prompt.name,\n                                                            onChange: (e)=>{\n                                                                setCustomPrompts((prev)=>prev.map((p)=>p.id === prompt.id ? {\n                                                                            ...p,\n                                                                            name: e.target.value\n                                                                        } : p));\n                                                            },\n                                                            style: {\n                                                                background: \"transparent\",\n                                                                border: \"none\",\n                                                                fontSize: \"16px\",\n                                                                fontWeight: \"600\",\n                                                                color: colors.text.primary,\n                                                                outline: \"none\",\n                                                                flex: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deletePrompt(prompt.id),\n                                                            style: {\n                                                                background: \"none\",\n                                                                border: \"none\",\n                                                                color: colors.text.tertiary,\n                                                                cursor: \"pointer\",\n                                                                padding: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Trash2, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: prompt.prompt,\n                                                    onChange: (e)=>handleSavePrompt(prompt.id, e.target.value),\n                                                    placeholder: \"Enter your custom prompt for Agent E...\",\n                                                    style: {\n                                                        width: \"100%\",\n                                                        padding: \"12px\",\n                                                        border: \"1px solid \".concat(colors.border),\n                                                        borderRadius: \"6px\",\n                                                        fontSize: \"14px\",\n                                                        background: colors.surface,\n                                                        outline: \"none\",\n                                                        minHeight: \"80px\",\n                                                        resize: \"vertical\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, prompt.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveAgentESettings,\n                                disabled: saving || !user,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 24px\",\n                                    background: saving || !user ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                    boxShadow: saving || !user ? \"none\" : \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                    opacity: saving || !user ? 0.6 : 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Save, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    saving ? \"Saving...\" : \"Save Agent E Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab === \"integrations\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Integrations\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"12px\",\n                                    padding: \"24px\",\n                                    marginBottom: \"24px\",\n                                    background: colors.background\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                size: 24,\n                                                color: colors.primary\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    margin: 0\n                                                },\n                                                children: \"X (Twitter) Account\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: \"Connect your X account to enable automated posting and content scheduling through our premium service.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    xAccountConnected && xAccountInfo ? // Connected State\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            padding: \"16px\",\n                                            background: \"#F0FDF4\",\n                                            border: \"1px solid #BBF7D0\",\n                                            borderRadius: \"8px\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\"\n                                                },\n                                                children: [\n                                                    xAccountInfo.profile_image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: xAccountInfo.profile_image_url,\n                                                        alt: \"Profile\",\n                                                        style: {\n                                                            width: \"40px\",\n                                                            height: \"40px\",\n                                                            borderRadius: \"50%\",\n                                                            objectFit: \"cover\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: \"#065F46\",\n                                                                    fontSize: \"16px\",\n                                                                    fontWeight: \"600\"\n                                                                },\n                                                                children: xAccountInfo.name || \"Connected Account\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: \"#047857\",\n                                                                    fontSize: \"14px\"\n                                                                },\n                                                                children: [\n                                                                    \"@\",\n                                                                    xAccountInfo.username\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    padding: \"6px 12px\",\n                                                    background: \"#10B981\",\n                                                    color: \"white\",\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"Connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 723,\n                                        columnNumber: 17\n                                    }, undefined) : // Not Connected State\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px\",\n                                            background: \"#FEF3C7\",\n                                            border: \"1px solid #FDE68A\",\n                                            borderRadius: \"8px\",\n                                            marginBottom: \"16px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"#92400E\",\n                                                    fontSize: \"14px\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"No X account connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"#B45309\",\n                                                    fontSize: \"12px\"\n                                                },\n                                                children: \"Connect your account to start scheduling posts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 790,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            gap: \"12px\"\n                                        },\n                                        children: xAccountConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDisconnectX,\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"10px 20px\",\n                                                background: \"#FEE2E2\",\n                                                color: \"#DC2626\",\n                                                border: \"1px solid #FECACA\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                cursor: \"pointer\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Disconnect Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                console.log(\"Connect X button clicked\");\n                                                handleConnectX();\n                                            },\n                                            disabled: connectingX,\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"12px 24px\",\n                                                background: connectingX ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                color: \"white\",\n                                                border: \"none\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                cursor: connectingX ? \"not-allowed\" : \"pointer\",\n                                                opacity: connectingX ? 0.6 : 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                connectingX ? \"Connecting...\" : \"Connect X Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 821,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 674,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab === \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Account Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 853,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Profile Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 868,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gap: \"16px\",\n                                            maxWidth: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Full Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 879,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: fullName,\n                                                        onChange: (e)=>setFullName(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 878,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Email Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        disabled: true,\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: \"#F9F9F9\",\n                                                            color: colors.text.secondary,\n                                                            cursor: \"not-allowed\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Avatar URL (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 932,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"url\",\n                                                        value: avatarUrl,\n                                                        onChange: (e)=>setAvatarUrl(e.target.value),\n                                                        placeholder: \"https://example.com/your-avatar.jpg\",\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 941,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            fontSize: \"12px\",\n                                                            color: colors.text.tertiary,\n                                                            marginTop: \"4px\",\n                                                            marginBottom: \"0\"\n                                                        },\n                                                        children: \"Enter a URL to your profile picture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 931,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 867,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 970,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \"15 0%, \").concat(colors.primaryLight, \"15 100%)\"),\n                                            border: \"1px solid \".concat(colors.primary, \"30\"),\n                                            borderRadius: \"12px\",\n                                            padding: \"20px\",\n                                            maxWidth: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\",\n                                                    marginBottom: \"12px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"32px\",\n                                                            height: \"32px\",\n                                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                            borderRadius: \"8px\",\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Crown, {\n                                                            size: 16,\n                                                            color: \"white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 1001,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: colors.text.primary,\n                                                                    fontSize: \"16px\",\n                                                                    fontWeight: \"600\"\n                                                                },\n                                                                children: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro Plan\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise Plan\" : \"Free Plan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: colors.text.secondary,\n                                                                    fontSize: \"14px\"\n                                                                },\n                                                                children: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"free\" ? \"No subscription\" : (profile === null || profile === void 0 ? void 0 : profile.subscription_status) === \"active\" ? \"$29/month • Active\" : \"Subscription inactive\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 1011,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 1003,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 986,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    gap: \"12px\",\n                                                    marginTop: \"16px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"8px 16px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        children: \"Manage Billing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 1025,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"8px 16px\",\n                                                            background: \"transparent\",\n                                                            color: colors.text.secondary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        children: \"Cancel Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 1037,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 1020,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 979,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 969,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveProfileSettings,\n                                disabled: saving || !user,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 24px\",\n                                    background: saving || !user ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                    boxShadow: saving || !user ? \"none\" : \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                    opacity: saving || !user ? 0.6 : 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Save, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 1073,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    saving ? \"Saving...\" : \"Save Account Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1054,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 852,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab !== \"agent-e\" && activeTab !== \"integrations\" && activeTab !== \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"60px 20px\",\n                            color: colors.text.secondary\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginBottom: \"12px\"\n                                },\n                                children: \"Coming Soon\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1086,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"This section is under development.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1087,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 1081,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, undefined),\n            showPinModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: \"rgba(0, 0, 0, 0.5)\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: colors.surface,\n                        borderRadius: \"16px\",\n                        padding: \"32px\",\n                        width: \"90%\",\n                        maxWidth: \"400px\",\n                        textAlign: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                color: colors.text.primary,\n                                fontSize: \"20px\",\n                                fontWeight: \"600\",\n                                marginBottom: \"16px\"\n                            },\n                            children: \"\\uD83D\\uDD17 Connect Your X Account\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1114,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#F0F9FF\",\n                                border: \"1px solid #BAE6FD\",\n                                borderRadius: \"8px\",\n                                padding: \"16px\",\n                                marginBottom: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: \"#0369A1\",\n                                        fontSize: \"14px\",\n                                        marginBottom: \"12px\",\n                                        fontWeight: \"600\"\n                                    },\n                                    children: \"\\uD83D\\uDCCB Instructions:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1130,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    style: {\n                                        color: \"#0369A1\",\n                                        fontSize: \"13px\",\n                                        lineHeight: \"1.5\",\n                                        margin: 0,\n                                        paddingLeft: \"20px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"A new tab will open with X authorization page\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1145,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Click \"Authorize app\" on the X page'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1146,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Copy the PIN code shown on X\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1147,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Come back to this page\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 1148,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \" and enter the PIN below\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1148,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Click \"Connect Account\" to finish'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1149,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1138,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#FEF3C7\",\n                                        border: \"1px solid #FDE68A\",\n                                        borderRadius: \"6px\",\n                                        padding: \"8px 12px\",\n                                        marginTop: \"12px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: \"#92400E\",\n                                            fontSize: \"12px\",\n                                            margin: 0,\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"\\uD83D\\uDCA1 This modal will stay open even if you refresh the page!\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 1159,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1152,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1123,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: colors.text.secondary,\n                                fontSize: \"14px\",\n                                marginBottom: \"20px\",\n                                textAlign: \"center\",\n                                fontWeight: \"600\"\n                            },\n                            children: \"Enter the PIN code from X:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1170,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: pin,\n                            onChange: (e)=>setPin(e.target.value),\n                            placeholder: \"Enter PIN code\",\n                            style: {\n                                width: \"100%\",\n                                padding: \"12px 16px\",\n                                border: \"1px solid \".concat(colors.border),\n                                borderRadius: \"8px\",\n                                fontSize: \"16px\",\n                                textAlign: \"center\",\n                                marginBottom: \"24px\",\n                                outline: \"none\"\n                            },\n                            maxLength: 7\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1180,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                gap: \"8px\",\n                                justifyContent: \"center\",\n                                flexWrap: \"wrap\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        // Clear localStorage tokens\n                                        localStorage.removeItem(\"pending_x_oauth_token\");\n                                        localStorage.removeItem(\"pending_x_oauth_secret\");\n                                        // Clear state\n                                        setShowPinModal(false);\n                                        setPin(\"\");\n                                        setOauthToken(\"\");\n                                        setOauthTokenSecret(\"\");\n                                    },\n                                    style: {\n                                        padding: \"12px 20px\",\n                                        background: colors.background,\n                                        color: colors.text.primary,\n                                        border: \"1px solid \".concat(colors.border),\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1199,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        // Re-open X authorization page\n                                        const authUrl = \"https://api.twitter.com/oauth/authorize?oauth_token=\".concat(oauthToken);\n                                        window.open(authUrl, \"_blank\");\n                                    },\n                                    disabled: !oauthToken,\n                                    style: {\n                                        padding: \"12px 20px\",\n                                        background: \"#1DA1F2\",\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: \"Open X Page\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1225,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleVerifyPin,\n                                    disabled: !pin.trim() || verifyingPin,\n                                    style: {\n                                        padding: \"12px 24px\",\n                                        background: !pin.trim() || verifyingPin ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        cursor: !pin.trim() || verifyingPin ? \"not-allowed\" : \"pointer\"\n                                    },\n                                    children: verifyingPin ? \"Verifying...\" : \"Connect Account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1246,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1198,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                    lineNumber: 1106,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 1094,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 331,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SettingsPage, \"fOtEomOVRvNx3M3sYkOOt8O0zJ4=\", false, function() {\n    return [\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser\n    ];\n});\n_c = SettingsPage;\nSettingsPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 1274,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/settings.tsx\n"));

/***/ })

});