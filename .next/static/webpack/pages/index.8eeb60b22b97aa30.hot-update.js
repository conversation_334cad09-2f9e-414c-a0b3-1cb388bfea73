"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/SidebarLayoutSimple.tsx":
/*!********************************************!*\
  !*** ./components/SidebarLayoutSimple.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,Search,User,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthModal */ \"./components/AuthModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    var _user_user_metadata;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, profile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Fallback user data for when not authenticated or loading\n    const userData = {\n        name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"Alex Chen\",\n        email: (user === null || user === void 0 ? void 0 : user.email) || \"<EMAIL>\",\n        plan: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise\" : \"Free\",\n        avatar: (profile === null || profile === void 0 ? void 0 : profile.avatar_url) || null,\n        isOnline: (profile === null || profile === void 0 ? void 0 : profile.is_online) || false\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        background: \"#F5F1EB\",\n        surface: \"#FFFFFF\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            surface: \"rgba(255, 107, 53, 0.05)\",\n            text: \"#2D1B14\",\n            textSecondary: \"#5D4037\",\n            textMuted: \"#8D6E63\",\n            accent: \"#FF6B35\",\n            accentSoft: \"#FF8A65\",\n            accentLight: \"#FFF7F4\",\n            border: \"rgba(255, 107, 53, 0.15)\",\n            hover: \"rgba(255, 107, 53, 0.08)\",\n            divider: \"rgba(255, 107, 53, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.25)\" // Orange glow\n        }\n    };\n    const menuItems = [\n        {\n            section: \"WORKSPACE\",\n            items: [\n                {\n                    href: \"/\",\n                    label: \"Briefing Room\",\n                    icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home\n                },\n                {\n                    href: \"/tweet-center\",\n                    label: \"Drafting Desk\",\n                    icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageCircle\n                },\n                {\n                    href: \"/dashboard\",\n                    label: \"Growth Lab\",\n                    icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.BarChart3\n                },\n                {\n                    href: \"/meeting\",\n                    label: \"AI Meetings\",\n                    icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Video\n                }\n            ]\n        },\n        {\n            section: \"SETTINGS\",\n            items: [\n                {\n                    href: \"/settings\",\n                    label: \"Settings\",\n                    icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User\n                },\n                {\n                    href: \"/settings?tab=account\",\n                    label: \"Account\",\n                    icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User\n                }\n            ]\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: colors.background,\n            padding: \"16px\",\n            gap: \"16px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    background: colors.sidebar.background,\n                    height: \"calc(100vh - 32px)\",\n                    borderRadius: \"24px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: \"0 8px 32px \".concat(colors.sidebar.glow, \", 0 2px 8px rgba(255, 107, 53, 0.1)\"),\n                    border: \"1px solid \".concat(colors.sidebar.border),\n                    overflow: \"hidden\",\n                    position: \"relative\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            height: \"2px\",\n                            background: \"linear-gradient(90deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \", \").concat(colors.sidebar.accent, \")\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"20px 16px 16px 16px\",\n                            textAlign: \"center\",\n                            background: colors.sidebar.accentLight\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"32px\",\n                                    color: colors.sidebar.accent,\n                                    fontFamily: \"Dancing Script, cursive\",\n                                    fontWeight: \"600\",\n                                    letterSpacing: \"1px\",\n                                    textShadow: \"0 2px 4px \".concat(colors.sidebar.glow),\n                                    marginBottom: \"4px\"\n                                },\n                                children: \"Exie\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"10px\",\n                                    color: colors.sidebar.textMuted,\n                                    fontWeight: \"500\",\n                                    letterSpacing: \"1px\",\n                                    textTransform: \"uppercase\"\n                                },\n                                children: \"AI Assistant\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"0 16px 16px 16px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                display: \"flex\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Search, {\n                                    size: 14,\n                                    color: colors.sidebar.textMuted,\n                                    style: {\n                                        position: \"absolute\",\n                                        left: \"12px\",\n                                        zIndex: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search...\",\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"8px 12px 8px 36px\",\n                                        border: \"1px solid \".concat(colors.sidebar.border),\n                                        borderRadius: \"8px\",\n                                        background: colors.sidebar.surface,\n                                        color: colors.sidebar.text,\n                                        fontSize: \"12px\",\n                                        fontWeight: \"400\",\n                                        outline: \"none\",\n                                        transition: \"all 0.2s ease\"\n                                    },\n                                    onFocus: (e)=>{\n                                        e.target.style.borderColor = colors.sidebar.accent;\n                                        e.target.style.background = colors.sidebar.background;\n                                    },\n                                    onBlur: (e)=>{\n                                        e.target.style.borderColor = colors.sidebar.border;\n                                        e.target.style.background = colors.sidebar.surface;\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"8px 16px\",\n                            flex: 1,\n                            display: \"flex\",\n                            flexDirection: \"column\"\n                        },\n                        children: menuItems.map((section, sectionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: sectionIndex < menuItems.length - 1 ? \"24px\" : \"0\"\n                                },\n                                children: [\n                                    sectionIndex > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            height: \"1px\",\n                                            background: \"linear-gradient(90deg, transparent, \".concat(colors.sidebar.divider, \", transparent)\"),\n                                            margin: \"16px 12px 20px 12px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"10px\",\n                                            fontWeight: \"600\",\n                                            color: colors.sidebar.textMuted,\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"1px\",\n                                            marginBottom: \"10px\",\n                                            paddingLeft: \"12px\"\n                                        },\n                                        children: section.section\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\",\n                                            gap: \"2px\"\n                                        },\n                                        children: section.items.map((item)=>{\n                                            const active = isActive(item.href);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        padding: \"8px 12px\",\n                                                        borderRadius: \"8px\",\n                                                        background: active ? colors.sidebar.surface : \"transparent\",\n                                                        cursor: \"pointer\",\n                                                        transition: \"all 0.2s ease\",\n                                                        position: \"relative\"\n                                                    },\n                                                    onMouseEnter: (e)=>{\n                                                        if (!active) {\n                                                            e.currentTarget.style.background = colors.sidebar.hover;\n                                                        }\n                                                    },\n                                                    onMouseLeave: (e)=>{\n                                                        if (!active) {\n                                                            e.currentTarget.style.background = \"transparent\";\n                                                        }\n                                                    },\n                                                    children: [\n                                                        active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: \"absolute\",\n                                                                left: \"0\",\n                                                                top: \"50%\",\n                                                                transform: \"translateY(-50%)\",\n                                                                width: \"2px\",\n                                                                height: \"16px\",\n                                                                background: colors.sidebar.accent,\n                                                                borderRadius: \"0 2px 2px 0\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: \"16px\",\n                                                                height: \"16px\",\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                justifyContent: \"center\",\n                                                                marginRight: \"10px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 14,\n                                                                color: active ? colors.sidebar.accent : colors.sidebar.textSecondary,\n                                                                strokeWidth: 2\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                                fontSize: \"13px\",\n                                                                fontWeight: active ? \"500\" : \"400\",\n                                                                letterSpacing: \"0.1px\"\n                                                            },\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, item.href, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, section.section, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            margin: \"0 16px 16px 16px\",\n                            height: \"1px\",\n                            background: \"linear-gradient(90deg, transparent, \".concat(colors.sidebar.divider, \", transparent)\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"12px 12px 16px 12px\",\n                            background: colors.sidebar.accentLight\n                        },\n                        children: user ? // Authenticated user - show profile\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"10px\",\n                                    padding: \"10px 12px\",\n                                    background: colors.sidebar.background,\n                                    borderRadius: \"14px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                    border: \"1px solid \".concat(colors.sidebar.border),\n                                    boxShadow: \"0 2px 8px \".concat(colors.sidebar.glow)\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.surface;\n                                    e.currentTarget.style.borderColor = colors.sidebar.accent + \"40\";\n                                    e.currentTarget.style.transform = \"translateY(-1px)\";\n                                    e.currentTarget.style.boxShadow = \"0 4px 16px \".concat(colors.sidebar.glow);\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.background;\n                                    e.currentTarget.style.borderColor = colors.sidebar.border;\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                    e.currentTarget.style.boxShadow = \"0 2px 8px \".concat(colors.sidebar.glow);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: userData.avatar ? \"transparent\" : \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\"),\n                                            borderRadius: \"10px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            overflow: \"hidden\",\n                                            border: \"2px solid \".concat(colors.sidebar.border)\n                                        },\n                                        children: [\n                                            userData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userData.avatar,\n                                                alt: userData.name,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"8px\"\n                                                },\n                                                onError: (e)=>{\n                                                    // Fallback to icon if image fails to load\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.parentElement.style.background = \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\");\n                                                    e.currentTarget.parentElement.innerHTML = '<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path><circle cx=\"12\" cy=\"7\" r=\"4\"></circle></svg>';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 16,\n                                                color: \"#FFFFFF\",\n                                                strokeWidth: 2\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            userData.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"10px\",\n                                                    height: \"10px\",\n                                                    background: \"#00FF88\",\n                                                    borderRadius: \"50%\",\n                                                    border: \"2px solid #FFFFFF\",\n                                                    boxShadow: \"0 0 6px rgba(0, 255, 136, 0.8)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    lineHeight: \"1.2\",\n                                                    marginBottom: \"2px\"\n                                                },\n                                                children: userData.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.textMuted,\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"500\",\n                                                    lineHeight: \"1.2\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            userData.plan,\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: colors.sidebar.accent\n                                                        },\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, undefined) : // Not authenticated - show sign in button\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>setShowAuthModal(true),\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"10px\",\n                                padding: \"10px 12px\",\n                                background: colors.sidebar.background,\n                                borderRadius: \"14px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                border: \"1px solid \".concat(colors.sidebar.border),\n                                boxShadow: \"0 2px 8px \".concat(colors.sidebar.glow)\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.background = colors.sidebar.surface;\n                                e.currentTarget.style.borderColor = colors.sidebar.accent + \"40\";\n                                e.currentTarget.style.transform = \"translateY(-1px)\";\n                                e.currentTarget.style.boxShadow = \"0 4px 16px \".concat(colors.sidebar.glow);\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.background = colors.sidebar.background;\n                                e.currentTarget.style.borderColor = colors.sidebar.border;\n                                e.currentTarget.style.transform = \"translateY(0)\";\n                                e.currentTarget.style.boxShadow = \"0 2px 8px \".concat(colors.sidebar.glow);\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"32px\",\n                                        height: \"32px\",\n                                        background: \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\"),\n                                        borderRadius: \"10px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        border: \"2px solid \".concat(colors.sidebar.border)\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogIn, {\n                                        size: 16,\n                                        color: \"#FFFFFF\",\n                                        strokeWidth: 2\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"13px\",\n                                                fontWeight: \"600\",\n                                                lineHeight: \"1.2\",\n                                                marginBottom: \"2px\"\n                                            },\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.textMuted,\n                                                fontSize: \"11px\",\n                                                fontWeight: \"500\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Access your account\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.surface,\n                    borderRadius: \"24px\",\n                    height: \"calc(100vh - 32px)\",\n                    position: \"relative\",\n                    boxShadow: \"0 4px 20px rgba(255, 107, 53, 0.08)\",\n                    border: \"1px solid \".concat(colors.sidebar.border),\n                    overflow: \"hidden\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"32px\",\n                        height: \"100%\",\n                        overflow: \"auto\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 465,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 485,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"2fxYphseme7UlXU7fqnQRjVv+Ys=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayoutSimple.tsx\n"));

/***/ })

});