"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* Global styles for Exie AI */\\n\\n/* Import Anek Tamil font for branding */\\n@import url('https://fonts.googleapis.com/css2?family=Anek+Tamil:wght@100;200;300;400;500;600;700;800&display=swap');\\n\\n* {\\n  box-sizing: border-box;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\nhtml,\\nbody {\\n  max-width: 100vw;\\n  overflow-x: hidden;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\\n    sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\nbody {\\n  color: #333;\\n  background: #ffffff;\\n}\\n\\na {\\n  color: inherit;\\n  text-decoration: none;\\n}\\n\\n/* CSS Variables for theming */\\n:root {\\n  --accent-color: #007bff;\\n  --background-color: #ffffff;\\n  --text-color: #333333;\\n  --border-color: #e0e0e0;\\n  --hover-color: #f5f5f5;\\n}\\n\\n/* Light mode theme (default) */\\n@media (prefers-color-scheme: light) {\\n  :root {\\n    --accent-color: #007bff;\\n    --background-color: #ffffff;\\n    --text-color: #333333;\\n    --border-color: #e0e0e0;\\n    --hover-color: #f5f5f5;\\n  }\\n}\\n\\n/* Button styles */\\nbutton {\\n  font-family: inherit;\\n  cursor: pointer;\\n  border: none;\\n  border-radius: 4px;\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  transition: all 0.2s ease;\\n}\\n\\nbutton:hover {\\n  opacity: 0.9;\\n}\\n\\nbutton:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n/* Input styles */\\ninput, textarea {\\n  font-family: inherit;\\n  border: 1px solid var(--border-color);\\n  border-radius: 4px;\\n  padding: 8px 12px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease;\\n}\\n\\ninput:focus, textarea:focus {\\n  outline: none;\\n  border-color: var(--accent-color);\\n}\\n\\n/* Subtle animations for account popup */\\n@keyframes subtleFadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateX(-4px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n/* Utility classes */\\n.container {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 16px;\\n}\\n\\n.text-center {\\n  text-align: center;\\n}\\n\\n.mb-4 {\\n  margin-bottom: 16px;\\n}\\n\\n.mt-4 {\\n  margin-top: 16px;\\n}\\n\\n.p-4 {\\n  padding: 16px;\\n}\\n\\n/* Loading spinner */\\n.spinner {\\n  border: 2px solid #f3f3f3;\\n  border-top: 2px solid var(--accent-color);\\n  border-radius: 50%;\\n  width: 20px;\\n  height: 20px;\\n  animation: spin 1s linear infinite;\\n}\\n\\n@keyframes spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n/* Responsive design */\\n@media (max-width: 768px) {\\n  .container {\\n    padding: 0 12px;\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,8BAA8B;;AAE9B,wCAAwC;AACxC,oHAAoH;;AAEpH;EACE,sBAAsB;EACtB,UAAU;EACV,SAAS;AACX;;AAEA;;EAEE,gBAAgB;EAChB,kBAAkB;EAClB;;cAEY;EACZ,mCAAmC;EACnC,kCAAkC;AACpC;;AAEA;EACE,WAAW;EACX,mBAAmB;AACrB;;AAEA;EACE,cAAc;EACd,qBAAqB;AACvB;;AAEA,8BAA8B;AAC9B;EACE,uBAAuB;EACvB,2BAA2B;EAC3B,qBAAqB;EACrB,uBAAuB;EACvB,sBAAsB;AACxB;;AAEA,+BAA+B;AAC/B;EACE;IACE,uBAAuB;IACvB,2BAA2B;IAC3B,qBAAqB;IACrB,uBAAuB;IACvB,sBAAsB;EACxB;AACF;;AAEA,kBAAkB;AAClB;EACE,oBAAoB;EACpB,eAAe;EACf,YAAY;EACZ,kBAAkB;EAClB,iBAAiB;EACjB,eAAe;EACf,yBAAyB;AAC3B;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA,iBAAiB;AACjB;EACE,oBAAoB;EACpB,qCAAqC;EACrC,kBAAkB;EAClB,iBAAiB;EACjB,eAAe;EACf,kCAAkC;AACpC;;AAEA;EACE,aAAa;EACb,iCAAiC;AACnC;;AAEA,wCAAwC;AACxC;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA,oBAAoB;AACpB;EACE,iBAAiB;EACjB,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,aAAa;AACf;;AAEA,oBAAoB;AACpB;EACE,yBAAyB;EACzB,yCAAyC;EACzC,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,kCAAkC;AACpC;;AAEA;EACE,KAAK,uBAAuB,EAAE;EAC9B,OAAO,yBAAyB,EAAE;AACpC;;AAEA,sBAAsB;AACtB;EACE;IACE,eAAe;EACjB;AACF\",\"sourcesContent\":[\"/* Global styles for Exie AI */\\n\\n/* Import Anek Tamil font for branding */\\n@import url('https://fonts.googleapis.com/css2?family=Anek+Tamil:wght@100;200;300;400;500;600;700;800&display=swap');\\n\\n* {\\n  box-sizing: border-box;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\nhtml,\\nbody {\\n  max-width: 100vw;\\n  overflow-x: hidden;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\\n    sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\nbody {\\n  color: #333;\\n  background: #ffffff;\\n}\\n\\na {\\n  color: inherit;\\n  text-decoration: none;\\n}\\n\\n/* CSS Variables for theming */\\n:root {\\n  --accent-color: #007bff;\\n  --background-color: #ffffff;\\n  --text-color: #333333;\\n  --border-color: #e0e0e0;\\n  --hover-color: #f5f5f5;\\n}\\n\\n/* Light mode theme (default) */\\n@media (prefers-color-scheme: light) {\\n  :root {\\n    --accent-color: #007bff;\\n    --background-color: #ffffff;\\n    --text-color: #333333;\\n    --border-color: #e0e0e0;\\n    --hover-color: #f5f5f5;\\n  }\\n}\\n\\n/* Button styles */\\nbutton {\\n  font-family: inherit;\\n  cursor: pointer;\\n  border: none;\\n  border-radius: 4px;\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  transition: all 0.2s ease;\\n}\\n\\nbutton:hover {\\n  opacity: 0.9;\\n}\\n\\nbutton:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n/* Input styles */\\ninput, textarea {\\n  font-family: inherit;\\n  border: 1px solid var(--border-color);\\n  border-radius: 4px;\\n  padding: 8px 12px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease;\\n}\\n\\ninput:focus, textarea:focus {\\n  outline: none;\\n  border-color: var(--accent-color);\\n}\\n\\n/* Subtle animations for account popup */\\n@keyframes subtleFadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateX(-4px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n/* Utility classes */\\n.container {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 16px;\\n}\\n\\n.text-center {\\n  text-align: center;\\n}\\n\\n.mb-4 {\\n  margin-bottom: 16px;\\n}\\n\\n.mt-4 {\\n  margin-top: 16px;\\n}\\n\\n.p-4 {\\n  padding: 16px;\\n}\\n\\n/* Loading spinner */\\n.spinner {\\n  border: 2px solid #f3f3f3;\\n  border-top: 2px solid var(--accent-color);\\n  border-radius: 50%;\\n  width: 20px;\\n  height: 20px;\\n  animation: spin 1s linear infinite;\\n}\\n\\n@keyframes spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n/* Responsive design */\\n@media (max-width: 768px) {\\n  .container {\\n    padding: 0 12px;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});