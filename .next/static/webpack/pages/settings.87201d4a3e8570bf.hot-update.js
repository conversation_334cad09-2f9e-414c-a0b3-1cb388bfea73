"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/settings",{

/***/ "./pages/settings.tsx":
/*!****************************!*\
  !*** ./pages/settings.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!lucide-react */ \"__barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/supabase */ \"./lib/supabase.ts\");\n// pages/settings.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SettingsPage = ()=>{\n    _s();\n    const { user, profile, updateProfile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"agent-e\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Agent E Settings\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"My AI Project\");\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [targetAudience, setTargetAudience] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [brandVoice, setBrandVoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"professional\");\n    const [customPrompts, setCustomPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: \"Product Launch\",\n            prompt: \"Create engaging content for product launches with excitement and clear benefits\"\n        },\n        {\n            id: 2,\n            name: \"Educational\",\n            prompt: \"Write informative content that teaches and provides value to the audience\"\n        }\n    ]);\n    // Profile Settings\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [avatarUrl, setAvatarUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // X Integration Settings\n    const [xAccountConnected, setXAccountConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [xAccountInfo, setXAccountInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connectingX, setConnectingX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPinModal, setShowPinModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [oauthToken, setOauthToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [pin, setPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [verifyingPin, setVerifyingPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load settings from Supabase\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            var _user_user_metadata;\n            loadSettings();\n            loadXAccountStatus();\n            setFullName((profile === null || profile === void 0 ? void 0 : profile.full_name) || ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"\");\n            setEmail(user.email || \"\");\n            setAvatarUrl((profile === null || profile === void 0 ? void 0 : profile.avatar_url) || \"\");\n        }\n    }, [\n        user,\n        profile\n    ]);\n    const loadSettings = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"agent_e_settings\").select(\"*\").eq(\"user_id\", user.id).single();\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error loading settings:\", error);\n                return;\n            }\n            if (data) {\n                setProjectName(data.project_name || \"My AI Project\");\n                setProjectDescription(data.project_description || \"\");\n                setTargetAudience(data.target_audience || \"\");\n                setBrandVoice(data.brand_voice || \"professional\");\n                setCustomPrompts(data.custom_prompts || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading settings:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveAgentESettings = async ()=>{\n        if (!user) return;\n        setSaving(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"agent_e_settings\").upsert({\n                user_id: user.id,\n                project_name: projectName,\n                project_description: projectDescription,\n                target_audience: targetAudience,\n                brand_voice: brandVoice,\n                custom_prompts: customPrompts\n            });\n            if (error) {\n                console.error(\"Error saving settings:\", error);\n                alert(\"Error saving settings. Please try again.\");\n            } else {\n                alert(\"Settings saved successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            alert(\"Error saving settings. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const saveProfileSettings = async ()=>{\n        if (!user) {\n            alert(\"You must be logged in to update your profile.\");\n            return;\n        }\n        setSaving(true);\n        try {\n            console.log(\"Updating profile with:\", {\n                full_name: fullName\n            });\n            // Try to update the profile\n            const { error } = await updateProfile({\n                full_name: fullName,\n                avatar_url: avatarUrl || null\n            });\n            if (error) {\n                var _error_message;\n                console.error(\"Error updating profile:\", error);\n                // If the table doesn't exist, try to create it first\n                if (error.code === \"42P01\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('relation \"user_profiles\" does not exist'))) {\n                    alert(\"Database setup required. Please run the database schema first, then try again.\");\n                } else {\n                    alert(\"Error updating profile: \".concat(error.message || \"Please try again.\"));\n                }\n            } else {\n                alert(\"Profile updated successfully!\");\n                console.log(\"Profile updated successfully\");\n            }\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            alert(\"Error updating profile. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const loadXAccountStatus = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/account-status?userId=\".concat(user.id));\n            const data = await response.json();\n            if (response.ok && data.connected) {\n                setXAccountConnected(true);\n                setXAccountInfo(data.accountInfo);\n            } else {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n            }\n        } catch (error) {\n            console.error(\"Error loading X account status:\", error);\n            setXAccountConnected(false);\n            setXAccountInfo(null);\n        }\n    };\n    const handleConnectX = async ()=>{\n        if (!user) return;\n        setConnectingX(true);\n        try {\n            const response = await fetch(\"/api/x/auth-url\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // Store the OAuth token and open the authorization URL\n                setOauthToken(data.oauth_token);\n                window.open(data.authUrl, \"_blank\");\n                setShowPinModal(true);\n            } else {\n                alert(data.error || \"Failed to initiate X connection\");\n            }\n        } catch (error) {\n            console.error(\"Error connecting to X:\", error);\n            alert(\"Failed to connect to X. Please try again.\");\n        } finally{\n            setConnectingX(false);\n        }\n    };\n    const handleVerifyPin = async ()=>{\n        if (!user || !pin.trim() || !oauthToken) return;\n        setVerifyingPin(true);\n        try {\n            const response = await fetch(\"/api/x/verify-pin\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    oauth_token: oauthToken,\n                    pin: pin.trim(),\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                setXAccountConnected(true);\n                setXAccountInfo(data.accountInfo);\n                setShowPinModal(false);\n                setPin(\"\");\n                setOauthToken(\"\");\n                alert(\"X account connected successfully!\");\n            } else {\n                alert(data.error || \"Failed to verify PIN\");\n            }\n        } catch (error) {\n            console.error(\"Error verifying PIN:\", error);\n            alert(\"Failed to verify PIN. Please try again.\");\n        } finally{\n            setVerifyingPin(false);\n        }\n    };\n    const handleDisconnectX = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/disconnect\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            if (response.ok) {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n                alert(\"X account disconnected successfully\");\n            } else {\n                const data = await response.json();\n                alert(data.error || \"Failed to disconnect X account\");\n            }\n        } catch (error) {\n            console.error(\"Error disconnecting X:\", error);\n            alert(\"Failed to disconnect X account. Please try again.\");\n        }\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        surface: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\"\n    };\n    const tabs = [\n        {\n            id: \"agent-e\",\n            label: \"Agent E\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot\n        },\n        {\n            id: \"account\",\n            label: \"Account\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User\n        },\n        {\n            id: \"integrations\",\n            label: \"Integrations\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap\n        },\n        {\n            id: \"notifications\",\n            label: \"Notifications\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bell\n        },\n        {\n            id: \"security\",\n            label: \"Security\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Shield\n        }\n    ];\n    const handleSavePrompt = (id, newPrompt)=>{\n        setCustomPrompts((prev)=>prev.map((p)=>p.id === id ? {\n                    ...p,\n                    prompt: newPrompt\n                } : p));\n    };\n    const addNewPrompt = ()=>{\n        const newId = Math.max(...customPrompts.map((p)=>p.id)) + 1;\n        setCustomPrompts((prev)=>[\n                ...prev,\n                {\n                    id: newId,\n                    name: \"New Prompt\",\n                    prompt: \"\"\n                }\n            ]);\n    };\n    const deletePrompt = (id)=>{\n        setCustomPrompts((prev)=>prev.filter((p)=>p.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.background\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"40px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"8px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        children: \"Settings\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            margin: 0,\n                            fontSize: \"16px\"\n                        },\n                        children: \"Configure Agent E and manage your account preferences\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    gap: \"8px\",\n                    marginBottom: \"40px\",\n                    borderBottom: \"1px solid \".concat(colors.border),\n                    paddingBottom: \"0\"\n                },\n                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(tab.id),\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"8px\",\n                            padding: \"12px 20px\",\n                            background: activeTab === tab.id ? colors.surface : \"transparent\",\n                            border: activeTab === tab.id ? \"1px solid \".concat(colors.border) : \"1px solid transparent\",\n                            borderBottom: activeTab === tab.id ? \"1px solid \".concat(colors.surface) : \"1px solid transparent\",\n                            borderRadius: \"8px 8px 0 0\",\n                            fontSize: \"14px\",\n                            fontWeight: \"500\",\n                            color: activeTab === tab.id ? colors.text.primary : colors.text.secondary,\n                            cursor: \"pointer\",\n                            transition: \"all 0.2s ease\",\n                            marginBottom: \"-1px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, undefined),\n                            tab.label\n                        ]\n                    }, tab.id, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: colors.surface,\n                    borderRadius: \"12px\",\n                    padding: \"32px\",\n                    border: \"1px solid \".concat(colors.border),\n                    minHeight: \"500px\"\n                },\n                children: [\n                    activeTab === \"agent-e\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Agent E Configuration\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Project Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gridTemplateColumns: \"1fr 1fr\",\n                                            gap: \"20px\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: \"Project Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: projectName,\n                                                        onChange: (e)=>setProjectName(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: \"Brand Voice\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: brandVoice,\n                                                        onChange: (e)=>setBrandVoice(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"professional\",\n                                                                children: \"Professional\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"casual\",\n                                                                children: \"Casual & Friendly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"technical\",\n                                                                children: \"Technical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"creative\",\n                                                                children: \"Creative & Fun\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"authoritative\",\n                                                                children: \"Authoritative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"Project Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: projectDescription,\n                                                onChange: (e)=>setProjectDescription(e.target.value),\n                                                placeholder: \"Describe your project, product, or service so Agent E can create relevant content...\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    padding: \"12px 16px\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    background: colors.background,\n                                                    outline: \"none\",\n                                                    minHeight: \"100px\",\n                                                    resize: \"vertical\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"Target Audience\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: targetAudience,\n                                                onChange: (e)=>setTargetAudience(e.target.value),\n                                                placeholder: \"e.g., Tech entrepreneurs, Small business owners, Developers...\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    padding: \"12px 16px\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    background: colors.background,\n                                                    outline: \"none\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    margin: 0\n                                                },\n                                                children: \"Custom Prompts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewPrompt,\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"6px\",\n                                                    padding: \"8px 16px\",\n                                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    cursor: \"pointer\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Plus, {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Add Prompt\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    customPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                border: \"1px solid \".concat(colors.border),\n                                                borderRadius: \"8px\",\n                                                padding: \"16px\",\n                                                marginBottom: \"12px\",\n                                                background: colors.background\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        alignItems: \"center\",\n                                                        marginBottom: \"12px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: prompt.name,\n                                                            onChange: (e)=>{\n                                                                setCustomPrompts((prev)=>prev.map((p)=>p.id === prompt.id ? {\n                                                                            ...p,\n                                                                            name: e.target.value\n                                                                        } : p));\n                                                            },\n                                                            style: {\n                                                                background: \"transparent\",\n                                                                border: \"none\",\n                                                                fontSize: \"16px\",\n                                                                fontWeight: \"600\",\n                                                                color: colors.text.primary,\n                                                                outline: \"none\",\n                                                                flex: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deletePrompt(prompt.id),\n                                                            style: {\n                                                                background: \"none\",\n                                                                border: \"none\",\n                                                                color: colors.text.tertiary,\n                                                                cursor: \"pointer\",\n                                                                padding: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Trash2, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: prompt.prompt,\n                                                    onChange: (e)=>handleSavePrompt(prompt.id, e.target.value),\n                                                    placeholder: \"Enter your custom prompt for Agent E...\",\n                                                    style: {\n                                                        width: \"100%\",\n                                                        padding: \"12px\",\n                                                        border: \"1px solid \".concat(colors.border),\n                                                        borderRadius: \"6px\",\n                                                        fontSize: \"14px\",\n                                                        background: colors.surface,\n                                                        outline: \"none\",\n                                                        minHeight: \"80px\",\n                                                        resize: \"vertical\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, prompt.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveAgentESettings,\n                                disabled: saving || !user,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 24px\",\n                                    background: saving || !user ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                    boxShadow: saving || !user ? \"none\" : \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                    opacity: saving || !user ? 0.6 : 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Save, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    saving ? \"Saving...\" : \"Save Agent E Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab === \"integrations\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Integrations\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"12px\",\n                                    padding: \"24px\",\n                                    marginBottom: \"24px\",\n                                    background: colors.background\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                size: 24,\n                                                color: colors.primary\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    margin: 0\n                                                },\n                                                children: \"X (Twitter) Account\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: \"Connect your X account to enable automated posting and content scheduling through our premium service.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    xAccountConnected && xAccountInfo ? // Connected State\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            padding: \"16px\",\n                                            background: \"#F0FDF4\",\n                                            border: \"1px solid #BBF7D0\",\n                                            borderRadius: \"8px\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\"\n                                                },\n                                                children: [\n                                                    xAccountInfo.profile_image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: xAccountInfo.profile_image_url,\n                                                        alt: \"Profile\",\n                                                        style: {\n                                                            width: \"40px\",\n                                                            height: \"40px\",\n                                                            borderRadius: \"50%\",\n                                                            objectFit: \"cover\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: \"#065F46\",\n                                                                    fontSize: \"16px\",\n                                                                    fontWeight: \"600\"\n                                                                },\n                                                                children: xAccountInfo.name || \"Connected Account\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: \"#047857\",\n                                                                    fontSize: \"14px\"\n                                                                },\n                                                                children: [\n                                                                    \"@\",\n                                                                    xAccountInfo.username\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    padding: \"6px 12px\",\n                                                    background: \"#10B981\",\n                                                    color: \"white\",\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"Connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 17\n                                    }, undefined) : // Not Connected State\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px\",\n                                            background: \"#FEF3C7\",\n                                            border: \"1px solid #FDE68A\",\n                                            borderRadius: \"8px\",\n                                            marginBottom: \"16px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"#92400E\",\n                                                    fontSize: \"14px\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"No X account connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"#B45309\",\n                                                    fontSize: \"12px\"\n                                                },\n                                                children: \"Connect your account to start scheduling posts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            gap: \"12px\"\n                                        },\n                                        children: xAccountConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDisconnectX,\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"10px 20px\",\n                                                background: \"#FEE2E2\",\n                                                color: \"#DC2626\",\n                                                border: \"1px solid #FECACA\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                cursor: \"pointer\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Disconnect Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleConnectX,\n                                            disabled: connectingX,\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"12px 24px\",\n                                                background: connectingX ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                color: \"white\",\n                                                border: \"none\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                cursor: connectingX ? \"not-allowed\" : \"pointer\",\n                                                opacity: connectingX ? 0.6 : 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                connectingX ? \"Connecting...\" : \"Connect X Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab === \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 830,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Account Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 821,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Profile Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 836,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gap: \"16px\",\n                                            maxWidth: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Full Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 847,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: fullName,\n                                                        onChange: (e)=>setFullName(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 846,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Email Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        disabled: true,\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: \"#F9F9F9\",\n                                                            color: colors.text.secondary,\n                                                            cursor: \"not-allowed\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Avatar URL (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"url\",\n                                                        value: avatarUrl,\n                                                        onChange: (e)=>setAvatarUrl(e.target.value),\n                                                        placeholder: \"https://example.com/your-avatar.jpg\",\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 909,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            fontSize: \"12px\",\n                                                            color: colors.text.tertiary,\n                                                            marginTop: \"4px\",\n                                                            marginBottom: \"0\"\n                                                        },\n                                                        children: \"Enter a URL to your profile picture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 924,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 899,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 835,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 938,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \"15 0%, \").concat(colors.primaryLight, \"15 100%)\"),\n                                            border: \"1px solid \".concat(colors.primary, \"30\"),\n                                            borderRadius: \"12px\",\n                                            padding: \"20px\",\n                                            maxWidth: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\",\n                                                    marginBottom: \"12px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"32px\",\n                                                            height: \"32px\",\n                                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                            borderRadius: \"8px\",\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Crown, {\n                                                            size: 16,\n                                                            color: \"white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 969,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 960,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: colors.text.primary,\n                                                                    fontSize: \"16px\",\n                                                                    fontWeight: \"600\"\n                                                                },\n                                                                children: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro Plan\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise Plan\" : \"Free Plan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 972,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: colors.text.secondary,\n                                                                    fontSize: \"14px\"\n                                                                },\n                                                                children: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"free\" ? \"No subscription\" : (profile === null || profile === void 0 ? void 0 : profile.subscription_status) === \"active\" ? \"$29/month • Active\" : \"Subscription inactive\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 979,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 971,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 954,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    gap: \"12px\",\n                                                    marginTop: \"16px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"8px 16px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        children: \"Manage Billing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 993,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"8px 16px\",\n                                                            background: \"transparent\",\n                                                            color: colors.text.secondary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        children: \"Cancel Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 988,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 947,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 937,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveProfileSettings,\n                                disabled: saving || !user,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 24px\",\n                                    background: saving || !user ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                    boxShadow: saving || !user ? \"none\" : \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                    opacity: saving || !user ? 0.6 : 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Save, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 1041,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    saving ? \"Saving...\" : \"Save Account Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1022,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 820,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab !== \"agent-e\" && activeTab !== \"integrations\" && activeTab !== \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"60px 20px\",\n                            color: colors.text.secondary\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginBottom: \"12px\"\n                                },\n                                children: \"Coming Soon\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1054,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"This section is under development.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1055,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 1049,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 368,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SettingsPage, \"f/n+y5xvjVo0A1V28Cs+FT/v84g=\", false, function() {\n    return [\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser\n    ];\n});\n_c = SettingsPage;\nSettingsPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 1065,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/settings.tsx\n"));

/***/ })

});