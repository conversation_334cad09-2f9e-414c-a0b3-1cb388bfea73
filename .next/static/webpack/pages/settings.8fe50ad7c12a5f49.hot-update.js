"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/settings",{

/***/ "./pages/settings.tsx":
/*!****************************!*\
  !*** ./pages/settings.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!lucide-react */ \"__barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/supabase */ \"./lib/supabase.ts\");\n// pages/settings.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SettingsPage = ()=>{\n    _s();\n    const { user, profile, updateProfile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"agent-e\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Agent E Settings\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"My AI Project\");\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [targetAudience, setTargetAudience] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [brandVoice, setBrandVoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"professional\");\n    const [customPrompts, setCustomPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: \"Product Launch\",\n            prompt: \"Create engaging content for product launches with excitement and clear benefits\"\n        },\n        {\n            id: 2,\n            name: \"Educational\",\n            prompt: \"Write informative content that teaches and provides value to the audience\"\n        }\n    ]);\n    // Profile Settings\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [avatarUrl, setAvatarUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // X Integration Settings\n    const [xAccountConnected, setXAccountConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [xAccountInfo, setXAccountInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connectingX, setConnectingX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load settings from Supabase\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            var _user_user_metadata;\n            loadSettings();\n            loadXAccountStatus();\n            setFullName((profile === null || profile === void 0 ? void 0 : profile.full_name) || ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"\");\n            setEmail(user.email || \"\");\n            setAvatarUrl((profile === null || profile === void 0 ? void 0 : profile.avatar_url) || \"\");\n        }\n    }, [\n        user,\n        profile\n    ]);\n    // Handle URL parameters for success/error messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const urlParams = new URLSearchParams(window.location.search);\n        const success = urlParams.get(\"success\");\n        const error = urlParams.get(\"error\");\n        if (success === \"connected\") {\n            alert(\"X account connected successfully!\");\n            loadXAccountStatus(); // Refresh the status\n            // Clean up URL\n            window.history.replaceState({}, \"\", \"/settings?tab=integrations\");\n        } else if (error) {\n            const errorMessages = {\n                \"store_failed\": \"Failed to save X account connection. Please try again.\",\n                \"auth_failed\": \"X authorization failed. Please try again.\",\n                \"invalid_token\": \"Invalid authorization token. Please try again.\"\n            };\n            alert(errorMessages[error] || \"An error occurred. Please try again.\");\n            // Clean up URL\n            window.history.replaceState({}, \"\", \"/settings?tab=integrations\");\n        }\n    }, []);\n    const loadSettings = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"agent_e_settings\").select(\"*\").eq(\"user_id\", user.id).single();\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error loading settings:\", error);\n                return;\n            }\n            if (data) {\n                setProjectName(data.project_name || \"My AI Project\");\n                setProjectDescription(data.project_description || \"\");\n                setTargetAudience(data.target_audience || \"\");\n                setBrandVoice(data.brand_voice || \"professional\");\n                setCustomPrompts(data.custom_prompts || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading settings:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveAgentESettings = async ()=>{\n        if (!user) return;\n        setSaving(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"agent_e_settings\").upsert({\n                user_id: user.id,\n                project_name: projectName,\n                project_description: projectDescription,\n                target_audience: targetAudience,\n                brand_voice: brandVoice,\n                custom_prompts: customPrompts\n            });\n            if (error) {\n                console.error(\"Error saving settings:\", error);\n                alert(\"Error saving settings. Please try again.\");\n            } else {\n                alert(\"Settings saved successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            alert(\"Error saving settings. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const saveProfileSettings = async ()=>{\n        if (!user) {\n            alert(\"You must be logged in to update your profile.\");\n            return;\n        }\n        setSaving(true);\n        try {\n            // Try to update the profile\n            const { error } = await updateProfile({\n                full_name: fullName,\n                avatar_url: avatarUrl || null\n            });\n            if (error) {\n                var _error_message;\n                console.error(\"Error updating profile:\", error);\n                // If the table doesn't exist, try to create it first\n                if (error.code === \"42P01\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('relation \"user_profiles\" does not exist'))) {\n                    alert(\"Database setup required. Please run the database schema first, then try again.\");\n                } else {\n                    alert(\"Error updating profile: \".concat(error.message || \"Please try again.\"));\n                }\n            } else {\n                alert(\"Profile updated successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            alert(\"Error updating profile. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const loadXAccountStatus = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/account-status?userId=\".concat(user.id));\n            const data = await response.json();\n            console.log(\"X Account Status Response:\", {\n                status: response.status,\n                connected: data.connected,\n                accountInfo: data.accountInfo,\n                error: data.error\n            });\n            if (response.ok && data.connected) {\n                setXAccountConnected(true);\n                setXAccountInfo(data.accountInfo);\n            } else {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n            }\n        } catch (error) {\n            console.error(\"Error loading X account status:\", error);\n            setXAccountConnected(false);\n            setXAccountInfo(null);\n        }\n    };\n    const handleConnectX = async ()=>{\n        if (!user) {\n            alert(\"Please log in first\");\n            return;\n        }\n        setConnectingX(true);\n        try {\n            const response = await fetch(\"/api/x/auth-url\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // Redirect directly to X authorization\n                window.location.href = data.authUrl;\n            } else {\n                alert(data.error || \"Failed to initiate X connection\");\n                setConnectingX(false);\n            }\n        } catch (error) {\n            console.error(\"Error connecting to X:\", error);\n            alert(\"Failed to connect to X. Please try again.\");\n            setConnectingX(false);\n        }\n    };\n    const handleDisconnectX = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/disconnect\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            if (response.ok) {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n                alert(\"X account disconnected successfully\");\n            } else {\n                const data = await response.json();\n                alert(data.error || \"Failed to disconnect X account\");\n            }\n        } catch (error) {\n            console.error(\"Error disconnecting X:\", error);\n            alert(\"Failed to disconnect X account. Please try again.\");\n        }\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        surface: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\"\n    };\n    const tabs = [\n        {\n            id: \"agent-e\",\n            label: \"Agent E\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot\n        },\n        {\n            id: \"account\",\n            label: \"Account\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User\n        },\n        {\n            id: \"integrations\",\n            label: \"Integrations\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap\n        },\n        {\n            id: \"notifications\",\n            label: \"Notifications\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bell\n        },\n        {\n            id: \"security\",\n            label: \"Security\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Shield\n        }\n    ];\n    const handleSavePrompt = (id, newPrompt)=>{\n        setCustomPrompts((prev)=>prev.map((p)=>p.id === id ? {\n                    ...p,\n                    prompt: newPrompt\n                } : p));\n    };\n    const addNewPrompt = ()=>{\n        const newId = Math.max(...customPrompts.map((p)=>p.id)) + 1;\n        setCustomPrompts((prev)=>[\n                ...prev,\n                {\n                    id: newId,\n                    name: \"New Prompt\",\n                    prompt: \"\"\n                }\n            ]);\n    };\n    const deletePrompt = (id)=>{\n        setCustomPrompts((prev)=>prev.filter((p)=>p.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.background\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"40px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"8px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        children: \"Settings\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            margin: 0,\n                            fontSize: \"16px\"\n                        },\n                        children: \"Configure Agent E and manage your account preferences\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    gap: \"8px\",\n                    marginBottom: \"40px\",\n                    borderBottom: \"1px solid \".concat(colors.border),\n                    paddingBottom: \"0\"\n                },\n                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(tab.id),\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"8px\",\n                            padding: \"12px 20px\",\n                            background: activeTab === tab.id ? colors.surface : \"transparent\",\n                            border: activeTab === tab.id ? \"1px solid \".concat(colors.border) : \"1px solid transparent\",\n                            borderBottom: activeTab === tab.id ? \"1px solid \".concat(colors.surface) : \"1px solid transparent\",\n                            borderRadius: \"8px 8px 0 0\",\n                            fontSize: \"14px\",\n                            fontWeight: \"500\",\n                            color: activeTab === tab.id ? colors.text.primary : colors.text.secondary,\n                            cursor: \"pointer\",\n                            transition: \"all 0.2s ease\",\n                            marginBottom: \"-1px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, undefined),\n                            tab.label\n                        ]\n                    }, tab.id, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: colors.surface,\n                    borderRadius: \"12px\",\n                    padding: \"32px\",\n                    border: \"1px solid \".concat(colors.border),\n                    minHeight: \"500px\"\n                },\n                children: [\n                    activeTab === \"agent-e\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Agent E Configuration\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Project Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gridTemplateColumns: \"1fr 1fr\",\n                                            gap: \"20px\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: \"Project Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: projectName,\n                                                        onChange: (e)=>setProjectName(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: \"Brand Voice\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: brandVoice,\n                                                        onChange: (e)=>setBrandVoice(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"professional\",\n                                                                children: \"Professional\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"casual\",\n                                                                children: \"Casual & Friendly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"technical\",\n                                                                children: \"Technical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"creative\",\n                                                                children: \"Creative & Fun\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"authoritative\",\n                                                                children: \"Authoritative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"Project Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: projectDescription,\n                                                onChange: (e)=>setProjectDescription(e.target.value),\n                                                placeholder: \"Describe your project, product, or service so Agent E can create relevant content...\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    padding: \"12px 16px\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    background: colors.background,\n                                                    outline: \"none\",\n                                                    minHeight: \"100px\",\n                                                    resize: \"vertical\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"Target Audience\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: targetAudience,\n                                                onChange: (e)=>setTargetAudience(e.target.value),\n                                                placeholder: \"e.g., Tech entrepreneurs, Small business owners, Developers...\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    padding: \"12px 16px\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    background: colors.background,\n                                                    outline: \"none\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    margin: 0\n                                                },\n                                                children: \"Custom Prompts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewPrompt,\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"6px\",\n                                                    padding: \"8px 16px\",\n                                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    cursor: \"pointer\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Plus, {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Add Prompt\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    customPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                border: \"1px solid \".concat(colors.border),\n                                                borderRadius: \"8px\",\n                                                padding: \"16px\",\n                                                marginBottom: \"12px\",\n                                                background: colors.background\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        alignItems: \"center\",\n                                                        marginBottom: \"12px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: prompt.name,\n                                                            onChange: (e)=>{\n                                                                setCustomPrompts((prev)=>prev.map((p)=>p.id === prompt.id ? {\n                                                                            ...p,\n                                                                            name: e.target.value\n                                                                        } : p));\n                                                            },\n                                                            style: {\n                                                                background: \"transparent\",\n                                                                border: \"none\",\n                                                                fontSize: \"16px\",\n                                                                fontWeight: \"600\",\n                                                                color: colors.text.primary,\n                                                                outline: \"none\",\n                                                                flex: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deletePrompt(prompt.id),\n                                                            style: {\n                                                                background: \"none\",\n                                                                border: \"none\",\n                                                                color: colors.text.tertiary,\n                                                                cursor: \"pointer\",\n                                                                padding: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Trash2, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: prompt.prompt,\n                                                    onChange: (e)=>handleSavePrompt(prompt.id, e.target.value),\n                                                    placeholder: \"Enter your custom prompt for Agent E...\",\n                                                    style: {\n                                                        width: \"100%\",\n                                                        padding: \"12px\",\n                                                        border: \"1px solid \".concat(colors.border),\n                                                        borderRadius: \"6px\",\n                                                        fontSize: \"14px\",\n                                                        background: colors.surface,\n                                                        outline: \"none\",\n                                                        minHeight: \"80px\",\n                                                        resize: \"vertical\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, prompt.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveAgentESettings,\n                                disabled: saving || !user,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 24px\",\n                                    background: saving || !user ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                    boxShadow: saving || !user ? \"none\" : \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                    opacity: saving || !user ? 0.6 : 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Save, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    saving ? \"Saving...\" : \"Save Agent E Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab === \"integrations\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Integrations\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 635,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"12px\",\n                                    padding: \"24px\",\n                                    marginBottom: \"24px\",\n                                    background: colors.background\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                size: 24,\n                                                color: colors.primary\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    margin: 0\n                                                },\n                                                children: \"X (Twitter) Account\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: \"Connect your X account to enable automated posting and content scheduling through our premium service.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    xAccountConnected && xAccountInfo ? // Connected State\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            padding: \"16px\",\n                                            background: \"#F0FDF4\",\n                                            border: \"1px solid #BBF7D0\",\n                                            borderRadius: \"8px\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\"\n                                                },\n                                                children: [\n                                                    xAccountInfo.profile_image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: xAccountInfo.profile_image_url,\n                                                        alt: \"Profile\",\n                                                        style: {\n                                                            width: \"40px\",\n                                                            height: \"40px\",\n                                                            borderRadius: \"50%\",\n                                                            objectFit: \"cover\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: \"#065F46\",\n                                                                    fontSize: \"16px\",\n                                                                    fontWeight: \"600\"\n                                                                },\n                                                                children: xAccountInfo.name || \"Connected Account\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: \"#047857\",\n                                                                    fontSize: \"14px\"\n                                                                },\n                                                                children: [\n                                                                    \"@\",\n                                                                    xAccountInfo.username\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    padding: \"6px 12px\",\n                                                    background: \"#10B981\",\n                                                    color: \"white\",\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"Connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 17\n                                    }, undefined) : // Not Connected State\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px\",\n                                            background: \"#FEF3C7\",\n                                            border: \"1px solid #FDE68A\",\n                                            borderRadius: \"8px\",\n                                            marginBottom: \"16px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"#92400E\",\n                                                    fontSize: \"14px\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"No X account connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 743,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"#B45309\",\n                                                    fontSize: \"12px\"\n                                                },\n                                                children: \"Connect your account to start scheduling posts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            gap: \"12px\"\n                                        },\n                                        children: xAccountConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDisconnectX,\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"10px 20px\",\n                                                background: \"#FEE2E2\",\n                                                color: \"#DC2626\",\n                                                border: \"1px solid #FECACA\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                cursor: \"pointer\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Disconnect Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                console.log(\"Connect X button clicked\");\n                                                handleConnectX();\n                                            },\n                                            disabled: connectingX,\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"12px 24px\",\n                                                background: connectingX ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                color: \"white\",\n                                                border: \"none\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                cursor: connectingX ? \"not-allowed\" : \"pointer\",\n                                                opacity: connectingX ? 0.6 : 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 802,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                connectingX ? \"Connecting...\" : \"Connect X Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 634,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab === \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Account Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 813,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Profile Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gap: \"16px\",\n                                            maxWidth: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Full Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: fullName,\n                                                        onChange: (e)=>setFullName(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 838,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Email Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 865,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        disabled: true,\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: \"#F9F9F9\",\n                                                            color: colors.text.secondary,\n                                                            cursor: \"not-allowed\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 874,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Avatar URL (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"url\",\n                                                        value: avatarUrl,\n                                                        onChange: (e)=>setAvatarUrl(e.target.value),\n                                                        placeholder: \"https://example.com/your-avatar.jpg\",\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            fontSize: \"12px\",\n                                                            color: colors.text.tertiary,\n                                                            marginTop: \"4px\",\n                                                            marginBottom: \"0\"\n                                                        },\n                                                        children: \"Enter a URL to your profile picture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 837,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 827,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \"15 0%, \").concat(colors.primaryLight, \"15 100%)\"),\n                                            border: \"1px solid \".concat(colors.primary, \"30\"),\n                                            borderRadius: \"12px\",\n                                            padding: \"20px\",\n                                            maxWidth: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\",\n                                                    marginBottom: \"12px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"32px\",\n                                                            height: \"32px\",\n                                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                            borderRadius: \"8px\",\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Crown, {\n                                                            size: 16,\n                                                            color: \"white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 952,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: colors.text.primary,\n                                                                    fontSize: \"16px\",\n                                                                    fontWeight: \"600\"\n                                                                },\n                                                                children: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro Plan\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise Plan\" : \"Free Plan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 964,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: colors.text.secondary,\n                                                                    fontSize: \"14px\"\n                                                                },\n                                                                children: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"free\" ? \"No subscription\" : (profile === null || profile === void 0 ? void 0 : profile.subscription_status) === \"active\" ? \"$29/month • Active\" : \"Subscription inactive\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 971,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 963,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 946,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    gap: \"12px\",\n                                                    marginTop: \"16px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"8px 16px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        children: \"Manage Billing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"8px 16px\",\n                                                            background: \"transparent\",\n                                                            color: colors.text.secondary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        children: \"Cancel Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 997,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 939,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 929,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveProfileSettings,\n                                disabled: saving || !user,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 24px\",\n                                    background: saving || !user ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                    boxShadow: saving || !user ? \"none\" : \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                    opacity: saving || !user ? 0.6 : 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Save, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 1033,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    saving ? \"Saving...\" : \"Save Account Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1014,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 812,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab !== \"agent-e\" && activeTab !== \"integrations\" && activeTab !== \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"60px 20px\",\n                            color: colors.text.secondary\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginBottom: \"12px\"\n                                },\n                                children: \"Coming Soon\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1046,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"This section is under development.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1047,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 1041,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, undefined),\n            showPinModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: \"rgba(0, 0, 0, 0.5)\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: colors.surface,\n                        borderRadius: \"16px\",\n                        padding: \"32px\",\n                        width: \"90%\",\n                        maxWidth: \"400px\",\n                        textAlign: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                color: colors.text.primary,\n                                fontSize: \"20px\",\n                                fontWeight: \"600\",\n                                marginBottom: \"16px\"\n                            },\n                            children: \"\\uD83D\\uDD17 Connect Your X Account\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1074,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#F0F9FF\",\n                                border: \"1px solid #BAE6FD\",\n                                borderRadius: \"8px\",\n                                padding: \"16px\",\n                                marginBottom: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: \"#0369A1\",\n                                        fontSize: \"14px\",\n                                        marginBottom: \"12px\",\n                                        fontWeight: \"600\"\n                                    },\n                                    children: \"\\uD83D\\uDCCB Instructions:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1090,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    style: {\n                                        color: \"#0369A1\",\n                                        fontSize: \"13px\",\n                                        lineHeight: \"1.5\",\n                                        margin: 0,\n                                        paddingLeft: \"20px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"A new tab will open with X authorization page\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1105,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Click \"Authorize app\" on the X page'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1106,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Copy the PIN code shown on X\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1107,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Come back to this page\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 1108,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \" and enter the PIN below\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1108,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Click \"Connect Account\" to finish'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1109,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1098,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#FEF3C7\",\n                                        border: \"1px solid #FDE68A\",\n                                        borderRadius: \"6px\",\n                                        padding: \"8px 12px\",\n                                        marginTop: \"12px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: \"#92400E\",\n                                            fontSize: \"12px\",\n                                            margin: 0,\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"\\uD83D\\uDCA1 This modal will stay open even if you refresh the page!\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 1119,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1112,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1083,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: colors.text.secondary,\n                                fontSize: \"14px\",\n                                marginBottom: \"20px\",\n                                textAlign: \"center\",\n                                fontWeight: \"600\"\n                            },\n                            children: \"Enter the PIN code from X:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1130,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: pin,\n                            onChange: (e)=>setPin(e.target.value),\n                            placeholder: \"Enter PIN code\",\n                            style: {\n                                width: \"100%\",\n                                padding: \"12px 16px\",\n                                border: \"1px solid \".concat(colors.border),\n                                borderRadius: \"8px\",\n                                fontSize: \"16px\",\n                                textAlign: \"center\",\n                                marginBottom: \"24px\",\n                                outline: \"none\"\n                            },\n                            maxLength: 7\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1140,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                gap: \"8px\",\n                                justifyContent: \"center\",\n                                flexWrap: \"wrap\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        // Clear localStorage tokens\n                                        localStorage.removeItem(\"pending_x_oauth_token\");\n                                        localStorage.removeItem(\"pending_x_oauth_secret\");\n                                        // Clear state\n                                        setShowPinModal(false);\n                                        setPin(\"\");\n                                        setOauthToken(\"\");\n                                        setOauthTokenSecret(\"\");\n                                    },\n                                    style: {\n                                        padding: \"12px 20px\",\n                                        background: colors.background,\n                                        color: colors.text.primary,\n                                        border: \"1px solid \".concat(colors.border),\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1159,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        // Re-open X authorization page\n                                        const authUrl = \"https://api.twitter.com/oauth/authorize?oauth_token=\".concat(oauthToken);\n                                        window.open(authUrl, \"_blank\");\n                                    },\n                                    disabled: !oauthToken,\n                                    style: {\n                                        padding: \"12px 20px\",\n                                        background: \"#1DA1F2\",\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: \"Open X Page\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1185,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleVerifyPin,\n                                    disabled: !pin.trim() || verifyingPin,\n                                    style: {\n                                        padding: \"12px 24px\",\n                                        background: !pin.trim() || verifyingPin ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        cursor: !pin.trim() || verifyingPin ? \"not-allowed\" : \"pointer\"\n                                    },\n                                    children: verifyingPin ? \"Verifying...\" : \"Connect Account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1206,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1158,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                    lineNumber: 1066,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 1054,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 291,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SettingsPage, \"yIA732Jes0SuxAsQsMw7aGMWO2k=\", false, function() {\n    return [\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser\n    ];\n});\n_c = SettingsPage;\nSettingsPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 1234,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/settings.tsx\n"));

/***/ })

});