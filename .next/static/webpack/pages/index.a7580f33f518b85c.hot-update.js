"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "__barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: function() { return /* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   Calendar: function() { return /* reexport safe */ _icons_calendar_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   Home: function() { return /* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   LogIn: function() { return /* reexport safe */ _icons_log_in_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   MessageCircle: function() { return /* reexport safe */ _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; },\n/* harmony export */   Search: function() { return /* reexport safe */ _icons_search_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]; },\n/* harmony export */   User: function() { return /* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]; },\n/* harmony export */   Video: function() { return /* reexport safe */ _icons_video_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_calendar_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/calendar.js */ \"./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/house.js */ \"./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _icons_log_in_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/log-in.js */ \"./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/message-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _icons_search_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/search.js */ \"./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_video_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/video.js */ \"./node_modules/lucide-react/dist/esm/icons/video.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ2FsZW5kYXIsSG9tZSxMb2dJbixNZXNzYWdlQ2lyY2xlLFNlYXJjaCxVc2VyLFZpZGVvIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM2RDtBQUNKO0FBQ1A7QUFDRTtBQUNnQjtBQUNmO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/ZjIwNiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQmFyQ2hhcnQzIH0gZnJvbSBcIi4vaWNvbnMvYmFyLWNoYXJ0LTMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYWxlbmRhciB9IGZyb20gXCIuL2ljb25zL2NhbGVuZGFyLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSG9tZSB9IGZyb20gXCIuL2ljb25zL2hvdXNlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9nSW4gfSBmcm9tIFwiLi9pY29ucy9sb2ctaW4uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNZXNzYWdlQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvbWVzc2FnZS1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZWFyY2ggfSBmcm9tIFwiLi9pY29ucy9zZWFyY2guanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyIH0gZnJvbSBcIi4vaWNvbnMvdXNlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFZpZGVvIH0gZnJvbSBcIi4vaWNvbnMvdmlkZW8uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ }),

/***/ "./components/SidebarLayoutSimple.tsx":
/*!********************************************!*\
  !*** ./components/SidebarLayoutSimple.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthModal */ \"./components/AuthModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    var _user_user_metadata;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, profile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Fallback user data for when not authenticated or loading\n    const userData = {\n        name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"Alex Chen\",\n        email: (user === null || user === void 0 ? void 0 : user.email) || \"<EMAIL>\",\n        plan: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise\" : \"Free\",\n        avatar: (profile === null || profile === void 0 ? void 0 : profile.avatar_url) || null,\n        isOnline: (profile === null || profile === void 0 ? void 0 : profile.is_online) || false\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        background: \"#F5F1EB\",\n        surface: \"#FFFFFF\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            surface: \"rgba(255, 107, 53, 0.05)\",\n            text: \"#2D1B14\",\n            textSecondary: \"#5D4037\",\n            textMuted: \"#8D6E63\",\n            accent: \"#FF6B35\",\n            accentSoft: \"#FF8A65\",\n            accentLight: \"#FFF7F4\",\n            border: \"rgba(255, 107, 53, 0.15)\",\n            hover: \"rgba(255, 107, 53, 0.08)\",\n            divider: \"rgba(255, 107, 53, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.25)\" // Orange glow\n        }\n    };\n    const menuItems = [\n        {\n            section: \"WORKSPACE\",\n            items: [\n                {\n                    href: \"/\",\n                    label: \"Briefing Room\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home\n                },\n                {\n                    href: \"/tweet-center\",\n                    label: \"Drafting Desk\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageCircle\n                },\n                {\n                    href: \"/schedule\",\n                    label: \"Content Scheduler\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Calendar\n                },\n                {\n                    href: \"/dashboard\",\n                    label: \"Growth Lab\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.BarChart3\n                },\n                {\n                    href: \"/meeting\",\n                    label: \"AI Meetings\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Video\n                }\n            ]\n        },\n        {\n            section: \"SETTINGS\",\n            items: [\n                {\n                    href: \"/settings\",\n                    label: \"Settings\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User\n                }\n            ]\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: colors.background,\n            padding: \"16px\",\n            gap: \"16px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    background: colors.sidebar.background,\n                    height: \"calc(100vh - 32px)\",\n                    borderRadius: \"24px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: \"0 8px 32px \".concat(colors.sidebar.glow, \", 0 2px 8px rgba(255, 107, 53, 0.1)\"),\n                    border: \"1px solid \".concat(colors.sidebar.border),\n                    overflow: \"hidden\",\n                    position: \"relative\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"24px 16px 20px 20px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"10px\",\n                            background: colors.sidebar.background\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"https://nlckamsrdiwkyyrxzntf.supabase.co/storage/v1/object/sign/logos/logoxe.png?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InN0b3JhZ2UtdXJsLXNpZ25pbmcta2V5X2RiNTE0YzE5LTlhNTQtNGZiNy1hMjY3LTJmNjY5ZDlhZjY1OCJ9.eyJ1cmwiOiJsb2dvcy9sb2dveGUucG5nIiwiaWF0IjoxNzQ4MjMxNDM1LCJleHAiOjE3NTA4MjM0MzV9.dw1yy3hjXvMy02IhFMKGw_-evgbmyYDuJ4m6HPP1Uec\",\n                                alt: \"Exie Logo\",\n                                style: {\n                                    height: \"28px\",\n                                    width: \"auto\",\n                                    objectFit: \"contain\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontFamily: \"Anek Tamil, sans-serif\",\n                                    fontSize: \"22px\",\n                                    fontWeight: \"600\",\n                                    color: colors.sidebar.text,\n                                    letterSpacing: \"-0.5px\",\n                                    lineHeight: \"1\"\n                                },\n                                children: \"Exie\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"0 16px 16px 16px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                display: \"flex\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Search, {\n                                    size: 14,\n                                    color: colors.sidebar.textMuted,\n                                    style: {\n                                        position: \"absolute\",\n                                        left: \"12px\",\n                                        zIndex: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search...\",\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"8px 12px 8px 36px\",\n                                        border: \"1px solid \".concat(colors.sidebar.border),\n                                        borderRadius: \"8px\",\n                                        background: colors.sidebar.surface,\n                                        color: colors.sidebar.text,\n                                        fontSize: \"12px\",\n                                        fontWeight: \"400\",\n                                        outline: \"none\",\n                                        transition: \"all 0.2s ease\"\n                                    },\n                                    onFocus: (e)=>{\n                                        e.target.style.borderColor = colors.sidebar.accent;\n                                        e.target.style.background = colors.sidebar.background;\n                                    },\n                                    onBlur: (e)=>{\n                                        e.target.style.borderColor = colors.sidebar.border;\n                                        e.target.style.background = colors.sidebar.surface;\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"8px 16px\",\n                            flex: 1,\n                            display: \"flex\",\n                            flexDirection: \"column\"\n                        },\n                        children: menuItems.map((section, sectionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: sectionIndex < menuItems.length - 1 ? \"24px\" : \"0\"\n                                },\n                                children: [\n                                    sectionIndex > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            height: \"1px\",\n                                            background: \"linear-gradient(90deg, transparent, \".concat(colors.sidebar.divider, \", transparent)\"),\n                                            margin: \"16px 12px 20px 12px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"10px\",\n                                            fontWeight: \"600\",\n                                            color: colors.sidebar.textMuted,\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"1px\",\n                                            marginBottom: \"10px\",\n                                            paddingLeft: \"12px\"\n                                        },\n                                        children: section.section\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\",\n                                            gap: \"2px\"\n                                        },\n                                        children: section.items.map((item)=>{\n                                            const active = isActive(item.href);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        padding: \"8px 12px\",\n                                                        borderRadius: \"8px\",\n                                                        background: active ? colors.sidebar.surface : \"transparent\",\n                                                        cursor: \"pointer\",\n                                                        transition: \"all 0.2s ease\",\n                                                        position: \"relative\"\n                                                    },\n                                                    onMouseEnter: (e)=>{\n                                                        if (!active) {\n                                                            e.currentTarget.style.background = colors.sidebar.hover;\n                                                        }\n                                                    },\n                                                    onMouseLeave: (e)=>{\n                                                        if (!active) {\n                                                            e.currentTarget.style.background = \"transparent\";\n                                                        }\n                                                    },\n                                                    children: [\n                                                        active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: \"absolute\",\n                                                                left: \"0\",\n                                                                top: \"50%\",\n                                                                transform: \"translateY(-50%)\",\n                                                                width: \"2px\",\n                                                                height: \"16px\",\n                                                                background: colors.sidebar.accent,\n                                                                borderRadius: \"0 2px 2px 0\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: \"16px\",\n                                                                height: \"16px\",\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                justifyContent: \"center\",\n                                                                marginRight: \"10px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 14,\n                                                                color: active ? colors.sidebar.accent : colors.sidebar.textSecondary,\n                                                                strokeWidth: 2\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                                fontSize: \"13px\",\n                                                                fontWeight: active ? \"500\" : \"400\",\n                                                                letterSpacing: \"0.1px\"\n                                                            },\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, item.href, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, section.section, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            margin: \"0 16px 16px 16px\",\n                            height: \"1px\",\n                            background: \"linear-gradient(90deg, transparent, \".concat(colors.sidebar.divider, \", transparent)\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"8px 16px 16px 16px\"\n                        },\n                        children: user ? // Authenticated user - show profile\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"10px\",\n                                    padding: \"10px 12px\",\n                                    background: colors.sidebar.background,\n                                    borderRadius: \"14px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                    border: \"1px solid \".concat(colors.sidebar.border),\n                                    boxShadow: \"0 2px 8px \".concat(colors.sidebar.glow)\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.surface;\n                                    e.currentTarget.style.borderColor = colors.sidebar.accent + \"40\";\n                                    e.currentTarget.style.transform = \"translateY(-1px)\";\n                                    e.currentTarget.style.boxShadow = \"0 4px 16px \".concat(colors.sidebar.glow);\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.background;\n                                    e.currentTarget.style.borderColor = colors.sidebar.border;\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                    e.currentTarget.style.boxShadow = \"0 2px 8px \".concat(colors.sidebar.glow);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: userData.avatar ? \"transparent\" : \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\"),\n                                            borderRadius: \"10px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            overflow: \"hidden\",\n                                            border: \"2px solid \".concat(colors.sidebar.border)\n                                        },\n                                        children: [\n                                            userData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userData.avatar,\n                                                alt: userData.name,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"8px\"\n                                                },\n                                                onError: (e)=>{\n                                                    // Fallback to icon if image fails to load\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.parentElement.style.background = \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\");\n                                                    e.currentTarget.parentElement.innerHTML = '<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path><circle cx=\"12\" cy=\"7\" r=\"4\"></circle></svg>';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 16,\n                                                color: \"#FFFFFF\",\n                                                strokeWidth: 2\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            userData.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"10px\",\n                                                    height: \"10px\",\n                                                    background: \"#00FF88\",\n                                                    borderRadius: \"50%\",\n                                                    border: \"2px solid #FFFFFF\",\n                                                    boxShadow: \"0 0 6px rgba(0, 255, 136, 0.8)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    lineHeight: \"1.2\",\n                                                    marginBottom: \"2px\"\n                                                },\n                                                children: userData.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.textMuted,\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"500\",\n                                                    lineHeight: \"1.2\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            userData.plan,\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: colors.sidebar.accent\n                                                        },\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, undefined) : // Not authenticated - show sign in button\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>setShowAuthModal(true),\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                padding: \"8px 12px\",\n                                borderRadius: \"8px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\",\n                                background: \"transparent\"\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.background = colors.sidebar.hover;\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.background = \"transparent\";\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"16px\",\n                                        height: \"16px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        marginRight: \"10px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogIn, {\n                                        size: 14,\n                                        color: colors.sidebar.textSecondary,\n                                        strokeWidth: 2\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: colors.sidebar.textSecondary,\n                                        fontSize: \"13px\",\n                                        fontWeight: \"400\",\n                                        letterSpacing: \"0.1px\"\n                                    },\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.surface,\n                    borderRadius: \"24px\",\n                    height: \"calc(100vh - 32px)\",\n                    position: \"relative\",\n                    boxShadow: \"0 4px 20px rgba(255, 107, 53, 0.08)\",\n                    border: \"1px solid \".concat(colors.sidebar.border),\n                    overflow: \"hidden\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"32px\",\n                        height: \"100%\",\n                        overflow: \"auto\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 460,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"2fxYphseme7UlXU7fqnQRjVv+Ys=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayoutSimple.tsx\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/calendar.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/calendar.js ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Calendar; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.400.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Calendar = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Calendar\", [\n    [\n        \"path\",\n        {\n            d: \"M8 2v4\",\n            key: \"1cmpym\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 2v4\",\n            key: \"4m81vk\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"18\",\n            height: \"18\",\n            x: \"3\",\n            y: \"4\",\n            rx: \"2\",\n            key: \"1hopcy\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 10h18\",\n            key: \"8toen8\"\n        }\n    ]\n]);\n //# sourceMappingURL=calendar.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/calendar.js\n"));

/***/ })

});