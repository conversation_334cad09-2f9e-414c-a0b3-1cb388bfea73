"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @supabase/auth-helpers-react */ \"./node_modules/@supabase/auth-helpers-react/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,TrendingUp,Users,Zap!=!lucide-react */ \"__barrel_optimize__?names=Calendar,TrendingUp,Users,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n// pages/index.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst HomePage = ()=>{\n    _s();\n    const user = (0,_supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        upcomingTweets: 0,\n        followers: 0,\n        engagementRate: 0,\n        contentScore: 0\n    });\n    const [upcomingPosts, setUpcomingPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\"\n        },\n        border: \"#E5E7EB\",\n        surface: \"#FFFFFF\",\n        background: \"#FFF8F3\"\n    };\n    // Load dashboard data\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (user === null || user === void 0 ? void 0 : user.id) {\n            loadDashboardData();\n        } else {\n            // Show demo data for non-authenticated users\n            setStats({\n                upcomingTweets: 3,\n                followers: 847,\n                engagementRate: 24,\n                contentScore: 8.7\n            });\n            setSuggestions([\n                \"Share a behind-the-scenes story about your workflow\",\n                \"Create a thread about productivity tips\",\n                \"Post about recent industry trends\",\n                \"Engage with your community through polls\"\n            ]);\n            setLoading(false);\n        }\n    }, [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    const loadDashboardData = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.id)) return;\n        console.log(\"Loading dashboard data for user:\", user.id);\n        try {\n            // Load scheduled posts\n            console.log(\"Fetching scheduled posts...\");\n            const scheduledResponse = await fetch(\"/api/twitter/scheduled?userId=\".concat(user.id));\n            console.log(\"Scheduled response status:\", scheduledResponse.status);\n            if (scheduledResponse.ok) {\n                const scheduledData = await scheduledResponse.json();\n                console.log(\"Scheduled data:\", scheduledData);\n                setUpcomingPosts(scheduledData.scheduledPosts || []);\n                setStats((prev)=>{\n                    var _scheduledData_scheduledPosts;\n                    return {\n                        ...prev,\n                        upcomingTweets: ((_scheduledData_scheduledPosts = scheduledData.scheduledPosts) === null || _scheduledData_scheduledPosts === void 0 ? void 0 : _scheduledData_scheduledPosts.length) || 0\n                    };\n                });\n            } else {\n                console.error(\"Failed to fetch scheduled posts:\", await scheduledResponse.text());\n            }\n            // Load posted content for analytics\n            console.log(\"Fetching posted content...\");\n            const postedResponse = await fetch(\"/api/twitter/posted?userId=\".concat(user.id));\n            console.log(\"Posted response status:\", postedResponse.status);\n            if (postedResponse.ok) {\n                const postedData = await postedResponse.json();\n                console.log(\"Posted data:\", postedData);\n                const posts = postedData.postedContent || [];\n                // Calculate engagement rate (mock calculation)\n                const engagementRate = posts.length > 0 ? Math.floor(Math.random() * 30) + 15 : 15;\n                const contentScore = posts.length > 0 ? (Math.random() * 2 + 8).toFixed(1) : \"8.5\";\n                setStats((prev)=>({\n                        ...prev,\n                        followers: Math.floor(Math.random() * 1000) + 500,\n                        engagementRate,\n                        contentScore: parseFloat(contentScore)\n                    }));\n            } else {\n                console.error(\"Failed to fetch posted content:\", await postedResponse.text());\n                // Set default stats even if API fails\n                setStats((prev)=>({\n                        ...prev,\n                        followers: 847,\n                        engagementRate: 24,\n                        contentScore: 8.7\n                    }));\n            }\n            // Generate AI suggestions\n            setSuggestions([\n                \"Share a behind-the-scenes story about your workflow\",\n                \"Create a thread about productivity tips\",\n                \"Post about recent industry trends\",\n                \"Engage with your community through polls\"\n            ]);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            // Set default stats on error\n            setStats((prev)=>({\n                    ...prev,\n                    followers: 847,\n                    engagementRate: 24,\n                    contentScore: 8.7\n                }));\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"32px\",\n            height: \"100vh\",\n            overflow: \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"32px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.5px\",\n                            marginBottom: \"8px\"\n                        },\n                        children: \"Briefing Room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            fontSize: \"16px\",\n                            margin: 0,\n                            fontWeight: \"400\"\n                        },\n                        children: \"Your daily mission control center\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: \"800px\",\n                    margin: \"0 auto\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            marginBottom: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"16px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        marginBottom: \"12px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                color: colors.text.primary,\n                                                margin: 0,\n                                                fontSize: \"20px\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"Today's Mission\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"4px 8px\",\n                                                background: \"\".concat(colors.primary, \"15\"),\n                                                borderRadius: \"6px\",\n                                                color: colors.primary,\n                                                fontSize: \"11px\",\n                                                fontWeight: \"600\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.5px\"\n                                            },\n                                            children: \"AI Generated\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: colors.text.secondary,\n                                        fontSize: \"16px\",\n                                        lineHeight: \"1.5\",\n                                        margin: 0,\n                                        marginBottom: \"20px\"\n                                    },\n                                    children: \"Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"grid\",\n                                        gridTemplateColumns: \"repeat(4, 1fr)\",\n                                        gap: \"16px\",\n                                        marginBottom: \"20px\"\n                                    },\n                                    children: [\n                                        {\n                                            label: \"Upcoming Tweets\",\n                                            value: loading ? \"...\" : stats.upcomingTweets.toString(),\n                                            icon: _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Calendar,\n                                            color: colors.primary\n                                        },\n                                        {\n                                            label: \"Followers\",\n                                            value: loading ? \"...\" : stats.followers.toLocaleString(),\n                                            icon: _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Users,\n                                            color: \"#10B981\"\n                                        },\n                                        {\n                                            label: \"Engagement Rate\",\n                                            value: loading ? \"...\" : \"\".concat(stats.engagementRate, \"%\"),\n                                            icon: _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.TrendingUp,\n                                            color: \"#3B82F6\"\n                                        },\n                                        {\n                                            label: \"Content Score\",\n                                            value: loading ? \"...\" : \"\".concat(stats.contentScore, \"/10\"),\n                                            icon: _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap,\n                                            color: \"#8B5CF6\"\n                                        }\n                                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: colors.surface,\n                                                borderRadius: \"12px\",\n                                                padding: \"20px\",\n                                                textAlign: \"center\",\n                                                border: \"1px solid \".concat(colors.border),\n                                                boxShadow: \"0 2px 8px rgba(0, 0, 0, 0.04)\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"center\",\n                                                        marginBottom: \"8px\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                        size: 20,\n                                                        color: stat.color\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"700\",\n                                                        color: colors.text.primary,\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.text.tertiary,\n                                                        fontWeight: \"500\"\n                                                    },\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        gap: \"12px\",\n                                        flexWrap: \"wrap\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/meeting\",\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"12px 20px\",\n                                                    background: colors.primary,\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Join Call\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                padding: \"12px 20px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                border: \"1px solid \".concat(colors.border),\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\"\n                                            },\n                                            children: \"Ask Mentor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/tweet-center\",\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"12px 20px\",\n                                                    background: colors.surface,\n                                                    color: colors.text.primary,\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Create Content\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined),\n                    upcomingPosts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            marginBottom: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    marginBottom: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Calendar, {\n                                        size: 20,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            margin: 0,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"Upcoming Tweets\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"12px\"\n                                },\n                                children: upcomingPosts.slice(0, 3).map((post, index)=>{\n                                    var _post_content;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px\",\n                                            background: colors.background,\n                                            borderRadius: \"12px\",\n                                            border: \"1px solid \".concat(colors.border)\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                justifyContent: \"space-between\",\n                                                alignItems: \"flex-start\",\n                                                marginBottom: \"8px\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: colors.text.primary,\n                                                        lineHeight: \"1.4\",\n                                                        flex: 1\n                                                    },\n                                                    children: [\n                                                        (_post_content = post.content) === null || _post_content === void 0 ? void 0 : _post_content.substring(0, 100),\n                                                        \"...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.text.tertiary,\n                                                        marginLeft: \"12px\",\n                                                        whiteSpace: \"nowrap\"\n                                                    },\n                                                    children: new Date(post.scheduled_time).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            marginBottom: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    marginBottom: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap, {\n                                        size: 20,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            margin: 0,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"AI Suggestions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"12px\"\n                                },\n                                children: suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px\",\n                                            background: colors.background,\n                                            borderRadius: \"12px\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: \"8px\",\n                                                    height: \"8px\",\n                                                    borderRadius: \"50%\",\n                                                    background: colors.primary\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.primary,\n                                                    flex: 1\n                                                },\n                                                children: suggestion\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/tweet-center\",\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        padding: \"6px 12px\",\n                                                        background: colors.primary,\n                                                        color: \"white\",\n                                                        border: \"none\",\n                                                        borderRadius: \"6px\",\n                                                        fontSize: \"12px\",\n                                                        fontWeight: \"500\",\n                                                        cursor: \"pointer\"\n                                                    },\n                                                    children: \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"flex-start\",\n                                gap: \"16px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"48px\",\n                                        height: \"48px\",\n                                        borderRadius: \"12px\",\n                                        background: \"\".concat(colors.primary, \"15\"),\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\",\n                                        flexShrink: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"24px\",\n                                                height: \"24px\",\n                                                borderRadius: \"6px\",\n                                                background: colors.primary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"2px\",\n                                                right: \"2px\",\n                                                width: \"12px\",\n                                                height: \"12px\",\n                                                borderRadius: \"50%\",\n                                                background: \"#00E676\",\n                                                border: \"2px solid white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: colors.text.primary,\n                                                margin: 0,\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                marginBottom: \"8px\"\n                                            },\n                                            children: \"AI Mentor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: colors.text.secondary,\n                                                margin: 0,\n                                                fontSize: \"15px\",\n                                                lineHeight: \"1.5\"\n                                            },\n                                            children: \"Ready to help you create content that resonates. What's on your mind today?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HomePage, \"QgDVr8VjFc6+rV3us+rtn0eYZLg=\", false, function() {\n    return [\n        _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = HomePage;\nHomePage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 520,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});