"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/settings",{

/***/ "./pages/settings.tsx":
/*!****************************!*\
  !*** ./pages/settings.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!lucide-react */ \"__barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/supabase */ \"./lib/supabase.ts\");\n// pages/settings.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SettingsPage = ()=>{\n    _s();\n    const { user, profile, updateProfile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"agent-e\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Agent E Settings\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"My AI Project\");\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [targetAudience, setTargetAudience] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [brandVoice, setBrandVoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"professional\");\n    const [customPrompts, setCustomPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: \"Product Launch\",\n            prompt: \"Create engaging content for product launches with excitement and clear benefits\"\n        },\n        {\n            id: 2,\n            name: \"Educational\",\n            prompt: \"Write informative content that teaches and provides value to the audience\"\n        }\n    ]);\n    // Profile Settings\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [avatarUrl, setAvatarUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // X Integration Settings\n    const [xAccountConnected, setXAccountConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [xAccountInfo, setXAccountInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connectingX, setConnectingX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPinModal, setShowPinModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [oauthToken, setOauthToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [oauthTokenSecret, setOauthTokenSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [pin, setPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [verifyingPin, setVerifyingPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load settings from Supabase\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            var _user_user_metadata;\n            loadSettings();\n            loadXAccountStatus();\n            setFullName((profile === null || profile === void 0 ? void 0 : profile.full_name) || ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"\");\n            setEmail(user.email || \"\");\n            setAvatarUrl((profile === null || profile === void 0 ? void 0 : profile.avatar_url) || \"\");\n        }\n    }, [\n        user,\n        profile\n    ]);\n    const loadSettings = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"agent_e_settings\").select(\"*\").eq(\"user_id\", user.id).single();\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error loading settings:\", error);\n                return;\n            }\n            if (data) {\n                setProjectName(data.project_name || \"My AI Project\");\n                setProjectDescription(data.project_description || \"\");\n                setTargetAudience(data.target_audience || \"\");\n                setBrandVoice(data.brand_voice || \"professional\");\n                setCustomPrompts(data.custom_prompts || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading settings:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveAgentESettings = async ()=>{\n        if (!user) return;\n        setSaving(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"agent_e_settings\").upsert({\n                user_id: user.id,\n                project_name: projectName,\n                project_description: projectDescription,\n                target_audience: targetAudience,\n                brand_voice: brandVoice,\n                custom_prompts: customPrompts\n            });\n            if (error) {\n                console.error(\"Error saving settings:\", error);\n                alert(\"Error saving settings. Please try again.\");\n            } else {\n                alert(\"Settings saved successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            alert(\"Error saving settings. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const saveProfileSettings = async ()=>{\n        if (!user) {\n            alert(\"You must be logged in to update your profile.\");\n            return;\n        }\n        setSaving(true);\n        try {\n            console.log(\"Updating profile with:\", {\n                full_name: fullName\n            });\n            // Try to update the profile\n            const { error } = await updateProfile({\n                full_name: fullName,\n                avatar_url: avatarUrl || null\n            });\n            if (error) {\n                var _error_message;\n                console.error(\"Error updating profile:\", error);\n                // If the table doesn't exist, try to create it first\n                if (error.code === \"42P01\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('relation \"user_profiles\" does not exist'))) {\n                    alert(\"Database setup required. Please run the database schema first, then try again.\");\n                } else {\n                    alert(\"Error updating profile: \".concat(error.message || \"Please try again.\"));\n                }\n            } else {\n                alert(\"Profile updated successfully!\");\n                console.log(\"Profile updated successfully\");\n            }\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            alert(\"Error updating profile. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const loadXAccountStatus = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/account-status?userId=\".concat(user.id));\n            const data = await response.json();\n            if (response.ok && data.connected) {\n                setXAccountConnected(true);\n                setXAccountInfo(data.accountInfo);\n            } else {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n            }\n        } catch (error) {\n            console.error(\"Error loading X account status:\", error);\n            setXAccountConnected(false);\n            setXAccountInfo(null);\n        }\n    };\n    const handleConnectX = async ()=>{\n        console.log(\"handleConnectX called, user:\", user);\n        if (!user) {\n            console.log(\"No user found, returning\");\n            alert(\"Please log in first\");\n            return;\n        }\n        setConnectingX(true);\n        try {\n            const response = await fetch(\"/api/x/auth-url\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            console.log(\"API response:\", {\n                status: response.status,\n                data\n            });\n            if (response.ok) {\n                // Store the OAuth tokens and open the authorization URL\n                console.log(\"Setting OAuth tokens and opening modal\");\n                setOauthToken(data.oauth_token);\n                setOauthTokenSecret(data.oauth_token_secret);\n                window.open(data.authUrl, \"_blank\");\n                setShowPinModal(true);\n            } else {\n                console.log(\"API error:\", data);\n                alert(data.error || \"Failed to initiate X connection\");\n            }\n        } catch (error) {\n            console.error(\"Error connecting to X:\", error);\n            alert(\"Failed to connect to X. Please try again.\");\n        } finally{\n            setConnectingX(false);\n        }\n    };\n    const handleVerifyPin = async ()=>{\n        if (!user || !pin.trim() || !oauthToken || !oauthTokenSecret) return;\n        setVerifyingPin(true);\n        try {\n            const response = await fetch(\"/api/x/verify-pin\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    oauth_token: oauthToken,\n                    oauth_token_secret: oauthTokenSecret,\n                    pin: pin.trim(),\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                setXAccountConnected(true);\n                setXAccountInfo(data.accountInfo);\n                setShowPinModal(false);\n                setPin(\"\");\n                setOauthToken(\"\");\n                setOauthTokenSecret(\"\");\n                alert(\"X account connected successfully!\");\n            } else {\n                alert(data.error || \"Failed to verify PIN\");\n            }\n        } catch (error) {\n            console.error(\"Error verifying PIN:\", error);\n            alert(\"Failed to verify PIN. Please try again.\");\n        } finally{\n            setVerifyingPin(false);\n        }\n    };\n    const handleDisconnectX = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/disconnect\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            if (response.ok) {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n                alert(\"X account disconnected successfully\");\n            } else {\n                const data = await response.json();\n                alert(data.error || \"Failed to disconnect X account\");\n            }\n        } catch (error) {\n            console.error(\"Error disconnecting X:\", error);\n            alert(\"Failed to disconnect X account. Please try again.\");\n        }\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        surface: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\"\n    };\n    const tabs = [\n        {\n            id: \"agent-e\",\n            label: \"Agent E\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot\n        },\n        {\n            id: \"account\",\n            label: \"Account\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User\n        },\n        {\n            id: \"integrations\",\n            label: \"Integrations\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap\n        },\n        {\n            id: \"notifications\",\n            label: \"Notifications\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bell\n        },\n        {\n            id: \"security\",\n            label: \"Security\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Shield\n        }\n    ];\n    const handleSavePrompt = (id, newPrompt)=>{\n        setCustomPrompts((prev)=>prev.map((p)=>p.id === id ? {\n                    ...p,\n                    prompt: newPrompt\n                } : p));\n    };\n    const addNewPrompt = ()=>{\n        const newId = Math.max(...customPrompts.map((p)=>p.id)) + 1;\n        setCustomPrompts((prev)=>[\n                ...prev,\n                {\n                    id: newId,\n                    name: \"New Prompt\",\n                    prompt: \"\"\n                }\n            ]);\n    };\n    const deletePrompt = (id)=>{\n        setCustomPrompts((prev)=>prev.filter((p)=>p.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.background\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"40px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"8px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        children: \"Settings\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            margin: 0,\n                            fontSize: \"16px\"\n                        },\n                        children: \"Configure Agent E and manage your account preferences\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    gap: \"8px\",\n                    marginBottom: \"40px\",\n                    borderBottom: \"1px solid \".concat(colors.border),\n                    paddingBottom: \"0\"\n                },\n                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(tab.id),\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"8px\",\n                            padding: \"12px 20px\",\n                            background: activeTab === tab.id ? colors.surface : \"transparent\",\n                            border: activeTab === tab.id ? \"1px solid \".concat(colors.border) : \"1px solid transparent\",\n                            borderBottom: activeTab === tab.id ? \"1px solid \".concat(colors.surface) : \"1px solid transparent\",\n                            borderRadius: \"8px 8px 0 0\",\n                            fontSize: \"14px\",\n                            fontWeight: \"500\",\n                            color: activeTab === tab.id ? colors.text.primary : colors.text.secondary,\n                            cursor: \"pointer\",\n                            transition: \"all 0.2s ease\",\n                            marginBottom: \"-1px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, undefined),\n                            tab.label\n                        ]\n                    }, tab.id, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: colors.surface,\n                    borderRadius: \"12px\",\n                    padding: \"32px\",\n                    border: \"1px solid \".concat(colors.border),\n                    minHeight: \"500px\"\n                },\n                children: [\n                    activeTab === \"agent-e\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Agent E Configuration\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Project Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gridTemplateColumns: \"1fr 1fr\",\n                                            gap: \"20px\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: \"Project Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: projectName,\n                                                        onChange: (e)=>setProjectName(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: \"Brand Voice\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: brandVoice,\n                                                        onChange: (e)=>setBrandVoice(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"professional\",\n                                                                children: \"Professional\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"casual\",\n                                                                children: \"Casual & Friendly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"technical\",\n                                                                children: \"Technical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"creative\",\n                                                                children: \"Creative & Fun\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"authoritative\",\n                                                                children: \"Authoritative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"Project Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: projectDescription,\n                                                onChange: (e)=>setProjectDescription(e.target.value),\n                                                placeholder: \"Describe your project, product, or service so Agent E can create relevant content...\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    padding: \"12px 16px\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    background: colors.background,\n                                                    outline: \"none\",\n                                                    minHeight: \"100px\",\n                                                    resize: \"vertical\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"Target Audience\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: targetAudience,\n                                                onChange: (e)=>setTargetAudience(e.target.value),\n                                                placeholder: \"e.g., Tech entrepreneurs, Small business owners, Developers...\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    padding: \"12px 16px\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    background: colors.background,\n                                                    outline: \"none\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    margin: 0\n                                                },\n                                                children: \"Custom Prompts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewPrompt,\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"6px\",\n                                                    padding: \"8px 16px\",\n                                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    cursor: \"pointer\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Plus, {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Add Prompt\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    customPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                border: \"1px solid \".concat(colors.border),\n                                                borderRadius: \"8px\",\n                                                padding: \"16px\",\n                                                marginBottom: \"12px\",\n                                                background: colors.background\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        alignItems: \"center\",\n                                                        marginBottom: \"12px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: prompt.name,\n                                                            onChange: (e)=>{\n                                                                setCustomPrompts((prev)=>prev.map((p)=>p.id === prompt.id ? {\n                                                                            ...p,\n                                                                            name: e.target.value\n                                                                        } : p));\n                                                            },\n                                                            style: {\n                                                                background: \"transparent\",\n                                                                border: \"none\",\n                                                                fontSize: \"16px\",\n                                                                fontWeight: \"600\",\n                                                                color: colors.text.primary,\n                                                                outline: \"none\",\n                                                                flex: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deletePrompt(prompt.id),\n                                                            style: {\n                                                                background: \"none\",\n                                                                border: \"none\",\n                                                                color: colors.text.tertiary,\n                                                                cursor: \"pointer\",\n                                                                padding: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Trash2, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: prompt.prompt,\n                                                    onChange: (e)=>handleSavePrompt(prompt.id, e.target.value),\n                                                    placeholder: \"Enter your custom prompt for Agent E...\",\n                                                    style: {\n                                                        width: \"100%\",\n                                                        padding: \"12px\",\n                                                        border: \"1px solid \".concat(colors.border),\n                                                        borderRadius: \"6px\",\n                                                        fontSize: \"14px\",\n                                                        background: colors.surface,\n                                                        outline: \"none\",\n                                                        minHeight: \"80px\",\n                                                        resize: \"vertical\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, prompt.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveAgentESettings,\n                                disabled: saving || !user,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 24px\",\n                                    background: saving || !user ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                    boxShadow: saving || !user ? \"none\" : \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                    opacity: saving || !user ? 0.6 : 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Save, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    saving ? \"Saving...\" : \"Save Agent E Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab === \"integrations\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Integrations\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"12px\",\n                                    padding: \"24px\",\n                                    marginBottom: \"24px\",\n                                    background: colors.background\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                size: 24,\n                                                color: colors.primary\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    margin: 0\n                                                },\n                                                children: \"X (Twitter) Account\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: \"Connect your X account to enable automated posting and content scheduling through our premium service.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    xAccountConnected && xAccountInfo ? // Connected State\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            padding: \"16px\",\n                                            background: \"#F0FDF4\",\n                                            border: \"1px solid #BBF7D0\",\n                                            borderRadius: \"8px\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\"\n                                                },\n                                                children: [\n                                                    xAccountInfo.profile_image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: xAccountInfo.profile_image_url,\n                                                        alt: \"Profile\",\n                                                        style: {\n                                                            width: \"40px\",\n                                                            height: \"40px\",\n                                                            borderRadius: \"50%\",\n                                                            objectFit: \"cover\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: \"#065F46\",\n                                                                    fontSize: \"16px\",\n                                                                    fontWeight: \"600\"\n                                                                },\n                                                                children: xAccountInfo.name || \"Connected Account\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: \"#047857\",\n                                                                    fontSize: \"14px\"\n                                                                },\n                                                                children: [\n                                                                    \"@\",\n                                                                    xAccountInfo.username\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    padding: \"6px 12px\",\n                                                    background: \"#10B981\",\n                                                    color: \"white\",\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"Connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 706,\n                                        columnNumber: 17\n                                    }, undefined) : // Not Connected State\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px\",\n                                            background: \"#FEF3C7\",\n                                            border: \"1px solid #FDE68A\",\n                                            borderRadius: \"8px\",\n                                            marginBottom: \"16px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"#92400E\",\n                                                    fontSize: \"14px\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"No X account connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"#B45309\",\n                                                    fontSize: \"12px\"\n                                                },\n                                                children: \"Connect your account to start scheduling posts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 758,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            gap: \"12px\"\n                                        },\n                                        children: xAccountConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDisconnectX,\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"10px 20px\",\n                                                background: \"#FEE2E2\",\n                                                color: \"#DC2626\",\n                                                border: \"1px solid #FECACA\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                cursor: \"pointer\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Disconnect Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                console.log(\"Connect X button clicked\");\n                                                handleConnectX();\n                                            },\n                                            disabled: connectingX,\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"12px 24px\",\n                                                background: connectingX ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                color: \"white\",\n                                                border: \"none\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                cursor: connectingX ? \"not-allowed\" : \"pointer\",\n                                                opacity: connectingX ? 0.6 : 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 825,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                connectingX ? \"Connecting...\" : \"Connect X Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 804,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 657,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab === \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Account Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 836,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Profile Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gap: \"16px\",\n                                            maxWidth: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Full Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 862,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: fullName,\n                                                        onChange: (e)=>setFullName(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Email Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        disabled: true,\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: \"#F9F9F9\",\n                                                            color: colors.text.secondary,\n                                                            cursor: \"not-allowed\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 887,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Avatar URL (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"url\",\n                                                        value: avatarUrl,\n                                                        onChange: (e)=>setAvatarUrl(e.target.value),\n                                                        placeholder: \"https://example.com/your-avatar.jpg\",\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 924,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            fontSize: \"12px\",\n                                                            color: colors.text.tertiary,\n                                                            marginTop: \"4px\",\n                                                            marginBottom: \"0\"\n                                                        },\n                                                        children: \"Enter a URL to your profile picture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 939,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 850,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 953,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \"15 0%, \").concat(colors.primaryLight, \"15 100%)\"),\n                                            border: \"1px solid \".concat(colors.primary, \"30\"),\n                                            borderRadius: \"12px\",\n                                            padding: \"20px\",\n                                            maxWidth: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\",\n                                                    marginBottom: \"12px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"32px\",\n                                                            height: \"32px\",\n                                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                            borderRadius: \"8px\",\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Crown, {\n                                                            size: 16,\n                                                            color: \"white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 984,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 975,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: colors.text.primary,\n                                                                    fontSize: \"16px\",\n                                                                    fontWeight: \"600\"\n                                                                },\n                                                                children: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro Plan\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise Plan\" : \"Free Plan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 987,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: colors.text.secondary,\n                                                                    fontSize: \"14px\"\n                                                                },\n                                                                children: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"free\" ? \"No subscription\" : (profile === null || profile === void 0 ? void 0 : profile.subscription_status) === \"active\" ? \"$29/month • Active\" : \"Subscription inactive\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 994,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 986,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 969,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    gap: \"12px\",\n                                                    marginTop: \"16px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"8px 16px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        children: \"Manage Billing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"8px 16px\",\n                                                            background: \"transparent\",\n                                                            color: colors.text.secondary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        children: \"Cancel Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 1020,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 952,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveProfileSettings,\n                                disabled: saving || !user,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 24px\",\n                                    background: saving || !user ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                    boxShadow: saving || !user ? \"none\" : \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                    opacity: saving || !user ? 0.6 : 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Save, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 1056,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    saving ? \"Saving...\" : \"Save Account Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1037,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 835,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab !== \"agent-e\" && activeTab !== \"integrations\" && activeTab !== \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"60px 20px\",\n                            color: colors.text.secondary\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginBottom: \"12px\"\n                                },\n                                children: \"Coming Soon\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1069,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"This section is under development.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1070,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 1064,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 380,\n                columnNumber: 7\n            }, undefined),\n            showPinModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: \"rgba(0, 0, 0, 0.5)\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: colors.surface,\n                        borderRadius: \"16px\",\n                        padding: \"32px\",\n                        width: \"90%\",\n                        maxWidth: \"400px\",\n                        textAlign: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                color: colors.text.primary,\n                                fontSize: \"20px\",\n                                fontWeight: \"600\",\n                                marginBottom: \"16px\"\n                            },\n                            children: \"Enter PIN Code\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1097,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: colors.text.secondary,\n                                fontSize: \"14px\",\n                                marginBottom: \"24px\",\n                                lineHeight: \"1.5\"\n                            },\n                            children: \"After authorizing the app on X, you'll receive a PIN code. Enter it below to complete the connection.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1106,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: pin,\n                            onChange: (e)=>setPin(e.target.value),\n                            placeholder: \"Enter PIN code\",\n                            style: {\n                                width: \"100%\",\n                                padding: \"12px 16px\",\n                                border: \"1px solid \".concat(colors.border),\n                                borderRadius: \"8px\",\n                                fontSize: \"16px\",\n                                textAlign: \"center\",\n                                marginBottom: \"24px\",\n                                outline: \"none\"\n                            },\n                            maxLength: 7\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1115,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                gap: \"12px\",\n                                justifyContent: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowPinModal(false);\n                                        setPin(\"\");\n                                        setOauthToken(\"\");\n                                        setOauthTokenSecret(\"\");\n                                    },\n                                    style: {\n                                        padding: \"12px 24px\",\n                                        background: colors.background,\n                                        color: colors.text.primary,\n                                        border: \"1px solid \".concat(colors.border),\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1134,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleVerifyPin,\n                                    disabled: !pin.trim() || verifyingPin,\n                                    style: {\n                                        padding: \"12px 24px\",\n                                        background: !pin.trim() || verifyingPin ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        cursor: !pin.trim() || verifyingPin ? \"not-allowed\" : \"pointer\"\n                                    },\n                                    children: verifyingPin ? \"Verifying...\" : \"Connect Account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1155,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1133,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                    lineNumber: 1089,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 1077,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SettingsPage, \"e5Cm54HPOOg+iPqh5I+Z1wonm0U=\", false, function() {\n    return [\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser\n    ];\n});\n_c = SettingsPage;\nSettingsPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 1183,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/settings.tsx\n"));

/***/ })

});