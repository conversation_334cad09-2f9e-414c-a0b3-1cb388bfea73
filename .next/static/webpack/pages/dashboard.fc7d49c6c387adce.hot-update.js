"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./components/SidebarLayoutSimple.tsx":
/*!********************************************!*\
  !*** ./components/SidebarLayoutSimple.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthModal */ \"./components/AuthModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    var _user_user_metadata;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, profile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Fallback user data for when not authenticated or loading\n    const userData = {\n        name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"Alex Chen\",\n        email: (user === null || user === void 0 ? void 0 : user.email) || \"<EMAIL>\",\n        plan: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise\" : \"Free\",\n        avatar: (profile === null || profile === void 0 ? void 0 : profile.avatar_url) || null,\n        isOnline: (profile === null || profile === void 0 ? void 0 : profile.is_online) || false\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        background: \"#F5F1EB\",\n        surface: \"#FFFFFF\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            surface: \"rgba(255, 107, 53, 0.05)\",\n            text: \"#2D1B14\",\n            textSecondary: \"#5D4037\",\n            textMuted: \"#8D6E63\",\n            accent: \"#FF6B35\",\n            accentSoft: \"#FF8A65\",\n            accentLight: \"#FFF7F4\",\n            border: \"rgba(255, 107, 53, 0.15)\",\n            hover: \"rgba(255, 107, 53, 0.08)\",\n            divider: \"rgba(255, 107, 53, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.25)\" // Orange glow\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageCircle\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.BarChart3\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Video\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: colors.background,\n            padding: \"16px\",\n            gap: \"16px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"180px\",\n                    background: colors.sidebar.background,\n                    height: \"calc(100vh - 32px)\",\n                    borderRadius: \"24px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: \"0 8px 32px \".concat(colors.sidebar.glow, \", 0 2px 8px rgba(255, 107, 53, 0.1)\"),\n                    border: \"1px solid \".concat(colors.sidebar.border),\n                    overflow: \"hidden\",\n                    position: \"relative\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            height: \"2px\",\n                            background: \"linear-gradient(90deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \", \").concat(colors.sidebar.accent, \")\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"20px 16px 16px 16px\",\n                            textAlign: \"center\",\n                            background: colors.sidebar.accentLight\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"32px\",\n                                    color: colors.sidebar.accent,\n                                    fontFamily: \"Dancing Script, cursive\",\n                                    fontWeight: \"600\",\n                                    letterSpacing: \"1px\",\n                                    textShadow: \"0 2px 4px \".concat(colors.sidebar.glow),\n                                    marginBottom: \"4px\"\n                                },\n                                children: \"Exie\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"10px\",\n                                    color: colors.sidebar.textMuted,\n                                    fontWeight: \"500\",\n                                    letterSpacing: \"1px\",\n                                    textTransform: \"uppercase\"\n                                },\n                                children: \"AI Assistant\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"16px 12px\",\n                            flex: 1,\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"4px\"\n                        },\n                        children: menuItems.map((item, index)=>{\n                            const active = isActive(item.href);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"12px 16px\",\n                                                borderRadius: \"16px\",\n                                                background: active ? colors.sidebar.surface : \"transparent\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                position: \"relative\",\n                                                border: active ? \"1px solid \".concat(colors.sidebar.border) : \"1px solid transparent\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                if (!active) {\n                                                    e.currentTarget.style.background = colors.sidebar.hover;\n                                                    e.currentTarget.style.transform = \"translateX(2px)\";\n                                                    e.currentTarget.style.borderColor = colors.sidebar.border;\n                                                }\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                if (!active) {\n                                                    e.currentTarget.style.background = \"transparent\";\n                                                    e.currentTarget.style.transform = \"translateX(0)\";\n                                                    e.currentTarget.style.borderColor = \"transparent\";\n                                                }\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"0\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"3px\",\n                                                        height: \"20px\",\n                                                        background: \"linear-gradient(180deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\"),\n                                                        borderRadius: \"0 3px 3px 0\",\n                                                        boxShadow: \"0 0 8px \".concat(colors.sidebar.glow)\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"20px\",\n                                                        height: \"20px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"12px\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        size: 16,\n                                                        color: active ? colors.sidebar.accent : colors.sidebar.textSecondary,\n                                                        strokeWidth: active ? 2.5 : 2\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                        fontSize: \"14px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        letterSpacing: \"0.2px\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    index < menuItems.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            alignItems: \"center\",\n                                            margin: \"8px 0\",\n                                            height: \"1px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"3px\"\n                                            },\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"4px\",\n                                                        height: \"1px\",\n                                                        background: colors.sidebar.divider,\n                                                        borderRadius: \"1px\"\n                                                    }\n                                                }, i, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, item.href, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            margin: \"0 16px 16px 16px\",\n                            height: \"1px\",\n                            background: \"linear-gradient(90deg, transparent, \".concat(colors.sidebar.divider, \", transparent)\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"12px 12px 16px 12px\",\n                            background: colors.sidebar.accentLight\n                        },\n                        children: user ? // Authenticated user - show profile\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"10px\",\n                                    padding: \"10px 12px\",\n                                    background: colors.sidebar.background,\n                                    borderRadius: \"14px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                    border: \"1px solid \".concat(colors.sidebar.border),\n                                    boxShadow: \"0 2px 8px \".concat(colors.sidebar.glow)\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.surface;\n                                    e.currentTarget.style.borderColor = colors.sidebar.accent + \"40\";\n                                    e.currentTarget.style.transform = \"translateY(-1px)\";\n                                    e.currentTarget.style.boxShadow = \"0 4px 16px \".concat(colors.sidebar.glow);\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.background;\n                                    e.currentTarget.style.borderColor = colors.sidebar.border;\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                    e.currentTarget.style.boxShadow = \"0 2px 8px \".concat(colors.sidebar.glow);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: userData.avatar ? \"transparent\" : \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\"),\n                                            borderRadius: \"10px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            overflow: \"hidden\",\n                                            border: \"2px solid \".concat(colors.sidebar.border)\n                                        },\n                                        children: [\n                                            userData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userData.avatar,\n                                                alt: userData.name,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"8px\"\n                                                },\n                                                onError: (e)=>{\n                                                    // Fallback to icon if image fails to load\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.parentElement.style.background = \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\");\n                                                    e.currentTarget.parentElement.innerHTML = '<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path><circle cx=\"12\" cy=\"7\" r=\"4\"></circle></svg>';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 16,\n                                                color: \"#FFFFFF\",\n                                                strokeWidth: 2\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            userData.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"10px\",\n                                                    height: \"10px\",\n                                                    background: \"#00FF88\",\n                                                    borderRadius: \"50%\",\n                                                    border: \"2px solid #FFFFFF\",\n                                                    boxShadow: \"0 0 6px rgba(0, 255, 136, 0.8)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    lineHeight: \"1.2\"\n                                                },\n                                                children: userData.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"rgba(255, 255, 255, 0.8)\",\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"400\",\n                                                    lineHeight: \"1.2\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            userData.plan,\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, undefined) : // Not authenticated - show sign in button\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>setShowAuthModal(true),\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"10px\",\n                                padding: \"10px\",\n                                background: \"rgba(255, 255, 255, 0.15)\",\n                                borderRadius: \"10px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\",\n                                border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.25)\";\n                                e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.4)\";\n                                e.currentTarget.style.transform = \"translateY(-1px)\";\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.15)\";\n                                e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.2)\";\n                                e.currentTarget.style.transform = \"translateY(0)\";\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"28px\",\n                                        height: \"28px\",\n                                        background: \"rgba(255, 255, 255, 0.3)\",\n                                        borderRadius: \"8px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogIn, {\n                                        size: 14,\n                                        color: colors.sidebar.text\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"13px\",\n                                                fontWeight: \"600\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: \"rgba(255, 255, 255, 0.8)\",\n                                                fontSize: \"11px\",\n                                                fontWeight: \"400\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Access your account\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.surface,\n                    borderRadius: \"20px\",\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"40px\",\n                        height: \"100%\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                    lineNumber: 418,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 427,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"2fxYphseme7UlXU7fqnQRjVv+Ys=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayoutSimple.tsx\n"));

/***/ })

});