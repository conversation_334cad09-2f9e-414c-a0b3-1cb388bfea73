"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/SidebarLayoutSimple.tsx":
/*!********************************************!*\
  !*** ./components/SidebarLayoutSimple.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthModal */ \"./components/AuthModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    var _user_user_metadata;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, profile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Fallback user data for when not authenticated or loading\n    const userData = {\n        name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"Alex Chen\",\n        email: (user === null || user === void 0 ? void 0 : user.email) || \"<EMAIL>\",\n        plan: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise\" : \"Free\",\n        avatar: (profile === null || profile === void 0 ? void 0 : profile.avatar_url) || null,\n        isOnline: (profile === null || profile === void 0 ? void 0 : profile.is_online) || false\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        background: \"#F5F1EB\",\n        surface: \"#FFFFFF\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        sidebar: {\n            background: \"#1C1612\",\n            surface: \"rgba(255, 107, 53, 0.04)\",\n            text: \"#F7F3EF\",\n            textSecondary: \"#D4B896\",\n            textMuted: \"#9A8066\",\n            accent: \"#FF6B35\",\n            accentSoft: \"#FF8A65\",\n            accentGlow: \"rgba(255, 107, 53, 0.2)\",\n            border: \"rgba(212, 184, 150, 0.12)\",\n            hover: \"rgba(255, 107, 53, 0.08)\",\n            divider: \"rgba(212, 184, 150, 0.15)\" // Warm divider\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageCircle\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.BarChart3\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Video\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: colors.background,\n            padding: \"20px\",\n            gap: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"160px\",\n                    background: colors.sidebar.background,\n                    minHeight: \"calc(100vh - 40px)\",\n                    borderRadius: \"12px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: \"0 4px 20px rgba(0, 0, 0, 0.1)\" // Subtle shadow\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px\",\n                            textAlign: \"center\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: \"28px\",\n                                color: colors.sidebar.text,\n                                fontFamily: \"Georgia, serif\",\n                                fontStyle: \"italic\"\n                            },\n                            children: \"ℰ\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"12px 12px\"\n                        },\n                        children: menuItems.map((item)=>{\n                            const active = isActive(item.href);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"4px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    style: {\n                                        textDecoration: \"none\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            padding: \"10px 12px\",\n                                            borderRadius: \"8px\",\n                                            background: active ? \"rgba(255, 255, 255, 0.2)\" : \"transparent\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            if (!active) {\n                                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.1)\";\n                                            }\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            if (!active) {\n                                                e.currentTarget.style.background = \"transparent\";\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                size: 14,\n                                                color: colors.sidebar.text\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    marginLeft: \"10px\",\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: active ? \"600\" : \"500\"\n                                                },\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, item.href, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"12px\",\n                            borderTop: \"1px solid rgba(255, 255, 255, 0.3)\"\n                        },\n                        children: user ? // Authenticated user - show profile\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"10px\",\n                                    padding: \"10px\",\n                                    background: \"rgba(255, 255, 255, 0.15)\",\n                                    borderRadius: \"10px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.25)\";\n                                    e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.4)\";\n                                    e.currentTarget.style.transform = \"translateY(-1px)\";\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.15)\";\n                                    e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.2)\";\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"36px\",\n                                            height: \"36px\",\n                                            background: userData.avatar ? \"transparent\" : \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \" 0%, #FF8A65 100%)\"),\n                                            borderRadius: \"10px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            overflow: \"hidden\",\n                                            border: \"2px solid \".concat(colors.sidebar.border)\n                                        },\n                                        children: [\n                                            userData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userData.avatar,\n                                                alt: userData.name,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"8px\"\n                                                },\n                                                onError: (e)=>{\n                                                    // Fallback to icon if image fails to load\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.parentElement.style.background = \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \" 0%, #FF8A65 100%)\");\n                                                    e.currentTarget.parentElement.innerHTML = '<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path><circle cx=\"12\" cy=\"7\" r=\"4\"></circle></svg>';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 16,\n                                                color: \"#FFFFFF\",\n                                                strokeWidth: 2\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            userData.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"10px\",\n                                                    height: \"10px\",\n                                                    background: \"#00FF88\",\n                                                    borderRadius: \"50%\",\n                                                    border: \"2px solid #2A2A2A\",\n                                                    boxShadow: \"0 0 6px rgba(0, 255, 136, 0.8)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    lineHeight: \"1.2\"\n                                                },\n                                                children: userData.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"rgba(255, 255, 255, 0.8)\",\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"400\",\n                                                    lineHeight: \"1.2\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            userData.plan,\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, undefined) : // Not authenticated - show sign in button\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>setShowAuthModal(true),\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"10px\",\n                                padding: \"10px\",\n                                background: \"rgba(255, 255, 255, 0.15)\",\n                                borderRadius: \"10px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\",\n                                border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.25)\";\n                                e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.4)\";\n                                e.currentTarget.style.transform = \"translateY(-1px)\";\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.15)\";\n                                e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.2)\";\n                                e.currentTarget.style.transform = \"translateY(0)\";\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"28px\",\n                                        height: \"28px\",\n                                        background: \"rgba(255, 255, 255, 0.3)\",\n                                        borderRadius: \"8px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogIn, {\n                                        size: 14,\n                                        color: colors.sidebar.text\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"13px\",\n                                                fontWeight: \"600\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: \"rgba(255, 255, 255, 0.8)\",\n                                                fontSize: \"11px\",\n                                                fontWeight: \"400\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Access your account\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.surface,\n                    borderRadius: \"20px\",\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"40px\",\n                        height: \"100%\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"2fxYphseme7UlXU7fqnQRjVv+Ys=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayoutSimple.tsx\n"));

/***/ })

});