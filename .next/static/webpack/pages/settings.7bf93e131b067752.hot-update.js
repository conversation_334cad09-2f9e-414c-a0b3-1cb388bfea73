"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/settings",{

/***/ "./pages/settings.tsx":
/*!****************************!*\
  !*** ./pages/settings.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!lucide-react */ \"__barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/supabase */ \"./lib/supabase.ts\");\n// pages/settings.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SettingsPage = ()=>{\n    _s();\n    const { user, profile, updateProfile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"agent-e\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Agent E Settings\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"My AI Project\");\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [targetAudience, setTargetAudience] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [brandVoice, setBrandVoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"professional\");\n    const [customPrompts, setCustomPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: \"Product Launch\",\n            prompt: \"Create engaging content for product launches with excitement and clear benefits\"\n        },\n        {\n            id: 2,\n            name: \"Educational\",\n            prompt: \"Write informative content that teaches and provides value to the audience\"\n        }\n    ]);\n    // Profile Settings\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [avatarUrl, setAvatarUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // X Integration Settings\n    const [xAccountConnected, setXAccountConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [xAccountInfo, setXAccountInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connectingX, setConnectingX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPinModal, setShowPinModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [oauthToken, setOauthToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [oauthTokenSecret, setOauthTokenSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [pin, setPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [verifyingPin, setVerifyingPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load settings from Supabase\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            var _user_user_metadata;\n            loadSettings();\n            loadXAccountStatus();\n            setFullName((profile === null || profile === void 0 ? void 0 : profile.full_name) || ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"\");\n            setEmail(user.email || \"\");\n            setAvatarUrl((profile === null || profile === void 0 ? void 0 : profile.avatar_url) || \"\");\n        }\n    }, [\n        user,\n        profile\n    ]);\n    const loadSettings = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"agent_e_settings\").select(\"*\").eq(\"user_id\", user.id).single();\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error loading settings:\", error);\n                return;\n            }\n            if (data) {\n                setProjectName(data.project_name || \"My AI Project\");\n                setProjectDescription(data.project_description || \"\");\n                setTargetAudience(data.target_audience || \"\");\n                setBrandVoice(data.brand_voice || \"professional\");\n                setCustomPrompts(data.custom_prompts || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading settings:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveAgentESettings = async ()=>{\n        if (!user) return;\n        setSaving(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"agent_e_settings\").upsert({\n                user_id: user.id,\n                project_name: projectName,\n                project_description: projectDescription,\n                target_audience: targetAudience,\n                brand_voice: brandVoice,\n                custom_prompts: customPrompts\n            });\n            if (error) {\n                console.error(\"Error saving settings:\", error);\n                alert(\"Error saving settings. Please try again.\");\n            } else {\n                alert(\"Settings saved successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            alert(\"Error saving settings. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const saveProfileSettings = async ()=>{\n        if (!user) {\n            alert(\"You must be logged in to update your profile.\");\n            return;\n        }\n        setSaving(true);\n        try {\n            console.log(\"Updating profile with:\", {\n                full_name: fullName\n            });\n            // Try to update the profile\n            const { error } = await updateProfile({\n                full_name: fullName,\n                avatar_url: avatarUrl || null\n            });\n            if (error) {\n                var _error_message;\n                console.error(\"Error updating profile:\", error);\n                // If the table doesn't exist, try to create it first\n                if (error.code === \"42P01\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('relation \"user_profiles\" does not exist'))) {\n                    alert(\"Database setup required. Please run the database schema first, then try again.\");\n                } else {\n                    alert(\"Error updating profile: \".concat(error.message || \"Please try again.\"));\n                }\n            } else {\n                alert(\"Profile updated successfully!\");\n                console.log(\"Profile updated successfully\");\n            }\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            alert(\"Error updating profile. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const loadXAccountStatus = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/account-status?userId=\".concat(user.id));\n            const data = await response.json();\n            if (response.ok && data.connected) {\n                setXAccountConnected(true);\n                setXAccountInfo(data.accountInfo);\n            } else {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n            }\n        } catch (error) {\n            console.error(\"Error loading X account status:\", error);\n            setXAccountConnected(false);\n            setXAccountInfo(null);\n        }\n    };\n    const handleConnectX = async ()=>{\n        if (!user) {\n            alert(\"Please log in first\");\n            return;\n        }\n        setConnectingX(true);\n        try {\n            const response = await fetch(\"/api/x/auth-url\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // Store the OAuth tokens and show the modal FIRST\n                setOauthToken(data.oauth_token);\n                setOauthTokenSecret(data.oauth_token_secret);\n                setShowPinModal(true);\n                // Then open X in a new tab with instructions\n                setTimeout(()=>{\n                    window.open(data.authUrl, \"_blank\");\n                }, 500);\n            } else {\n                alert(data.error || \"Failed to initiate X connection\");\n            }\n        } catch (error) {\n            console.error(\"Error connecting to X:\", error);\n            alert(\"Failed to connect to X. Please try again.\");\n        } finally{\n            setConnectingX(false);\n        }\n    };\n    const handleVerifyPin = async ()=>{\n        if (!user || !pin.trim() || !oauthToken || !oauthTokenSecret) return;\n        setVerifyingPin(true);\n        try {\n            const response = await fetch(\"/api/x/verify-pin\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    oauth_token: oauthToken,\n                    oauth_token_secret: oauthTokenSecret,\n                    pin: pin.trim(),\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                setXAccountConnected(true);\n                setXAccountInfo(data.accountInfo);\n                setShowPinModal(false);\n                setPin(\"\");\n                setOauthToken(\"\");\n                setOauthTokenSecret(\"\");\n                alert(\"X account connected successfully!\");\n            } else {\n                alert(data.error || \"Failed to verify PIN\");\n            }\n        } catch (error) {\n            console.error(\"Error verifying PIN:\", error);\n            alert(\"Failed to verify PIN. Please try again.\");\n        } finally{\n            setVerifyingPin(false);\n        }\n    };\n    const handleDisconnectX = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/disconnect\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            if (response.ok) {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n                alert(\"X account disconnected successfully\");\n            } else {\n                const data = await response.json();\n                alert(data.error || \"Failed to disconnect X account\");\n            }\n        } catch (error) {\n            console.error(\"Error disconnecting X:\", error);\n            alert(\"Failed to disconnect X account. Please try again.\");\n        }\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        surface: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\"\n    };\n    const tabs = [\n        {\n            id: \"agent-e\",\n            label: \"Agent E\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot\n        },\n        {\n            id: \"account\",\n            label: \"Account\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User\n        },\n        {\n            id: \"integrations\",\n            label: \"Integrations\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap\n        },\n        {\n            id: \"notifications\",\n            label: \"Notifications\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bell\n        },\n        {\n            id: \"security\",\n            label: \"Security\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Shield\n        }\n    ];\n    const handleSavePrompt = (id, newPrompt)=>{\n        setCustomPrompts((prev)=>prev.map((p)=>p.id === id ? {\n                    ...p,\n                    prompt: newPrompt\n                } : p));\n    };\n    const addNewPrompt = ()=>{\n        const newId = Math.max(...customPrompts.map((p)=>p.id)) + 1;\n        setCustomPrompts((prev)=>[\n                ...prev,\n                {\n                    id: newId,\n                    name: \"New Prompt\",\n                    prompt: \"\"\n                }\n            ]);\n    };\n    const deletePrompt = (id)=>{\n        setCustomPrompts((prev)=>prev.filter((p)=>p.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.background\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"40px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"8px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        children: \"Settings\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            margin: 0,\n                            fontSize: \"16px\"\n                        },\n                        children: \"Configure Agent E and manage your account preferences\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    gap: \"8px\",\n                    marginBottom: \"40px\",\n                    borderBottom: \"1px solid \".concat(colors.border),\n                    paddingBottom: \"0\"\n                },\n                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(tab.id),\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"8px\",\n                            padding: \"12px 20px\",\n                            background: activeTab === tab.id ? colors.surface : \"transparent\",\n                            border: activeTab === tab.id ? \"1px solid \".concat(colors.border) : \"1px solid transparent\",\n                            borderBottom: activeTab === tab.id ? \"1px solid \".concat(colors.surface) : \"1px solid transparent\",\n                            borderRadius: \"8px 8px 0 0\",\n                            fontSize: \"14px\",\n                            fontWeight: \"500\",\n                            color: activeTab === tab.id ? colors.text.primary : colors.text.secondary,\n                            cursor: \"pointer\",\n                            transition: \"all 0.2s ease\",\n                            marginBottom: \"-1px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, undefined),\n                            tab.label\n                        ]\n                    }, tab.id, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: colors.surface,\n                    borderRadius: \"12px\",\n                    padding: \"32px\",\n                    border: \"1px solid \".concat(colors.border),\n                    minHeight: \"500px\"\n                },\n                children: [\n                    activeTab === \"agent-e\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Agent E Configuration\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Project Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gridTemplateColumns: \"1fr 1fr\",\n                                            gap: \"20px\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: \"Project Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: projectName,\n                                                        onChange: (e)=>setProjectName(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: \"Brand Voice\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: brandVoice,\n                                                        onChange: (e)=>setBrandVoice(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"professional\",\n                                                                children: \"Professional\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"casual\",\n                                                                children: \"Casual & Friendly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"technical\",\n                                                                children: \"Technical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"creative\",\n                                                                children: \"Creative & Fun\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"authoritative\",\n                                                                children: \"Authoritative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"Project Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: projectDescription,\n                                                onChange: (e)=>setProjectDescription(e.target.value),\n                                                placeholder: \"Describe your project, product, or service so Agent E can create relevant content...\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    padding: \"12px 16px\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    background: colors.background,\n                                                    outline: \"none\",\n                                                    minHeight: \"100px\",\n                                                    resize: \"vertical\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"Target Audience\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: targetAudience,\n                                                onChange: (e)=>setTargetAudience(e.target.value),\n                                                placeholder: \"e.g., Tech entrepreneurs, Small business owners, Developers...\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    padding: \"12px 16px\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    background: colors.background,\n                                                    outline: \"none\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    margin: 0\n                                                },\n                                                children: \"Custom Prompts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewPrompt,\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"6px\",\n                                                    padding: \"8px 16px\",\n                                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    cursor: \"pointer\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Plus, {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Add Prompt\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    customPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                border: \"1px solid \".concat(colors.border),\n                                                borderRadius: \"8px\",\n                                                padding: \"16px\",\n                                                marginBottom: \"12px\",\n                                                background: colors.background\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        alignItems: \"center\",\n                                                        marginBottom: \"12px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: prompt.name,\n                                                            onChange: (e)=>{\n                                                                setCustomPrompts((prev)=>prev.map((p)=>p.id === prompt.id ? {\n                                                                            ...p,\n                                                                            name: e.target.value\n                                                                        } : p));\n                                                            },\n                                                            style: {\n                                                                background: \"transparent\",\n                                                                border: \"none\",\n                                                                fontSize: \"16px\",\n                                                                fontWeight: \"600\",\n                                                                color: colors.text.primary,\n                                                                outline: \"none\",\n                                                                flex: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deletePrompt(prompt.id),\n                                                            style: {\n                                                                background: \"none\",\n                                                                border: \"none\",\n                                                                color: colors.text.tertiary,\n                                                                cursor: \"pointer\",\n                                                                padding: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Trash2, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: prompt.prompt,\n                                                    onChange: (e)=>handleSavePrompt(prompt.id, e.target.value),\n                                                    placeholder: \"Enter your custom prompt for Agent E...\",\n                                                    style: {\n                                                        width: \"100%\",\n                                                        padding: \"12px\",\n                                                        border: \"1px solid \".concat(colors.border),\n                                                        borderRadius: \"6px\",\n                                                        fontSize: \"14px\",\n                                                        background: colors.surface,\n                                                        outline: \"none\",\n                                                        minHeight: \"80px\",\n                                                        resize: \"vertical\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, prompt.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveAgentESettings,\n                                disabled: saving || !user,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 24px\",\n                                    background: saving || !user ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                    boxShadow: saving || !user ? \"none\" : \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                    opacity: saving || !user ? 0.6 : 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Save, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    saving ? \"Saving...\" : \"Save Agent E Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab === \"integrations\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Integrations\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"12px\",\n                                    padding: \"24px\",\n                                    marginBottom: \"24px\",\n                                    background: colors.background\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                size: 24,\n                                                color: colors.primary\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    margin: 0\n                                                },\n                                                children: \"X (Twitter) Account\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: \"Connect your X account to enable automated posting and content scheduling through our premium service.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    xAccountConnected && xAccountInfo ? // Connected State\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            padding: \"16px\",\n                                            background: \"#F0FDF4\",\n                                            border: \"1px solid #BBF7D0\",\n                                            borderRadius: \"8px\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\"\n                                                },\n                                                children: [\n                                                    xAccountInfo.profile_image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: xAccountInfo.profile_image_url,\n                                                        alt: \"Profile\",\n                                                        style: {\n                                                            width: \"40px\",\n                                                            height: \"40px\",\n                                                            borderRadius: \"50%\",\n                                                            objectFit: \"cover\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: \"#065F46\",\n                                                                    fontSize: \"16px\",\n                                                                    fontWeight: \"600\"\n                                                                },\n                                                                children: xAccountInfo.name || \"Connected Account\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: \"#047857\",\n                                                                    fontSize: \"14px\"\n                                                                },\n                                                                children: [\n                                                                    \"@\",\n                                                                    xAccountInfo.username\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    padding: \"6px 12px\",\n                                                    background: \"#10B981\",\n                                                    color: \"white\",\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"Connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 17\n                                    }, undefined) : // Not Connected State\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px\",\n                                            background: \"#FEF3C7\",\n                                            border: \"1px solid #FDE68A\",\n                                            borderRadius: \"8px\",\n                                            marginBottom: \"16px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"#92400E\",\n                                                    fontSize: \"14px\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"No X account connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 765,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"#B45309\",\n                                                    fontSize: \"12px\"\n                                                },\n                                                children: \"Connect your account to start scheduling posts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            gap: \"12px\"\n                                        },\n                                        children: xAccountConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDisconnectX,\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"10px 20px\",\n                                                background: \"#FEE2E2\",\n                                                color: \"#DC2626\",\n                                                border: \"1px solid #FECACA\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                cursor: \"pointer\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Disconnect Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleConnectX,\n                                            disabled: connectingX,\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"12px 24px\",\n                                                background: connectingX ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                color: \"white\",\n                                                border: \"none\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                cursor: connectingX ? \"not-allowed\" : \"pointer\",\n                                                opacity: connectingX ? 0.6 : 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 821,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                connectingX ? \"Connecting...\" : \"Connect X Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 671,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 656,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab === \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 841,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Account Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 832,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Profile Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 847,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gap: \"16px\",\n                                            maxWidth: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Full Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 858,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: fullName,\n                                                        onChange: (e)=>setFullName(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 857,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Email Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        disabled: true,\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: \"#F9F9F9\",\n                                                            color: colors.text.secondary,\n                                                            cursor: \"not-allowed\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 883,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Avatar URL (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"url\",\n                                                        value: avatarUrl,\n                                                        onChange: (e)=>setAvatarUrl(e.target.value),\n                                                        placeholder: \"https://example.com/your-avatar.jpg\",\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            fontSize: \"12px\",\n                                                            color: colors.text.tertiary,\n                                                            marginTop: \"4px\",\n                                                            marginBottom: \"0\"\n                                                        },\n                                                        children: \"Enter a URL to your profile picture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 910,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 856,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 846,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \"15 0%, \").concat(colors.primaryLight, \"15 100%)\"),\n                                            border: \"1px solid \".concat(colors.primary, \"30\"),\n                                            borderRadius: \"12px\",\n                                            padding: \"20px\",\n                                            maxWidth: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\",\n                                                    marginBottom: \"12px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"32px\",\n                                                            height: \"32px\",\n                                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                            borderRadius: \"8px\",\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Crown, {\n                                                            size: 16,\n                                                            color: \"white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 980,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 971,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: colors.text.primary,\n                                                                    fontSize: \"16px\",\n                                                                    fontWeight: \"600\"\n                                                                },\n                                                                children: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro Plan\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise Plan\" : \"Free Plan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: colors.text.secondary,\n                                                                    fontSize: \"14px\"\n                                                                },\n                                                                children: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"free\" ? \"No subscription\" : (profile === null || profile === void 0 ? void 0 : profile.subscription_status) === \"active\" ? \"$29/month • Active\" : \"Subscription inactive\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 990,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 965,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    gap: \"12px\",\n                                                    marginTop: \"16px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"8px 16px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        children: \"Manage Billing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"8px 16px\",\n                                                            background: \"transparent\",\n                                                            color: colors.text.secondary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        children: \"Cancel Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 1016,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 999,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 948,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveProfileSettings,\n                                disabled: saving || !user,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 24px\",\n                                    background: saving || !user ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                    boxShadow: saving || !user ? \"none\" : \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                    opacity: saving || !user ? 0.6 : 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Save, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 1052,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    saving ? \"Saving...\" : \"Save Account Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1033,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 831,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab !== \"agent-e\" && activeTab !== \"integrations\" && activeTab !== \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"60px 20px\",\n                            color: colors.text.secondary\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginBottom: \"12px\"\n                                },\n                                children: \"Coming Soon\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1065,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"This section is under development.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1066,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 1060,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, undefined),\n            showPinModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: \"rgba(0, 0, 0, 0.5)\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: colors.surface,\n                        borderRadius: \"16px\",\n                        padding: \"32px\",\n                        width: \"90%\",\n                        maxWidth: \"400px\",\n                        textAlign: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                color: colors.text.primary,\n                                fontSize: \"20px\",\n                                fontWeight: \"600\",\n                                marginBottom: \"16px\"\n                            },\n                            children: \"\\uD83D\\uDD17 Connect Your X Account\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1093,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#F0F9FF\",\n                                border: \"1px solid #BAE6FD\",\n                                borderRadius: \"8px\",\n                                padding: \"16px\",\n                                marginBottom: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: \"#0369A1\",\n                                        fontSize: \"14px\",\n                                        marginBottom: \"12px\",\n                                        fontWeight: \"600\"\n                                    },\n                                    children: \"\\uD83D\\uDCCB Instructions:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1109,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    style: {\n                                        color: \"#0369A1\",\n                                        fontSize: \"13px\",\n                                        lineHeight: \"1.5\",\n                                        margin: 0,\n                                        paddingLeft: \"20px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"A new tab will open with X authorization page\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1124,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Click \"Authorize app\" on the X page'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1125,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Copy the PIN code shown on X\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1126,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Come back to this page and enter the PIN below\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1127,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Click \"Connect Account\" to finish'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1128,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1117,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1102,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: colors.text.secondary,\n                                fontSize: \"14px\",\n                                marginBottom: \"20px\",\n                                textAlign: \"center\",\n                                fontWeight: \"600\"\n                            },\n                            children: \"Enter the PIN code from X:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1132,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: pin,\n                            onChange: (e)=>setPin(e.target.value),\n                            placeholder: \"Enter PIN code\",\n                            style: {\n                                width: \"100%\",\n                                padding: \"12px 16px\",\n                                border: \"1px solid \".concat(colors.border),\n                                borderRadius: \"8px\",\n                                fontSize: \"16px\",\n                                textAlign: \"center\",\n                                marginBottom: \"24px\",\n                                outline: \"none\"\n                            },\n                            maxLength: 7\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1142,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                gap: \"8px\",\n                                justifyContent: \"center\",\n                                flexWrap: \"wrap\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowPinModal(false);\n                                        setPin(\"\");\n                                        setOauthToken(\"\");\n                                        setOauthTokenSecret(\"\");\n                                    },\n                                    style: {\n                                        padding: \"12px 20px\",\n                                        background: colors.background,\n                                        color: colors.text.primary,\n                                        border: \"1px solid \".concat(colors.border),\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1161,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        // Re-open X authorization page\n                                        const authUrl = \"https://api.twitter.com/oauth/authorize?oauth_token=\".concat(oauthToken);\n                                        window.open(authUrl, \"_blank\");\n                                    },\n                                    disabled: !oauthToken,\n                                    style: {\n                                        padding: \"12px 20px\",\n                                        background: \"#1DA1F2\",\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: \"Open X Page\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1182,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleVerifyPin,\n                                    disabled: !pin.trim() || verifyingPin,\n                                    style: {\n                                        padding: \"12px 24px\",\n                                        background: !pin.trim() || verifyingPin ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        cursor: !pin.trim() || verifyingPin ? \"not-allowed\" : \"pointer\"\n                                    },\n                                    children: verifyingPin ? \"Verifying...\" : \"Connect Account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1203,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1160,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                    lineNumber: 1085,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 1073,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SettingsPage, \"e5Cm54HPOOg+iPqh5I+Z1wonm0U=\", false, function() {\n    return [\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser\n    ];\n});\n_c = SettingsPage;\nSettingsPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 1231,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/settings.tsx\n"));

/***/ })

});