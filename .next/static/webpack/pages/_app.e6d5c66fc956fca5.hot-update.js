"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./contexts/UserContext.tsx":
/*!**********************************!*\
  !*** ./contexts/UserContext.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: function() { return /* binding */ UserProvider; },\n/* harmony export */   useUser: function() { return /* binding */ useUser; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"./lib/supabase.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useUser = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n};\n_s(useUser, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst UserProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n            var _session_user;\n            setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                await fetchUserProfile(session.user.id);\n                await updateOnlineStatus(true);\n            }\n            setLoading(false);\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                await fetchUserProfile(session.user.id);\n                await updateOnlineStatus(true);\n            } else {\n                setProfile(null);\n            }\n            setLoading(false);\n        });\n        // Update online status when user leaves\n        const handleBeforeUnload = ()=>{\n            if (user) {\n                updateOnlineStatus(false);\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>{\n            subscription.unsubscribe();\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        user\n    ]);\n    const fetchUserProfile = async (userId)=>{\n        try {\n            console.log(\"Fetching user profile for userId:\", userId);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").select(\"*\").eq(\"user_id\", userId).single();\n            console.log(\"Profile fetch result:\", {\n                data,\n                error\n            });\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error fetching user profile:\", error);\n                return;\n            }\n            if (data) {\n                console.log(\"Setting profile data:\", data);\n                setProfile(data);\n            } else {\n                var _user_user_metadata;\n                // Create default profile if it doesn't exist\n                console.log(\"Creating new profile for user:\", userId);\n                const { data: newProfile, error: createError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").insert({\n                    user_id: userId,\n                    full_name: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"User\",\n                    plan: \"free\",\n                    subscription_status: \"inactive\"\n                }).select().single();\n                console.log(\"Profile creation result:\", {\n                    newProfile,\n                    createError\n                });\n                if (createError) {\n                    console.error(\"Error creating user profile:\", createError);\n                } else {\n                    console.log(\"Setting new profile:\", newProfile);\n                    setProfile(newProfile);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error in fetchUserProfile:\", error);\n        }\n    };\n    const signIn = async (email, password)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            error\n        };\n    };\n    const signUp = async (email, password, fullName)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    full_name: fullName\n                }\n            }\n        });\n        return {\n            error\n        };\n    };\n    const signOut = async ()=>{\n        await updateOnlineStatus(false);\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) return {\n            error: \"No user logged in\"\n        };\n        console.log(\"Updating profile for user:\", user.id, \"with updates:\", updates);\n        try {\n            // First, try to update the profile\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").update(updates).eq(\"user_id\", user.id).select().single();\n            console.log(\"Update result:\", {\n                data,\n                error\n            });\n            if (error) {\n                // If the profile doesn't exist, try to create it first\n                if (error.code === \"PGRST116\") {\n                    var _user_user_metadata;\n                    console.log(\"Profile not found, creating new profile...\");\n                    const { data: newProfile, error: createError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").insert({\n                        user_id: user.id,\n                        full_name: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"User\",\n                        plan: \"free\",\n                        subscription_status: \"inactive\",\n                        ...updates\n                    }).select().single();\n                    if (createError) {\n                        console.error(\"Error creating profile:\", createError);\n                        return {\n                            error: createError\n                        };\n                    } else {\n                        console.log(\"Profile created successfully:\", newProfile);\n                        setProfile(newProfile);\n                        return {\n                            error: null\n                        };\n                    }\n                } else {\n                    console.error(\"Error updating profile:\", error);\n                    return {\n                        error\n                    };\n                }\n            } else {\n                console.log(\"Profile updated successfully:\", data);\n                setProfile(data);\n                return {\n                    error: null\n                };\n            }\n        } catch (error) {\n            console.error(\"Unexpected error in updateProfile:\", error);\n            return {\n                error\n            };\n        }\n    };\n    const updateOnlineStatus = async (isOnline)=>{\n        if (!user) return;\n        try {\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"user_profiles\").update({\n                is_online: isOnline,\n                last_seen: new Date().toISOString()\n            }).eq(\"user_id\", user.id);\n        } catch (error) {\n            console.error(\"Error updating online status:\", error);\n        }\n    };\n    const value = {\n        user,\n        profile,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        updateProfile,\n        updateOnlineStatus\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/contexts/UserContext.tsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(UserProvider, \"DYSpA4ZauWKW8e4CNkO4ayA+RbM=\");\n_c = UserProvider;\nvar _c;\n$RefreshReg$(_c, \"UserProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250ZXh0cy9Vc2VyQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQTZFO0FBRWhCO0FBYTdELE1BQU1NLDRCQUFjTCxvREFBYUEsQ0FBOEJNO0FBRXhELE1BQU1DLFVBQVU7O0lBQ3JCLE1BQU1DLFVBQVVQLGlEQUFVQSxDQUFDSTtJQUMzQixJQUFJRyxZQUFZRixXQUFXO1FBQ3pCLE1BQU0sSUFBSUcsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBQztHQU5ZRDtBQVFOLE1BQU1HLGVBQXdEO1FBQUMsRUFBRUMsUUFBUSxFQUFFOztJQUNoRixNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR1YsK0NBQVFBLENBQXNCO0lBQ3RELE1BQU0sQ0FBQ1csU0FBU0MsV0FBVyxHQUFHWiwrQ0FBUUEsQ0FBcUI7SUFDM0QsTUFBTSxDQUFDYSxTQUFTQyxXQUFXLEdBQUdkLCtDQUFRQSxDQUFDO0lBRXZDRCxnREFBU0EsQ0FBQztRQUNSLHNCQUFzQjtRQUN0QixNQUFNZ0Isb0JBQW9CO1lBQ3hCLE1BQU0sRUFBRUMsTUFBTSxFQUFFQyxPQUFPLEVBQUUsRUFBRSxHQUFHLE1BQU1oQixtREFBUUEsQ0FBQ2lCLElBQUksQ0FBQ0MsVUFBVTtnQkFDcERGO1lBQVJQLFFBQVFPLENBQUFBLGdCQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNSLElBQUksY0FBYlEsMkJBQUFBLGdCQUFpQjtZQUV6QixJQUFJQSxvQkFBQUEsOEJBQUFBLFFBQVNSLElBQUksRUFBRTtnQkFDakIsTUFBTVcsaUJBQWlCSCxRQUFRUixJQUFJLENBQUNZLEVBQUU7Z0JBQ3RDLE1BQU1DLG1CQUFtQjtZQUMzQjtZQUVBUixXQUFXO1FBQ2I7UUFFQUM7UUFFQSwwQkFBMEI7UUFDMUIsTUFBTSxFQUFFQyxNQUFNLEVBQUVPLFlBQVksRUFBRSxFQUFFLEdBQUd0QixtREFBUUEsQ0FBQ2lCLElBQUksQ0FBQ00saUJBQWlCLENBQ2hFLE9BQU9DLE9BQU9SO2dCQUNKQTtZQUFSUCxRQUFRTyxDQUFBQSxnQkFBQUEsb0JBQUFBLDhCQUFBQSxRQUFTUixJQUFJLGNBQWJRLDJCQUFBQSxnQkFBaUI7WUFFekIsSUFBSUEsb0JBQUFBLDhCQUFBQSxRQUFTUixJQUFJLEVBQUU7Z0JBQ2pCLE1BQU1XLGlCQUFpQkgsUUFBUVIsSUFBSSxDQUFDWSxFQUFFO2dCQUN0QyxNQUFNQyxtQkFBbUI7WUFDM0IsT0FBTztnQkFDTFYsV0FBVztZQUNiO1lBRUFFLFdBQVc7UUFDYjtRQUdGLHdDQUF3QztRQUN4QyxNQUFNWSxxQkFBcUI7WUFDekIsSUFBSWpCLE1BQU07Z0JBQ1JhLG1CQUFtQjtZQUNyQjtRQUNGO1FBRUFLLE9BQU9DLGdCQUFnQixDQUFDLGdCQUFnQkY7UUFFeEMsT0FBTztZQUNMSCxhQUFhTSxXQUFXO1lBQ3hCRixPQUFPRyxtQkFBbUIsQ0FBQyxnQkFBZ0JKO1FBQzdDO0lBQ0YsR0FBRztRQUFDakI7S0FBSztJQUVULE1BQU1XLG1CQUFtQixPQUFPVztRQUM5QixJQUFJO1lBQ0ZDLFFBQVFDLEdBQUcsQ0FBQyxxQ0FBcUNGO1lBQ2pELE1BQU0sRUFBRWYsSUFBSSxFQUFFa0IsS0FBSyxFQUFFLEdBQUcsTUFBTWpDLG1EQUFRQSxDQUNuQ2tDLElBQUksQ0FBQyxpQkFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxXQUFXTixRQUNkTyxNQUFNO1lBRVROLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUI7Z0JBQUVqQjtnQkFBTWtCO1lBQU07WUFFbkQsSUFBSUEsU0FBU0EsTUFBTUssSUFBSSxLQUFLLFlBQVk7Z0JBQ3RDUCxRQUFRRSxLQUFLLENBQUMsZ0NBQWdDQTtnQkFDOUM7WUFDRjtZQUVBLElBQUlsQixNQUFNO2dCQUNSZ0IsUUFBUUMsR0FBRyxDQUFDLHlCQUF5QmpCO2dCQUNyQ0osV0FBV0k7WUFDYixPQUFPO29CQU9VUDtnQkFOZiw2Q0FBNkM7Z0JBQzdDdUIsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQ0Y7Z0JBQzlDLE1BQU0sRUFBRWYsTUFBTXdCLFVBQVUsRUFBRU4sT0FBT08sV0FBVyxFQUFFLEdBQUcsTUFBTXhDLG1EQUFRQSxDQUM1RGtDLElBQUksQ0FBQyxpQkFDTE8sTUFBTSxDQUFDO29CQUNOQyxTQUFTWjtvQkFDVGEsV0FBV25DLENBQUFBLGlCQUFBQSw0QkFBQUEsc0JBQUFBLEtBQU1vQyxhQUFhLGNBQW5CcEMsMENBQUFBLG9CQUFxQm1DLFNBQVMsS0FBSTtvQkFDN0NFLE1BQU07b0JBQ05DLHFCQUFxQjtnQkFDdkIsR0FDQ1gsTUFBTSxHQUNORSxNQUFNO2dCQUVUTixRQUFRQyxHQUFHLENBQUMsNEJBQTRCO29CQUFFTztvQkFBWUM7Z0JBQVk7Z0JBRWxFLElBQUlBLGFBQWE7b0JBQ2ZULFFBQVFFLEtBQUssQ0FBQyxnQ0FBZ0NPO2dCQUNoRCxPQUFPO29CQUNMVCxRQUFRQyxHQUFHLENBQUMsd0JBQXdCTztvQkFDcEM1QixXQUFXNEI7Z0JBQ2I7WUFDRjtRQUNGLEVBQUUsT0FBT04sT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsOEJBQThCQTtRQUM5QztJQUNGO0lBRUEsTUFBTWMsU0FBUyxPQUFPQyxPQUFlQztRQUNuQyxNQUFNLEVBQUVoQixLQUFLLEVBQUUsR0FBRyxNQUFNakMsbURBQVFBLENBQUNpQixJQUFJLENBQUNpQyxrQkFBa0IsQ0FBQztZQUN2REY7WUFDQUM7UUFDRjtRQUNBLE9BQU87WUFBRWhCO1FBQU07SUFDakI7SUFFQSxNQUFNa0IsU0FBUyxPQUFPSCxPQUFlQyxVQUFrQkc7UUFDckQsTUFBTSxFQUFFbkIsS0FBSyxFQUFFLEdBQUcsTUFBTWpDLG1EQUFRQSxDQUFDaUIsSUFBSSxDQUFDa0MsTUFBTSxDQUFDO1lBQzNDSDtZQUNBQztZQUNBSSxTQUFTO2dCQUNQdEMsTUFBTTtvQkFDSjRCLFdBQVdTO2dCQUNiO1lBQ0Y7UUFDRjtRQUNBLE9BQU87WUFBRW5CO1FBQU07SUFDakI7SUFFQSxNQUFNcUIsVUFBVTtRQUNkLE1BQU1qQyxtQkFBbUI7UUFDekIsTUFBTXJCLG1EQUFRQSxDQUFDaUIsSUFBSSxDQUFDcUMsT0FBTztJQUM3QjtJQUVBLE1BQU1DLGdCQUFnQixPQUFPQztRQUMzQixJQUFJLENBQUNoRCxNQUFNLE9BQU87WUFBRXlCLE9BQU87UUFBb0I7UUFFL0NGLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJ4QixLQUFLWSxFQUFFLEVBQUUsaUJBQWlCb0M7UUFFcEUsSUFBSTtZQUNGLG1DQUFtQztZQUNuQyxNQUFNLEVBQUV6QyxJQUFJLEVBQUVrQixLQUFLLEVBQUUsR0FBRyxNQUFNakMsbURBQVFBLENBQ25Da0MsSUFBSSxDQUFDLGlCQUNMdUIsTUFBTSxDQUFDRCxTQUNQcEIsRUFBRSxDQUFDLFdBQVc1QixLQUFLWSxFQUFFLEVBQ3JCZSxNQUFNLEdBQ05FLE1BQU07WUFFVE4sUUFBUUMsR0FBRyxDQUFDLGtCQUFrQjtnQkFBRWpCO2dCQUFNa0I7WUFBTTtZQUU1QyxJQUFJQSxPQUFPO2dCQUNULHVEQUF1RDtnQkFDdkQsSUFBSUEsTUFBTUssSUFBSSxLQUFLLFlBQVk7d0JBTWQ5QjtvQkFMZnVCLFFBQVFDLEdBQUcsQ0FBQztvQkFDWixNQUFNLEVBQUVqQixNQUFNd0IsVUFBVSxFQUFFTixPQUFPTyxXQUFXLEVBQUUsR0FBRyxNQUFNeEMsbURBQVFBLENBQzVEa0MsSUFBSSxDQUFDLGlCQUNMTyxNQUFNLENBQUM7d0JBQ05DLFNBQVNsQyxLQUFLWSxFQUFFO3dCQUNoQnVCLFdBQVduQyxDQUFBQSxpQkFBQUEsNEJBQUFBLHNCQUFBQSxLQUFNb0MsYUFBYSxjQUFuQnBDLDBDQUFBQSxvQkFBcUJtQyxTQUFTLEtBQUk7d0JBQzdDRSxNQUFNO3dCQUNOQyxxQkFBcUI7d0JBQ3JCLEdBQUdVLE9BQU87b0JBQ1osR0FDQ3JCLE1BQU0sR0FDTkUsTUFBTTtvQkFFVCxJQUFJRyxhQUFhO3dCQUNmVCxRQUFRRSxLQUFLLENBQUMsMkJBQTJCTzt3QkFDekMsT0FBTzs0QkFBRVAsT0FBT087d0JBQVk7b0JBQzlCLE9BQU87d0JBQ0xULFFBQVFDLEdBQUcsQ0FBQyxpQ0FBaUNPO3dCQUM3QzVCLFdBQVc0Qjt3QkFDWCxPQUFPOzRCQUFFTixPQUFPO3dCQUFLO29CQUN2QjtnQkFDRixPQUFPO29CQUNMRixRQUFRRSxLQUFLLENBQUMsMkJBQTJCQTtvQkFDekMsT0FBTzt3QkFBRUE7b0JBQU07Z0JBQ2pCO1lBQ0YsT0FBTztnQkFDTEYsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ2pCO2dCQUM3Q0osV0FBV0k7Z0JBQ1gsT0FBTztvQkFBRWtCLE9BQU87Z0JBQUs7WUFDdkI7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZEYsUUFBUUUsS0FBSyxDQUFDLHNDQUFzQ0E7WUFDcEQsT0FBTztnQkFBRUE7WUFBTTtRQUNqQjtJQUNGO0lBRUEsTUFBTVoscUJBQXFCLE9BQU9xQztRQUNoQyxJQUFJLENBQUNsRCxNQUFNO1FBRVgsSUFBSTtZQUNGLE1BQU1SLG1EQUFRQSxDQUNYa0MsSUFBSSxDQUFDLGlCQUNMdUIsTUFBTSxDQUFDO2dCQUNORSxXQUFXRDtnQkFDWEUsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ25DLEdBQ0MxQixFQUFFLENBQUMsV0FBVzVCLEtBQUtZLEVBQUU7UUFDMUIsRUFBRSxPQUFPYSxPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyxpQ0FBaUNBO1FBQ2pEO0lBQ0Y7SUFFQSxNQUFNOEIsUUFBUTtRQUNadkQ7UUFDQUU7UUFDQUU7UUFDQW1DO1FBQ0FJO1FBQ0FHO1FBQ0FDO1FBQ0FsQztJQUNGO0lBRUEscUJBQ0UsOERBQUNwQixZQUFZK0QsUUFBUTtRQUFDRCxPQUFPQTtrQkFDMUJ4RDs7Ozs7O0FBR1AsRUFBQztJQXBOWUQ7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29udGV4dHMvVXNlckNvbnRleHQudHN4P2Y1OTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFVzZXIgYXMgU3VwYWJhc2VVc2VyIH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuaW1wb3J0IHsgc3VwYWJhc2UsIFVzZXIsIFVzZXJQcm9maWxlIH0gZnJvbSAnLi4vbGliL3N1cGFiYXNlJ1xuXG5pbnRlcmZhY2UgVXNlckNvbnRleHRUeXBlIHtcbiAgdXNlcjogU3VwYWJhc2VVc2VyIHwgbnVsbFxuICBwcm9maWxlOiBVc2VyUHJvZmlsZSB8IG51bGxcbiAgbG9hZGluZzogYm9vbGVhblxuICBzaWduSW46IChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiBQcm9taXNlPHsgZXJyb3I6IGFueSB9PlxuICBzaWduVXA6IChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nLCBmdWxsTmFtZTogc3RyaW5nKSA9PiBQcm9taXNlPHsgZXJyb3I6IGFueSB9PlxuICBzaWduT3V0OiAoKSA9PiBQcm9taXNlPHZvaWQ+XG4gIHVwZGF0ZVByb2ZpbGU6ICh1cGRhdGVzOiBQYXJ0aWFsPFVzZXJQcm9maWxlPikgPT4gUHJvbWlzZTx7IGVycm9yOiBhbnkgfT5cbiAgdXBkYXRlT25saW5lU3RhdHVzOiAoaXNPbmxpbmU6IGJvb2xlYW4pID0+IFByb21pc2U8dm9pZD5cbn1cblxuY29uc3QgVXNlckNvbnRleHQgPSBjcmVhdGVDb250ZXh0PFVzZXJDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKVxuXG5leHBvcnQgY29uc3QgdXNlVXNlciA9ICgpID0+IHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoVXNlckNvbnRleHQpXG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZVVzZXIgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFVzZXJQcm92aWRlcicpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cblxuZXhwb3J0IGNvbnN0IFVzZXJQcm92aWRlcjogUmVhY3QuRkM8eyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxTdXBhYmFzZVVzZXIgfCBudWxsPihudWxsKVxuICBjb25zdCBbcHJvZmlsZSwgc2V0UHJvZmlsZV0gPSB1c2VTdGF0ZTxVc2VyUHJvZmlsZSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBHZXQgaW5pdGlhbCBzZXNzaW9uXG4gICAgY29uc3QgZ2V0SW5pdGlhbFNlc3Npb24gPSBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCB7IGRhdGE6IHsgc2Vzc2lvbiB9IH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFNlc3Npb24oKVxuICAgICAgc2V0VXNlcihzZXNzaW9uPy51c2VyID8/IG51bGwpXG5cbiAgICAgIGlmIChzZXNzaW9uPy51c2VyKSB7XG4gICAgICAgIGF3YWl0IGZldGNoVXNlclByb2ZpbGUoc2Vzc2lvbi51c2VyLmlkKVxuICAgICAgICBhd2FpdCB1cGRhdGVPbmxpbmVTdGF0dXModHJ1ZSlcbiAgICAgIH1cblxuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG5cbiAgICBnZXRJbml0aWFsU2Vzc2lvbigpXG5cbiAgICAvLyBMaXN0ZW4gZm9yIGF1dGggY2hhbmdlc1xuICAgIGNvbnN0IHsgZGF0YTogeyBzdWJzY3JpcHRpb24gfSB9ID0gc3VwYWJhc2UuYXV0aC5vbkF1dGhTdGF0ZUNoYW5nZShcbiAgICAgIGFzeW5jIChldmVudCwgc2Vzc2lvbikgPT4ge1xuICAgICAgICBzZXRVc2VyKHNlc3Npb24/LnVzZXIgPz8gbnVsbClcblxuICAgICAgICBpZiAoc2Vzc2lvbj8udXNlcikge1xuICAgICAgICAgIGF3YWl0IGZldGNoVXNlclByb2ZpbGUoc2Vzc2lvbi51c2VyLmlkKVxuICAgICAgICAgIGF3YWl0IHVwZGF0ZU9ubGluZVN0YXR1cyh0cnVlKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNldFByb2ZpbGUobnVsbClcbiAgICAgICAgfVxuXG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgICB9XG4gICAgKVxuXG4gICAgLy8gVXBkYXRlIG9ubGluZSBzdGF0dXMgd2hlbiB1c2VyIGxlYXZlc1xuICAgIGNvbnN0IGhhbmRsZUJlZm9yZVVubG9hZCA9ICgpID0+IHtcbiAgICAgIGlmICh1c2VyKSB7XG4gICAgICAgIHVwZGF0ZU9ubGluZVN0YXR1cyhmYWxzZSlcbiAgICAgIH1cbiAgICB9XG5cbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignYmVmb3JldW5sb2FkJywgaGFuZGxlQmVmb3JlVW5sb2FkKVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpXG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignYmVmb3JldW5sb2FkJywgaGFuZGxlQmVmb3JlVW5sb2FkKVxuICAgIH1cbiAgfSwgW3VzZXJdKVxuXG4gIGNvbnN0IGZldGNoVXNlclByb2ZpbGUgPSBhc3luYyAodXNlcklkOiBzdHJpbmcpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ0ZldGNoaW5nIHVzZXIgcHJvZmlsZSBmb3IgdXNlcklkOicsIHVzZXJJZClcbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKCd1c2VyX3Byb2ZpbGVzJylcbiAgICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgIGNvbnNvbGUubG9nKCdQcm9maWxlIGZldGNoIHJlc3VsdDonLCB7IGRhdGEsIGVycm9yIH0pXG5cbiAgICAgIGlmIChlcnJvciAmJiBlcnJvci5jb2RlICE9PSAnUEdSU1QxMTYnKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHVzZXIgcHJvZmlsZTonLCBlcnJvcilcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIGlmIChkYXRhKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdTZXR0aW5nIHByb2ZpbGUgZGF0YTonLCBkYXRhKVxuICAgICAgICBzZXRQcm9maWxlKGRhdGEpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBDcmVhdGUgZGVmYXVsdCBwcm9maWxlIGlmIGl0IGRvZXNuJ3QgZXhpc3RcbiAgICAgICAgY29uc29sZS5sb2coJ0NyZWF0aW5nIG5ldyBwcm9maWxlIGZvciB1c2VyOicsIHVzZXJJZClcbiAgICAgICAgY29uc3QgeyBkYXRhOiBuZXdQcm9maWxlLCBlcnJvcjogY3JlYXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oJ3VzZXJfcHJvZmlsZXMnKVxuICAgICAgICAgIC5pbnNlcnQoe1xuICAgICAgICAgICAgdXNlcl9pZDogdXNlcklkLFxuICAgICAgICAgICAgZnVsbF9uYW1lOiB1c2VyPy51c2VyX21ldGFkYXRhPy5mdWxsX25hbWUgfHwgJ1VzZXInLFxuICAgICAgICAgICAgcGxhbjogJ2ZyZWUnLFxuICAgICAgICAgICAgc3Vic2NyaXB0aW9uX3N0YXR1czogJ2luYWN0aXZlJ1xuICAgICAgICAgIH0pXG4gICAgICAgICAgLnNlbGVjdCgpXG4gICAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgICAgY29uc29sZS5sb2coJ1Byb2ZpbGUgY3JlYXRpb24gcmVzdWx0OicsIHsgbmV3UHJvZmlsZSwgY3JlYXRlRXJyb3IgfSlcblxuICAgICAgICBpZiAoY3JlYXRlRXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyB1c2VyIHByb2ZpbGU6JywgY3JlYXRlRXJyb3IpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ1NldHRpbmcgbmV3IHByb2ZpbGU6JywgbmV3UHJvZmlsZSlcbiAgICAgICAgICBzZXRQcm9maWxlKG5ld1Byb2ZpbGUpXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gZmV0Y2hVc2VyUHJvZmlsZTonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICBjb25zdCBzaWduSW4gPSBhc3luYyAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguc2lnbkluV2l0aFBhc3N3b3JkKHtcbiAgICAgIGVtYWlsLFxuICAgICAgcGFzc3dvcmQsXG4gICAgfSlcbiAgICByZXR1cm4geyBlcnJvciB9XG4gIH1cblxuICBjb25zdCBzaWduVXAgPSBhc3luYyAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZywgZnVsbE5hbWU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguc2lnblVwKHtcbiAgICAgIGVtYWlsLFxuICAgICAgcGFzc3dvcmQsXG4gICAgICBvcHRpb25zOiB7XG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICBmdWxsX25hbWU6IGZ1bGxOYW1lLFxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSlcbiAgICByZXR1cm4geyBlcnJvciB9XG4gIH1cblxuICBjb25zdCBzaWduT3V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIGF3YWl0IHVwZGF0ZU9ubGluZVN0YXR1cyhmYWxzZSlcbiAgICBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25PdXQoKVxuICB9XG5cbiAgY29uc3QgdXBkYXRlUHJvZmlsZSA9IGFzeW5jICh1cGRhdGVzOiBQYXJ0aWFsPFVzZXJQcm9maWxlPikgPT4ge1xuICAgIGlmICghdXNlcikgcmV0dXJuIHsgZXJyb3I6ICdObyB1c2VyIGxvZ2dlZCBpbicgfVxuXG4gICAgY29uc29sZS5sb2coJ1VwZGF0aW5nIHByb2ZpbGUgZm9yIHVzZXI6JywgdXNlci5pZCwgJ3dpdGggdXBkYXRlczonLCB1cGRhdGVzKVxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIEZpcnN0LCB0cnkgdG8gdXBkYXRlIHRoZSBwcm9maWxlXG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAgIC51cGRhdGUodXBkYXRlcylcbiAgICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgICAgLnNlbGVjdCgpXG4gICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICBjb25zb2xlLmxvZygnVXBkYXRlIHJlc3VsdDonLCB7IGRhdGEsIGVycm9yIH0pXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICAvLyBJZiB0aGUgcHJvZmlsZSBkb2Vzbid0IGV4aXN0LCB0cnkgdG8gY3JlYXRlIGl0IGZpcnN0XG4gICAgICAgIGlmIChlcnJvci5jb2RlID09PSAnUEdSU1QxMTYnKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ1Byb2ZpbGUgbm90IGZvdW5kLCBjcmVhdGluZyBuZXcgcHJvZmlsZS4uLicpXG4gICAgICAgICAgY29uc3QgeyBkYXRhOiBuZXdQcm9maWxlLCBlcnJvcjogY3JlYXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAgICAgICAuaW5zZXJ0KHtcbiAgICAgICAgICAgICAgdXNlcl9pZDogdXNlci5pZCxcbiAgICAgICAgICAgICAgZnVsbF9uYW1lOiB1c2VyPy51c2VyX21ldGFkYXRhPy5mdWxsX25hbWUgfHwgJ1VzZXInLFxuICAgICAgICAgICAgICBwbGFuOiAnZnJlZScsXG4gICAgICAgICAgICAgIHN1YnNjcmlwdGlvbl9zdGF0dXM6ICdpbmFjdGl2ZScsXG4gICAgICAgICAgICAgIC4uLnVwZGF0ZXNcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAuc2VsZWN0KClcbiAgICAgICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICAgICAgaWYgKGNyZWF0ZUVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBwcm9maWxlOicsIGNyZWF0ZUVycm9yKVxuICAgICAgICAgICAgcmV0dXJuIHsgZXJyb3I6IGNyZWF0ZUVycm9yIH1cbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ1Byb2ZpbGUgY3JlYXRlZCBzdWNjZXNzZnVsbHk6JywgbmV3UHJvZmlsZSlcbiAgICAgICAgICAgIHNldFByb2ZpbGUobmV3UHJvZmlsZSlcbiAgICAgICAgICAgIHJldHVybiB7IGVycm9yOiBudWxsIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgcHJvZmlsZTonLCBlcnJvcilcbiAgICAgICAgICByZXR1cm4geyBlcnJvciB9XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdQcm9maWxlIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5OicsIGRhdGEpXG4gICAgICAgIHNldFByb2ZpbGUoZGF0YSlcbiAgICAgICAgcmV0dXJuIHsgZXJyb3I6IG51bGwgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdVbmV4cGVjdGVkIGVycm9yIGluIHVwZGF0ZVByb2ZpbGU6JywgZXJyb3IpXG4gICAgICByZXR1cm4geyBlcnJvciB9XG4gICAgfVxuICB9XG5cbiAgY29uc3QgdXBkYXRlT25saW5lU3RhdHVzID0gYXN5bmMgKGlzT25saW5lOiBib29sZWFuKSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgIGlzX29ubGluZTogaXNPbmxpbmUsXG4gICAgICAgICAgbGFzdF9zZWVuOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgfSlcbiAgICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgb25saW5lIHN0YXR1czonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICBjb25zdCB2YWx1ZSA9IHtcbiAgICB1c2VyLFxuICAgIHByb2ZpbGUsXG4gICAgbG9hZGluZyxcbiAgICBzaWduSW4sXG4gICAgc2lnblVwLFxuICAgIHNpZ25PdXQsXG4gICAgdXBkYXRlUHJvZmlsZSxcbiAgICB1cGRhdGVPbmxpbmVTdGF0dXNcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPFVzZXJDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9Vc2VyQ29udGV4dC5Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInN1cGFiYXNlIiwiVXNlckNvbnRleHQiLCJ1bmRlZmluZWQiLCJ1c2VVc2VyIiwiY29udGV4dCIsIkVycm9yIiwiVXNlclByb3ZpZGVyIiwiY2hpbGRyZW4iLCJ1c2VyIiwic2V0VXNlciIsInByb2ZpbGUiLCJzZXRQcm9maWxlIiwibG9hZGluZyIsInNldExvYWRpbmciLCJnZXRJbml0aWFsU2Vzc2lvbiIsImRhdGEiLCJzZXNzaW9uIiwiYXV0aCIsImdldFNlc3Npb24iLCJmZXRjaFVzZXJQcm9maWxlIiwiaWQiLCJ1cGRhdGVPbmxpbmVTdGF0dXMiLCJzdWJzY3JpcHRpb24iLCJvbkF1dGhTdGF0ZUNoYW5nZSIsImV2ZW50IiwiaGFuZGxlQmVmb3JlVW5sb2FkIiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsInVuc3Vic2NyaWJlIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInVzZXJJZCIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsImZyb20iLCJzZWxlY3QiLCJlcSIsInNpbmdsZSIsImNvZGUiLCJuZXdQcm9maWxlIiwiY3JlYXRlRXJyb3IiLCJpbnNlcnQiLCJ1c2VyX2lkIiwiZnVsbF9uYW1lIiwidXNlcl9tZXRhZGF0YSIsInBsYW4iLCJzdWJzY3JpcHRpb25fc3RhdHVzIiwic2lnbkluIiwiZW1haWwiLCJwYXNzd29yZCIsInNpZ25JbldpdGhQYXNzd29yZCIsInNpZ25VcCIsImZ1bGxOYW1lIiwib3B0aW9ucyIsInNpZ25PdXQiLCJ1cGRhdGVQcm9maWxlIiwidXBkYXRlcyIsInVwZGF0ZSIsImlzT25saW5lIiwiaXNfb25saW5lIiwibGFzdF9zZWVuIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwidmFsdWUiLCJQcm92aWRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./contexts/UserContext.tsx\n"));

/***/ })

});