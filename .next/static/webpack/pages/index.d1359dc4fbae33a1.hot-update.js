"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @supabase/auth-helpers-react */ \"./node_modules/@supabase/auth-helpers-react/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,TrendingUp,Users,Zap!=!lucide-react */ \"__barrel_optimize__?names=Calendar,TrendingUp,Users,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n// pages/index.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst HomePage = ()=>{\n    _s();\n    const user = (0,_supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        upcomingTweets: 0,\n        followers: 0,\n        engagementRate: 0,\n        contentScore: 0\n    });\n    const [upcomingPosts, setUpcomingPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\"\n        },\n        border: \"#E5E7EB\",\n        surface: \"#FFFFFF\",\n        background: \"#FFF8F3\"\n    };\n    // Load dashboard data\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (user === null || user === void 0 ? void 0 : user.id) {\n            loadDashboardData();\n        }\n    }, [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    const loadDashboardData = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.id)) return;\n        console.log(\"Loading dashboard data for user:\", user.id);\n        try {\n            // Load scheduled posts\n            console.log(\"Fetching scheduled posts...\");\n            const scheduledResponse = await fetch(\"/api/twitter/scheduled?userId=\".concat(user.id));\n            console.log(\"Scheduled response status:\", scheduledResponse.status);\n            if (scheduledResponse.ok) {\n                const scheduledData = await scheduledResponse.json();\n                console.log(\"Scheduled data:\", scheduledData);\n                setUpcomingPosts(scheduledData.scheduledPosts || []);\n                setStats((prev)=>{\n                    var _scheduledData_scheduledPosts;\n                    return {\n                        ...prev,\n                        upcomingTweets: ((_scheduledData_scheduledPosts = scheduledData.scheduledPosts) === null || _scheduledData_scheduledPosts === void 0 ? void 0 : _scheduledData_scheduledPosts.length) || 0\n                    };\n                });\n            } else {\n                console.error(\"Failed to fetch scheduled posts:\", await scheduledResponse.text());\n            }\n            // Load posted content for analytics\n            console.log(\"Fetching posted content...\");\n            const postedResponse = await fetch(\"/api/twitter/posted?userId=\".concat(user.id));\n            console.log(\"Posted response status:\", postedResponse.status);\n            if (postedResponse.ok) {\n                const postedData = await postedResponse.json();\n                console.log(\"Posted data:\", postedData);\n                const posts = postedData.postedContent || [];\n                // Calculate engagement rate (mock calculation)\n                const engagementRate = posts.length > 0 ? Math.floor(Math.random() * 30) + 15 : 15;\n                const contentScore = posts.length > 0 ? (Math.random() * 2 + 8).toFixed(1) : \"8.5\";\n                setStats((prev)=>({\n                        ...prev,\n                        followers: Math.floor(Math.random() * 1000) + 500,\n                        engagementRate,\n                        contentScore: parseFloat(contentScore)\n                    }));\n            } else {\n                console.error(\"Failed to fetch posted content:\", await postedResponse.text());\n                // Set default stats even if API fails\n                setStats((prev)=>({\n                        ...prev,\n                        followers: 847,\n                        engagementRate: 24,\n                        contentScore: 8.7\n                    }));\n            }\n            // Generate AI suggestions\n            setSuggestions([\n                \"Share a behind-the-scenes story about your workflow\",\n                \"Create a thread about productivity tips\",\n                \"Post about recent industry trends\",\n                \"Engage with your community through polls\"\n            ]);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            // Set default stats on error\n            setStats((prev)=>({\n                    ...prev,\n                    followers: 847,\n                    engagementRate: 24,\n                    contentScore: 8.7\n                }));\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"32px\",\n            height: \"100vh\",\n            overflow: \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"32px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.5px\",\n                            marginBottom: \"8px\"\n                        },\n                        children: \"Briefing Room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            fontSize: \"16px\",\n                            margin: 0,\n                            fontWeight: \"400\"\n                        },\n                        children: \"Your daily mission control center\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: \"800px\",\n                    margin: \"0 auto\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            marginBottom: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"16px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        marginBottom: \"12px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                color: colors.text.primary,\n                                                margin: 0,\n                                                fontSize: \"20px\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"Today's Mission\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"4px 8px\",\n                                                background: \"\".concat(colors.primary, \"15\"),\n                                                borderRadius: \"6px\",\n                                                color: colors.primary,\n                                                fontSize: \"11px\",\n                                                fontWeight: \"600\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.5px\"\n                                            },\n                                            children: \"AI Generated\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: colors.text.secondary,\n                                        fontSize: \"16px\",\n                                        lineHeight: \"1.5\",\n                                        margin: 0,\n                                        marginBottom: \"20px\"\n                                    },\n                                    children: \"Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"grid\",\n                                        gridTemplateColumns: \"repeat(4, 1fr)\",\n                                        gap: \"16px\",\n                                        marginBottom: \"20px\"\n                                    },\n                                    children: [\n                                        {\n                                            label: \"Upcoming Tweets\",\n                                            value: loading ? \"...\" : stats.upcomingTweets.toString(),\n                                            icon: _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Calendar,\n                                            color: colors.primary\n                                        },\n                                        {\n                                            label: \"Followers\",\n                                            value: loading ? \"...\" : stats.followers.toLocaleString(),\n                                            icon: _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Users,\n                                            color: \"#10B981\"\n                                        },\n                                        {\n                                            label: \"Engagement Rate\",\n                                            value: loading ? \"...\" : \"\".concat(stats.engagementRate, \"%\"),\n                                            icon: _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.TrendingUp,\n                                            color: \"#3B82F6\"\n                                        },\n                                        {\n                                            label: \"Content Score\",\n                                            value: loading ? \"...\" : \"\".concat(stats.contentScore, \"/10\"),\n                                            icon: _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap,\n                                            color: \"#8B5CF6\"\n                                        }\n                                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: colors.surface,\n                                                borderRadius: \"12px\",\n                                                padding: \"20px\",\n                                                textAlign: \"center\",\n                                                border: \"1px solid \".concat(colors.border),\n                                                boxShadow: \"0 2px 8px rgba(0, 0, 0, 0.04)\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"center\",\n                                                        marginBottom: \"8px\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                        size: 20,\n                                                        color: stat.color\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"700\",\n                                                        color: colors.text.primary,\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.text.tertiary,\n                                                        fontWeight: \"500\"\n                                                    },\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        gap: \"12px\",\n                                        flexWrap: \"wrap\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/meeting\",\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"12px 20px\",\n                                                    background: colors.primary,\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Join Call\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                padding: \"12px 20px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                border: \"1px solid \".concat(colors.border),\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\"\n                                            },\n                                            children: \"Ask Mentor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/tweet-center\",\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"12px 20px\",\n                                                    background: colors.surface,\n                                                    color: colors.text.primary,\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Create Content\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    upcomingPosts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            marginBottom: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    marginBottom: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Calendar, {\n                                        size: 20,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            margin: 0,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"Upcoming Tweets\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"12px\"\n                                },\n                                children: upcomingPosts.slice(0, 3).map((post, index)=>{\n                                    var _post_content;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px\",\n                                            background: colors.background,\n                                            borderRadius: \"12px\",\n                                            border: \"1px solid \".concat(colors.border)\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                justifyContent: \"space-between\",\n                                                alignItems: \"flex-start\",\n                                                marginBottom: \"8px\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: colors.text.primary,\n                                                        lineHeight: \"1.4\",\n                                                        flex: 1\n                                                    },\n                                                    children: [\n                                                        (_post_content = post.content) === null || _post_content === void 0 ? void 0 : _post_content.substring(0, 100),\n                                                        \"...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.text.tertiary,\n                                                        marginLeft: \"12px\",\n                                                        whiteSpace: \"nowrap\"\n                                                    },\n                                                    children: new Date(post.scheduled_time).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            marginBottom: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    marginBottom: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap, {\n                                        size: 20,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            margin: 0,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"AI Suggestions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"12px\"\n                                },\n                                children: suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px\",\n                                            background: colors.background,\n                                            borderRadius: \"12px\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: \"8px\",\n                                                    height: \"8px\",\n                                                    borderRadius: \"50%\",\n                                                    background: colors.primary\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.primary,\n                                                    flex: 1\n                                                },\n                                                children: suggestion\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/tweet-center\",\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        padding: \"6px 12px\",\n                                                        background: colors.primary,\n                                                        color: \"white\",\n                                                        border: \"none\",\n                                                        borderRadius: \"6px\",\n                                                        fontSize: \"12px\",\n                                                        fontWeight: \"500\",\n                                                        cursor: \"pointer\"\n                                                    },\n                                                    children: \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"flex-start\",\n                                gap: \"16px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"48px\",\n                                        height: \"48px\",\n                                        borderRadius: \"12px\",\n                                        background: \"\".concat(colors.primary, \"15\"),\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\",\n                                        flexShrink: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"24px\",\n                                                height: \"24px\",\n                                                borderRadius: \"6px\",\n                                                background: colors.primary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"2px\",\n                                                right: \"2px\",\n                                                width: \"12px\",\n                                                height: \"12px\",\n                                                borderRadius: \"50%\",\n                                                background: \"#00E676\",\n                                                border: \"2px solid white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: colors.text.primary,\n                                                margin: 0,\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                marginBottom: \"8px\"\n                                            },\n                                            children: \"AI Mentor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: colors.text.secondary,\n                                                margin: 0,\n                                                fontSize: \"15px\",\n                                                lineHeight: \"1.5\"\n                                            },\n                                            children: \"Ready to help you create content that resonates. What's on your mind today?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HomePage, \"QgDVr8VjFc6+rV3us+rtn0eYZLg=\", false, function() {\n    return [\n        _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = HomePage;\nHomePage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 505,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});