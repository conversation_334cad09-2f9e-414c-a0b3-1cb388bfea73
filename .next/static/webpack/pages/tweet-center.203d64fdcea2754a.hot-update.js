"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot!=!lucide-react */ \"__barrel_optimize__?names=Bot!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n// pages/tweet-center.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst TweetCenterPage = ()=>{\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showAgentEInput, setShowAgentEInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [agentEPrompt, setAgentEPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showOptionsDropdown, setShowOptionsDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [autoRunMode, setAutoRunMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        borderHover: \"#E8D5C4\",\n        surface: \"#FFFFFF\",\n        surfaceHover: \"#F9F7F4\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Real AI prediction based on content context\n    const generateContextualSuggestion = (text)=>{\n        if (text.length < 10) return \"\";\n        try {\n            var _text_split_pop;\n            // Simple prediction logic based on sentence patterns\n            const lastSentence = ((_text_split_pop = text.split(\".\").pop()) === null || _text_split_pop === void 0 ? void 0 : _text_split_pop.trim()) || text;\n            // If sentence seems incomplete, suggest completion\n            if (lastSentence.length > 0) {\n                // Sports context\n                if (lastSentence.toLowerCase().includes(\"sports\") || lastSentence.toLowerCase().includes(\"game\") || lastSentence.toLowerCase().includes(\"team\")) {\n                    const sportsSuggestions = [\n                        \" requires dedication and consistent practice\",\n                        \" teaches us valuable life lessons\",\n                        \" brings people together like nothing else\",\n                        \" is more than just competition\"\n                    ];\n                    return sportsSuggestions[Math.floor(Math.random() * sportsSuggestions.length)];\n                }\n                // Tech context\n                if (lastSentence.toLowerCase().includes(\"technology\") || lastSentence.toLowerCase().includes(\"coding\") || lastSentence.toLowerCase().includes(\"software\")) {\n                    const techSuggestions = [\n                        \" is evolving faster than ever before\",\n                        \" has the power to solve real problems\",\n                        \" requires continuous learning and adaptation\",\n                        \" should be accessible to everyone\"\n                    ];\n                    return techSuggestions[Math.floor(Math.random() * techSuggestions.length)];\n                }\n                // Business context\n                if (lastSentence.toLowerCase().includes(\"business\") || lastSentence.toLowerCase().includes(\"startup\") || lastSentence.toLowerCase().includes(\"entrepreneur\")) {\n                    const businessSuggestions = [\n                        \" is about solving problems for people\",\n                        \" requires patience and persistence\",\n                        \" success comes from understanding your customers\",\n                        \" failure is just feedback in disguise\"\n                    ];\n                    return businessSuggestions[Math.floor(Math.random() * businessSuggestions.length)];\n                }\n                // General sentence completion based on common patterns\n                if (lastSentence.endsWith(\"I think\") || lastSentence.endsWith(\"I believe\")) {\n                    return \" that consistency beats perfection every time\";\n                }\n                if (lastSentence.includes(\"The key to\")) {\n                    return \" success is taking action despite uncertainty\";\n                }\n                if (lastSentence.includes(\"What I learned\")) {\n                    return \" is that small steps lead to big changes\";\n                }\n                // Default contextual completions\n                const generalSuggestions = [\n                    \" and here's why that matters\",\n                    \" - let me explain\",\n                    \" in my experience\",\n                    \" based on what I've seen\",\n                    \" and the results speak for themselves\"\n                ];\n                return generalSuggestions[Math.floor(Math.random() * generalSuggestions.length)];\n            }\n            return \"\";\n        } catch (error) {\n            console.error(\"Error generating suggestion:\", error);\n            return \"\";\n        }\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    const handleAgentESubmit = (e)=>{\n        e.preventDefault();\n        if (!agentEPrompt.trim()) return;\n        // Generate content based on the prompt\n        const generatedContent = generateContentFromPrompt(agentEPrompt);\n        setContent(generatedContent);\n        setAgentEPrompt(\"\");\n        setShowAgentEInput(false);\n    };\n    const generateContentFromPrompt = (prompt)=>{\n        const lowerPrompt = prompt.toLowerCase();\n        // Different content types based on prompt\n        if (lowerPrompt.includes(\"thread\") || lowerPrompt.includes(\"twitter thread\")) {\n            return \"Here's a thread about \".concat(prompt.replace(/thread|twitter thread/gi, \"\").trim(), \":\\n\\n1/ The key to understanding this topic is...\\n\\n2/ Most people think...\\n\\n3/ But here's what actually works...\");\n        }\n        if (lowerPrompt.includes(\"tips\") || lowerPrompt.includes(\"advice\")) {\n            return \"\".concat(prompt.charAt(0).toUpperCase() + prompt.slice(1), \":\\n\\n• Focus on the fundamentals first\\n• Consistency beats perfection\\n• Learn from others who've succeeded\\n• Take action despite uncertainty\");\n        }\n        if (lowerPrompt.includes(\"story\") || lowerPrompt.includes(\"experience\")) {\n            return \"Here's my experience with \".concat(prompt.replace(/story|experience/gi, \"\").trim(), \":\\n\\nIt started when I realized that most advice online was generic. I needed something that actually worked in the real world...\");\n        }\n        // Default content generation\n        return \"\".concat(prompt.charAt(0).toUpperCase() + prompt.slice(1), \".\\n\\nHere's what I've learned from years of experience: the biggest difference between success and failure isn't talent or luck—it's consistency.\\n\\nMost people give up right before they would have succeeded.\");\n    };\n    // Auto-run mode - automatically generates tweets\n    const generateAutoTweet = ()=>{\n        const topics = [\n            \"productivity tips for entrepreneurs\",\n            \"lessons learned from building a startup\",\n            \"the importance of consistency in business\",\n            \"how to stay motivated during tough times\",\n            \"building habits that stick\",\n            \"the power of compound growth\",\n            \"why most people give up too early\",\n            \"simple strategies for better focus\"\n        ];\n        const randomTopic = topics[Math.floor(Math.random() * topics.length)];\n        const generatedContent = generateContentFromPrompt(randomTopic);\n        setContent(generatedContent);\n    };\n    // Auto-run effect\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (autoRunMode && !content) {\n            const timer = setTimeout(()=>{\n                generateAutoTweet();\n            }, 1000);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        autoRunMode,\n        content\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.paper,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n        },\n        className: \"jsx-3f45dd3580662bfa\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    marginBottom: \"40px\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.4px\",\n                            fontFamily: \"SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowOptionsDropdown(!showOptionsDropdown),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"8px 16px\",\n                                    background: colors.surface,\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"12px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    color: colors.text.primary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    \"Options\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            transform: showOptionsDropdown ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                            transition: \"transform 0.2s ease\",\n                                            fontSize: \"12px\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: \"▼\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, undefined),\n                            showOptionsDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"100%\",\n                                    right: 0,\n                                    marginTop: \"8px\",\n                                    background: \"white\",\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"12px\",\n                                    boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.12)\",\n                                    padding: \"12px\",\n                                    minWidth: \"220px\",\n                                    zIndex: 1000\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            padding: \"8px 0\",\n                                            borderBottom: \"1px solid \".concat(colors.border)\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.primary\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: \"AI Predictions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setAiEnabled(!aiEnabled),\n                                                style: {\n                                                    width: \"40px\",\n                                                    height: \"20px\",\n                                                    borderRadius: \"10px\",\n                                                    border: \"none\",\n                                                    background: aiEnabled ? colors.primary : colors.border,\n                                                    position: \"relative\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"white\",\n                                                        position: \"absolute\",\n                                                        top: \"2px\",\n                                                        left: aiEnabled ? \"22px\" : \"2px\",\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    className: \"jsx-3f45dd3580662bfa\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            padding: \"8px 0\",\n                                            borderBottom: \"1px solid \".concat(colors.border)\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            color: colors.text.primary,\n                                                            fontWeight: \"500\"\n                                                        },\n                                                        className: \"jsx-3f45dd3580662bfa\",\n                                                        children: \"Auto-Run Mode ✨\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"12px\",\n                                                            color: colors.text.tertiary,\n                                                            marginTop: \"2px\"\n                                                        },\n                                                        className: \"jsx-3f45dd3580662bfa\",\n                                                        children: \"Automatically generates tweets\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setAutoRunMode(!autoRunMode);\n                                                    if (!autoRunMode) {\n                                                        setContent(\"\"); // Clear content to trigger auto-generation\n                                                    }\n                                                },\n                                                style: {\n                                                    width: \"40px\",\n                                                    height: \"20px\",\n                                                    borderRadius: \"10px\",\n                                                    border: \"none\",\n                                                    background: autoRunMode ? \"#10B981\" : colors.border,\n                                                    position: \"relative\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\",\n                                                    boxShadow: autoRunMode ? \"0 0 12px rgba(16, 185, 129, 0.4)\" : \"none\"\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"white\",\n                                                        position: \"absolute\",\n                                                        top: \"2px\",\n                                                        left: autoRunMode ? \"22px\" : \"2px\",\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    className: \"jsx-3f45dd3580662bfa\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            padding: \"8px 0\",\n                                            borderBottom: \"1px solid \".concat(colors.border)\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.primary\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: \"Thread Mode\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setFormatMode(formatMode === \"thread\" ? \"single\" : \"thread\"),\n                                                style: {\n                                                    width: \"40px\",\n                                                    height: \"20px\",\n                                                    borderRadius: \"10px\",\n                                                    border: \"none\",\n                                                    background: formatMode === \"thread\" ? colors.primary : colors.border,\n                                                    position: \"relative\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"white\",\n                                                        position: \"absolute\",\n                                                        top: \"2px\",\n                                                        left: formatMode === \"thread\" ? \"22px\" : \"2px\",\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    className: \"jsx-3f45dd3580662bfa\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"8px 0\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowAgentEInput(true);\n                                                setShowOptionsDropdown(false);\n                                            },\n                                            style: {\n                                                width: \"100%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"10px 16px\",\n                                                background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                border: \"none\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                color: \"white\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Ask Agent E\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    background: \"transparent\",\n                    position: \"relative\",\n                    minHeight: \"500px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            position: \"relative\",\n                            padding: \"40px 60px\",\n                            minHeight: \"450px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            showSuggestion && aiEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"40px\",\n                                    left: \"60px\",\n                                    right: \"60px\",\n                                    bottom: \"40px\",\n                                    pointerEvents: \"none\",\n                                    zIndex: 1,\n                                    overflow: \"hidden\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"20px\",\n                                        lineHeight: \"1.7\",\n                                        color: \"transparent\",\n                                        whiteSpace: \"pre-wrap\",\n                                        wordWrap: \"break-word\",\n                                        letterSpacing: \"0.3px\",\n                                        fontWeight: \"400\"\n                                    },\n                                    className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                    children: [\n                                        content,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#9CA3AF\",\n                                                opacity: 0.6,\n                                                fontStyle: \"normal\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: aiSuggestion\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: content,\n                                onChange: (e)=>setContent(e.target.value),\n                                onKeyDown: handleTabPress,\n                                placeholder: aiEnabled ? \"Start writing...\" : \"What's on your mind?\",\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    minHeight: \"400px\",\n                                    padding: \"0\",\n                                    border: \"none\",\n                                    background: \"transparent\",\n                                    fontSize: \"20px\",\n                                    lineHeight: \"1.7\",\n                                    color: colors.text.primary,\n                                    resize: \"none\",\n                                    outline: \"none\",\n                                    letterSpacing: \"0.3px\",\n                                    position: \"relative\",\n                                    zIndex: 2,\n                                    fontWeight: \"400\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            padding: \"0 60px 40px\",\n                            marginTop: \"20px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"400\",\n                                    opacity: 0.7\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                children: [\n                                    content.length,\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setContent(\"\"),\n                                        style: {\n                                            padding: \"12px 24px\",\n                                            background: \"transparent\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            borderRadius: \"10px\",\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = colors.surfaceHover;\n                                            target.style.borderColor = colors.borderHover;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = \"transparent\";\n                                            target.style.borderColor = colors.border;\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Save Draft\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // Publish logic here\n                                            console.log(\"Publishing:\", content);\n                                        },\n                                        style: {\n                                            padding: \"12px 28px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            border: \"none\",\n                                            borderRadius: \"10px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(-1px)\";\n                                            target.style.boxShadow = \"0 6px 16px \".concat(colors.primary, \"40\");\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(0)\";\n                                            target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"30\");\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Publish\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 9\n                    }, undefined),\n                    showAgentEInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"fixed\",\n                            bottom: \"24px\",\n                            left: \"50%\",\n                            transform: \"translateX(-50%)\",\n                            width: \"600px\",\n                            height: \"48px\",\n                            background: \"white\",\n                            borderRadius: \"24px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            padding: \"0 20px\",\n                            boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05)\",\n                            zIndex: 1000,\n                            border: \"2px solid \".concat(colors.primary)\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                size: 18,\n                                color: colors.primary,\n                                style: {\n                                    marginRight: \"12px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleAgentESubmit,\n                                style: {\n                                    flex: 1,\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: agentEPrompt,\n                                        onChange: (e)=>setAgentEPrompt(e.target.value),\n                                        placeholder: \"Ask Agent E to write something for you...\",\n                                        autoFocus: true,\n                                        style: {\n                                            flex: 1,\n                                            border: \"none\",\n                                            outline: \"none\",\n                                            fontSize: \"14px\",\n                                            color: colors.text.primary,\n                                            background: \"transparent\",\n                                            fontWeight: \"400\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        style: {\n                                            background: \"none\",\n                                            border: \"none\",\n                                            cursor: \"pointer\",\n                                            padding: \"4px\",\n                                            marginLeft: \"8px\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: \"↵\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAgentEInput(false),\n                                style: {\n                                    background: \"none\",\n                                    border: \"none\",\n                                    cursor: \"pointer\",\n                                    padding: \"4px\",\n                                    marginLeft: \"8px\",\n                                    color: colors.text.tertiary\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 590,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"3f45dd3580662bfa\",\n                children: \"@-webkit-keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);transform:scale(1.2)}}@-moz-keyframes pulse{0%,100%{opacity:.8;-moz-transform:scale(1);transform:scale(1)}50%{opacity:1;-moz-transform:scale(1.2);transform:scale(1.2)}}@-o-keyframes pulse{0%,100%{opacity:.8;-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-o-transform:scale(1.2);transform:scale(1.2)}}@keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-o-transform:scale(1.2);transform:scale(1.2)}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TweetCenterPage, \"7QuKRY1laqlm1+LZN0E84hfCivE=\");\n_c = TweetCenterPage;\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 668,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (TweetCenterPage);\nvar _c;\n$RefreshReg$(_c, \"TweetCenterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n"));

/***/ })

});