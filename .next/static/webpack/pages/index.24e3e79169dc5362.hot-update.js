"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @supabase/auth-helpers-react */ \"./node_modules/@supabase/auth-helpers-react/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,TrendingUp,Users,Zap!=!lucide-react */ \"__barrel_optimize__?names=Calendar,TrendingUp,Users,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n// pages/index.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst HomePage = ()=>{\n    _s();\n    const user = (0,_supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        upcomingTweets: 0,\n        followers: 0,\n        engagementRate: 0,\n        contentScore: 0\n    });\n    const [upcomingPosts, setUpcomingPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\"\n        },\n        border: \"#E5E7EB\",\n        surface: \"#FFFFFF\",\n        background: \"#FFF8F3\"\n    };\n    // Load dashboard data\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (user === null || user === void 0 ? void 0 : user.id) {\n            loadDashboardData();\n        } else {\n            // Show demo data for non-authenticated users\n            setStats({\n                upcomingTweets: 3,\n                followers: 847,\n                engagementRate: 24,\n                contentScore: 8.7\n            });\n            setSuggestions([\n                \"Share a behind-the-scenes story about your workflow\",\n                \"Create a thread about productivity tips\",\n                \"Post about recent industry trends\",\n                \"Engage with your community through polls\"\n            ]);\n            setLoading(false);\n        }\n    }, [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    const loadDashboardData = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.id)) return;\n        console.log(\"Loading dashboard data for user:\", user.id);\n        console.log(\"User object:\", user);\n        try {\n            // Load scheduled posts\n            console.log(\"Fetching scheduled posts...\");\n            const scheduledResponse = await fetch(\"/api/twitter/scheduled?userId=\".concat(user.id));\n            console.log(\"Scheduled response status:\", scheduledResponse.status);\n            if (scheduledResponse.ok) {\n                const scheduledData = await scheduledResponse.json();\n                console.log(\"Scheduled data:\", scheduledData);\n                setUpcomingPosts(scheduledData.scheduledPosts || []);\n                setStats((prev)=>{\n                    var _scheduledData_scheduledPosts;\n                    return {\n                        ...prev,\n                        upcomingTweets: ((_scheduledData_scheduledPosts = scheduledData.scheduledPosts) === null || _scheduledData_scheduledPosts === void 0 ? void 0 : _scheduledData_scheduledPosts.length) || 0\n                    };\n                });\n            } else {\n                console.error(\"Failed to fetch scheduled posts:\", await scheduledResponse.text());\n            }\n            // Load posted content for analytics\n            console.log(\"Fetching posted content...\");\n            const postedResponse = await fetch(\"/api/twitter/posted?userId=\".concat(user.id));\n            console.log(\"Posted response status:\", postedResponse.status);\n            if (postedResponse.ok) {\n                const postedData = await postedResponse.json();\n                console.log(\"Posted data:\", postedData);\n                const posts = postedData.postedContent || [];\n                // Calculate engagement rate (mock calculation)\n                const engagementRate = posts.length > 0 ? Math.floor(Math.random() * 30) + 15 : 15;\n                const contentScore = posts.length > 0 ? (Math.random() * 2 + 8).toFixed(1) : \"8.5\";\n                setStats((prev)=>({\n                        ...prev,\n                        followers: Math.floor(Math.random() * 1000) + 500,\n                        engagementRate,\n                        contentScore: parseFloat(contentScore)\n                    }));\n            } else {\n                console.error(\"Failed to fetch posted content:\", await postedResponse.text());\n                // Set default stats even if API fails\n                setStats((prev)=>({\n                        ...prev,\n                        followers: 847,\n                        engagementRate: 24,\n                        contentScore: 8.7\n                    }));\n            }\n            // Generate AI suggestions\n            setSuggestions([\n                \"Share a behind-the-scenes story about your workflow\",\n                \"Create a thread about productivity tips\",\n                \"Post about recent industry trends\",\n                \"Engage with your community through polls\"\n            ]);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            // Set default stats on error\n            setStats((prev)=>({\n                    ...prev,\n                    followers: 847,\n                    engagementRate: 24,\n                    contentScore: 8.7\n                }));\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"32px\",\n            height: \"100vh\",\n            overflow: \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"32px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.5px\",\n                            marginBottom: \"8px\"\n                        },\n                        children: \"Briefing Room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            fontSize: \"16px\",\n                            margin: 0,\n                            fontWeight: \"400\"\n                        },\n                        children: \"Your daily mission control center\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: \"800px\",\n                    margin: \"0 auto\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            marginBottom: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"16px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        marginBottom: \"12px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                color: colors.text.primary,\n                                                margin: 0,\n                                                fontSize: \"20px\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"Today's Mission\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"4px 8px\",\n                                                background: \"\".concat(colors.primary, \"15\"),\n                                                borderRadius: \"6px\",\n                                                color: colors.primary,\n                                                fontSize: \"11px\",\n                                                fontWeight: \"600\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.5px\"\n                                            },\n                                            children: \"AI Generated\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: colors.text.secondary,\n                                        fontSize: \"16px\",\n                                        lineHeight: \"1.5\",\n                                        margin: 0,\n                                        marginBottom: \"20px\"\n                                    },\n                                    children: \"Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"grid\",\n                                        gridTemplateColumns: \"repeat(4, 1fr)\",\n                                        gap: \"16px\",\n                                        marginBottom: \"20px\"\n                                    },\n                                    children: [\n                                        {\n                                            label: \"Upcoming Tweets\",\n                                            value: loading ? \"...\" : stats.upcomingTweets.toString(),\n                                            icon: _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Calendar,\n                                            color: colors.primary\n                                        },\n                                        {\n                                            label: \"Followers\",\n                                            value: loading ? \"...\" : stats.followers.toLocaleString(),\n                                            icon: _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Users,\n                                            color: \"#10B981\"\n                                        },\n                                        {\n                                            label: \"Engagement Rate\",\n                                            value: loading ? \"...\" : \"\".concat(stats.engagementRate, \"%\"),\n                                            icon: _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.TrendingUp,\n                                            color: \"#3B82F6\"\n                                        },\n                                        {\n                                            label: \"Content Score\",\n                                            value: loading ? \"...\" : \"\".concat(stats.contentScore, \"/10\"),\n                                            icon: _barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap,\n                                            color: \"#8B5CF6\"\n                                        }\n                                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: colors.surface,\n                                                borderRadius: \"12px\",\n                                                padding: \"20px\",\n                                                textAlign: \"center\",\n                                                border: \"1px solid \".concat(colors.border),\n                                                boxShadow: \"0 2px 8px rgba(0, 0, 0, 0.04)\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"center\",\n                                                        marginBottom: \"8px\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                        size: 20,\n                                                        color: stat.color\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"700\",\n                                                        color: colors.text.primary,\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.text.tertiary,\n                                                        fontWeight: \"500\"\n                                                    },\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        gap: \"12px\",\n                                        flexWrap: \"wrap\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/meeting\",\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"12px 20px\",\n                                                    background: colors.primary,\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Join Call\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                padding: \"12px 20px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                border: \"1px solid \".concat(colors.border),\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\"\n                                            },\n                                            children: \"Ask Mentor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/tweet-center\",\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"12px 20px\",\n                                                    background: colors.surface,\n                                                    color: colors.text.primary,\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Create Content\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined),\n                    upcomingPosts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            marginBottom: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    marginBottom: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Calendar, {\n                                        size: 20,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            margin: 0,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"Upcoming Tweets\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"12px\"\n                                },\n                                children: upcomingPosts.slice(0, 3).map((post, index)=>{\n                                    var _post_content;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px\",\n                                            background: colors.background,\n                                            borderRadius: \"12px\",\n                                            border: \"1px solid \".concat(colors.border)\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                justifyContent: \"space-between\",\n                                                alignItems: \"flex-start\",\n                                                marginBottom: \"8px\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: colors.text.primary,\n                                                        lineHeight: \"1.4\",\n                                                        flex: 1\n                                                    },\n                                                    children: [\n                                                        (_post_content = post.content) === null || _post_content === void 0 ? void 0 : _post_content.substring(0, 100),\n                                                        \"...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.text.tertiary,\n                                                        marginLeft: \"12px\",\n                                                        whiteSpace: \"nowrap\"\n                                                    },\n                                                    children: new Date(post.scheduled_time).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            marginBottom: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    marginBottom: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap, {\n                                        size: 20,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            margin: 0,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"AI Suggestions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"12px\"\n                                },\n                                children: suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px\",\n                                            background: colors.background,\n                                            borderRadius: \"12px\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: \"8px\",\n                                                    height: \"8px\",\n                                                    borderRadius: \"50%\",\n                                                    background: colors.primary\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.primary,\n                                                    flex: 1\n                                                },\n                                                children: suggestion\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/tweet-center\",\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        padding: \"6px 12px\",\n                                                        background: colors.primary,\n                                                        color: \"white\",\n                                                        border: \"none\",\n                                                        borderRadius: \"6px\",\n                                                        fontSize: \"12px\",\n                                                        fontWeight: \"500\",\n                                                        cursor: \"pointer\"\n                                                    },\n                                                    children: \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"flex-start\",\n                                gap: \"16px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"48px\",\n                                        height: \"48px\",\n                                        borderRadius: \"12px\",\n                                        background: \"\".concat(colors.primary, \"15\"),\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\",\n                                        flexShrink: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"24px\",\n                                                height: \"24px\",\n                                                borderRadius: \"6px\",\n                                                background: colors.primary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"2px\",\n                                                right: \"2px\",\n                                                width: \"12px\",\n                                                height: \"12px\",\n                                                borderRadius: \"50%\",\n                                                background: \"#00E676\",\n                                                border: \"2px solid white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: colors.text.primary,\n                                                margin: 0,\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                marginBottom: \"8px\"\n                                            },\n                                            children: \"AI Mentor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: colors.text.secondary,\n                                                margin: 0,\n                                                fontSize: \"15px\",\n                                                lineHeight: \"1.5\"\n                                            },\n                                            children: \"Ready to help you create content that resonates. What's on your mind today?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HomePage, \"QgDVr8VjFc6+rV3us+rtn0eYZLg=\", false, function() {\n    return [\n        _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = HomePage;\nHomePage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 521,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});