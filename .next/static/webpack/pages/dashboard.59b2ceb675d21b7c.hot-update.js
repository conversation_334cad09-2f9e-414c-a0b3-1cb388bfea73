"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./components/SidebarLayoutSimple.tsx":
/*!********************************************!*\
  !*** ./components/SidebarLayoutSimple.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthModal */ \"./components/AuthModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    var _user_user_metadata;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, profile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Fallback user data for when not authenticated or loading\n    const userData = {\n        name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"Alex Chen\",\n        email: (user === null || user === void 0 ? void 0 : user.email) || \"<EMAIL>\",\n        plan: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise\" : \"Free\",\n        avatar: (profile === null || profile === void 0 ? void 0 : profile.avatar_url) || null,\n        isOnline: (profile === null || profile === void 0 ? void 0 : profile.is_online) || false\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        background: \"#F5F1EB\",\n        surface: \"#FFFFFF\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        sidebar: {\n            background: \"#2A2A2A\",\n            surface: \"#333333\",\n            text: \"#E8E8E8\",\n            textSecondary: \"#B8B8B8\",\n            accent: \"#FF6B35\",\n            border: \"#404040\",\n            hover: \"#3A3A3A\" // Hover state\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageCircle\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.BarChart3\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Video\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: colors.background,\n            padding: \"16px\",\n            gap: \"16px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"220px\",\n                    background: colors.sidebar.background,\n                    height: \"calc(100vh - 32px)\",\n                    borderRadius: \"16px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08)\",\n                    border: \"1px solid \".concat(colors.sidebar.border),\n                    overflow: \"hidden\" // Prevent content overflow\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"20px 24px 16px 24px\",\n                            borderBottom: \"1px solid \".concat(colors.sidebar.border)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"12px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"32px\",\n                                        height: \"32px\",\n                                        background: \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \" 0%, #FF8A65 100%)\"),\n                                        borderRadius: \"8px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        boxShadow: \"0 4px 12px rgba(255, 107, 53, 0.3)\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"18px\",\n                                            color: \"#FFFFFF\",\n                                            fontFamily: \"Georgia, serif\",\n                                            fontStyle: \"italic\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"ℰ\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"16px\",\n                                                fontWeight: \"700\",\n                                                fontFamily: \"Georgia, serif\",\n                                                fontStyle: \"italic\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Exie\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.textSecondary,\n                                                fontSize: \"11px\",\n                                                fontWeight: \"500\",\n                                                letterSpacing: \"0.5px\",\n                                                textTransform: \"uppercase\"\n                                            },\n                                            children: \"AI Assistant\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"20px 16px\",\n                            flex: 1,\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"2px\"\n                        },\n                        children: menuItems.map((item, index)=>{\n                            const active = isActive(item.href);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"12px 16px\",\n                                                borderRadius: \"12px\",\n                                                background: active ? colors.sidebar.surface : \"transparent\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                border: active ? \"1px solid \".concat(colors.sidebar.accent, \"20\") : \"1px solid transparent\",\n                                                position: \"relative\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                if (!active) {\n                                                    e.currentTarget.style.background = colors.sidebar.hover;\n                                                    e.currentTarget.style.transform = \"translateX(2px)\";\n                                                }\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                if (!active) {\n                                                    e.currentTarget.style.background = \"transparent\";\n                                                    e.currentTarget.style.transform = \"translateX(0)\";\n                                                }\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"0\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"3px\",\n                                                        height: \"20px\",\n                                                        background: colors.sidebar.accent,\n                                                        borderRadius: \"0 2px 2px 0\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"20px\",\n                                                        height: \"20px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"12px\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        size: 16,\n                                                        color: active ? colors.sidebar.accent : colors.sidebar.textSecondary,\n                                                        strokeWidth: active ? 2.5 : 2\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                        fontSize: \"14px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        letterSpacing: \"0.2px\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    index < menuItems.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            margin: \"8px 0\",\n                                            opacity: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"20px\",\n                                                height: \"1px\",\n                                                background: colors.sidebar.textSecondary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, item.href, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px\",\n                            borderTop: \"1px solid \".concat(colors.sidebar.border),\n                            background: colors.sidebar.surface\n                        },\n                        children: user ? // Authenticated user - show profile\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    padding: \"12px\",\n                                    background: colors.sidebar.background,\n                                    borderRadius: \"12px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                    border: \"1px solid \".concat(colors.sidebar.border)\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.hover;\n                                    e.currentTarget.style.borderColor = colors.sidebar.accent + \"40\";\n                                    e.currentTarget.style.transform = \"translateY(-1px)\";\n                                    e.currentTarget.style.boxShadow = \"0 4px 12px rgba(0, 0, 0, 0.15)\";\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.background;\n                                    e.currentTarget.style.borderColor = colors.sidebar.border;\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                    e.currentTarget.style.boxShadow = \"none\";\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"28px\",\n                                            height: \"28px\",\n                                            background: userData.avatar ? \"transparent\" : \"rgba(255, 255, 255, 0.3)\",\n                                            borderRadius: \"8px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            overflow: \"hidden\"\n                                        },\n                                        children: [\n                                            userData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userData.avatar,\n                                                alt: userData.name,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"8px\"\n                                                },\n                                                onError: (e)=>{\n                                                    // Fallback to icon if image fails to load\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.parentElement.style.background = \"rgba(255, 255, 255, 0.3)\";\n                                                    e.currentTarget.parentElement.innerHTML = '<svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"'.concat(colors.sidebar.text, '\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path><circle cx=\"12\" cy=\"7\" r=\"4\"></circle></svg>');\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 14,\n                                                color: colors.sidebar.text\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            userData.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-2px\",\n                                                    right: \"-2px\",\n                                                    width: \"8px\",\n                                                    height: \"8px\",\n                                                    background: \"#00FF88\",\n                                                    borderRadius: \"50%\",\n                                                    border: \"2px solid rgba(255, 255, 255, 0.9)\",\n                                                    boxShadow: \"0 0 4px rgba(0, 255, 136, 0.6)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    lineHeight: \"1.2\"\n                                                },\n                                                children: userData.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"rgba(255, 255, 255, 0.8)\",\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"400\",\n                                                    lineHeight: \"1.2\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            userData.plan,\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, undefined) : // Not authenticated - show sign in button\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>setShowAuthModal(true),\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"10px\",\n                                padding: \"10px\",\n                                background: \"rgba(255, 255, 255, 0.15)\",\n                                borderRadius: \"10px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\",\n                                border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.25)\";\n                                e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.4)\";\n                                e.currentTarget.style.transform = \"translateY(-1px)\";\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.15)\";\n                                e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.2)\";\n                                e.currentTarget.style.transform = \"translateY(0)\";\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"28px\",\n                                        height: \"28px\",\n                                        background: \"rgba(255, 255, 255, 0.3)\",\n                                        borderRadius: \"8px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogIn, {\n                                        size: 14,\n                                        color: colors.sidebar.text\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"13px\",\n                                                fontWeight: \"600\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: \"rgba(255, 255, 255, 0.8)\",\n                                                fontSize: \"11px\",\n                                                fontWeight: \"400\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Access your account\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.surface,\n                    borderRadius: \"20px\",\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"40px\",\n                        height: \"100%\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"2fxYphseme7UlXU7fqnQRjVv+Ys=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayoutSimple.tsx\n"));

/***/ })

});