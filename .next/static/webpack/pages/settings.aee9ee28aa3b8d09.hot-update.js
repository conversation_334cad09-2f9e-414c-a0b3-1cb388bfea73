"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/settings",{

/***/ "./components/SidebarLayoutSimple.tsx":
/*!********************************************!*\
  !*** ./components/SidebarLayoutSimple.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthModal */ \"./components/AuthModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    var _user_user_metadata, _user_email;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, profile, loading } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Debug logging\n    console.log(\"Sidebar render:\", {\n        user: !!user,\n        userEmail: user === null || user === void 0 ? void 0 : user.email,\n        profile: !!profile,\n        profileName: profile === null || profile === void 0 ? void 0 : profile.full_name,\n        loading\n    });\n    // User data - only use real data when authenticated\n    const userData = user ? {\n        name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || (user === null || user === void 0 ? void 0 : (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split(\"@\")[0]) || \"User\",\n        email: (user === null || user === void 0 ? void 0 : user.email) || \"\",\n        plan: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise\" : \"Free\",\n        avatar: (profile === null || profile === void 0 ? void 0 : profile.avatar_url) || null,\n        isOnline: (profile === null || profile === void 0 ? void 0 : profile.is_online) || false\n    } : null;\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        background: \"#F5F1EB\",\n        surface: \"#FFFFFF\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            surface: \"rgba(255, 107, 53, 0.05)\",\n            text: \"#2D1B14\",\n            textSecondary: \"#5D4037\",\n            textMuted: \"#8D6E63\",\n            accent: \"#FF6B35\",\n            accentSoft: \"#FF8A65\",\n            accentLight: \"#FFF7F4\",\n            border: \"rgba(255, 107, 53, 0.15)\",\n            hover: \"rgba(255, 107, 53, 0.08)\",\n            divider: \"rgba(255, 107, 53, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.25)\" // Orange glow\n        }\n    };\n    const menuItems = [\n        {\n            section: \"WORKSPACE\",\n            items: [\n                {\n                    href: \"/\",\n                    label: \"Briefing Room\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home\n                },\n                {\n                    href: \"/tweet-center\",\n                    label: \"Drafting Desk\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageCircle\n                },\n                {\n                    href: \"/schedule\",\n                    label: \"Content Scheduler\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Calendar\n                },\n                {\n                    href: \"/dashboard\",\n                    label: \"Growth Lab\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.BarChart3\n                },\n                {\n                    href: \"/meeting\",\n                    label: \"AI Meetings\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Video\n                }\n            ]\n        },\n        {\n            section: \"SETTINGS\",\n            items: [\n                {\n                    href: \"/settings\",\n                    label: \"Settings\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User\n                }\n            ]\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: colors.background,\n            padding: \"16px\",\n            gap: \"16px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    background: colors.sidebar.background,\n                    height: \"calc(100vh - 32px)\",\n                    borderRadius: \"24px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: \"0 8px 32px \".concat(colors.sidebar.glow, \", 0 2px 8px rgba(255, 107, 53, 0.1)\"),\n                    border: \"1px solid \".concat(colors.sidebar.border),\n                    overflow: \"hidden\",\n                    position: \"relative\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"24px 16px 20px 20px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"10px\",\n                            background: colors.sidebar.background\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"https://nlckamsrdiwkyyrxzntf.supabase.co/storage/v1/object/sign/logos/logoxe.png?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InN0b3JhZ2UtdXJsLXNpZ25pbmcta2V5X2RiNTE0YzE5LTlhNTQtNGZiNy1hMjY3LTJmNjY5ZDlhZjY1OCJ9.eyJ1cmwiOiJsb2dvcy9sb2dveGUucG5nIiwiaWF0IjoxNzQ4MjMxNDM1LCJleHAiOjE3NTA4MjM0MzV9.dw1yy3hjXvMy02IhFMKGw_-evgbmyYDuJ4m6HPP1Uec\",\n                                alt: \"Exie Logo\",\n                                style: {\n                                    height: \"28px\",\n                                    width: \"auto\",\n                                    objectFit: \"contain\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontFamily: \"Anek Tamil, sans-serif\",\n                                    fontSize: \"22px\",\n                                    fontWeight: \"600\",\n                                    color: colors.sidebar.text,\n                                    letterSpacing: \"-0.5px\",\n                                    lineHeight: \"1\"\n                                },\n                                children: \"Exie\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"0 16px 16px 16px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                display: \"flex\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Search, {\n                                    size: 14,\n                                    color: colors.sidebar.textMuted,\n                                    style: {\n                                        position: \"absolute\",\n                                        left: \"12px\",\n                                        zIndex: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search...\",\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"8px 12px 8px 36px\",\n                                        border: \"1px solid \".concat(colors.sidebar.border),\n                                        borderRadius: \"8px\",\n                                        background: colors.sidebar.surface,\n                                        color: colors.sidebar.text,\n                                        fontSize: \"12px\",\n                                        fontWeight: \"400\",\n                                        outline: \"none\",\n                                        transition: \"all 0.2s ease\"\n                                    },\n                                    onFocus: (e)=>{\n                                        e.target.style.borderColor = colors.sidebar.accent;\n                                        e.target.style.background = colors.sidebar.background;\n                                    },\n                                    onBlur: (e)=>{\n                                        e.target.style.borderColor = colors.sidebar.border;\n                                        e.target.style.background = colors.sidebar.surface;\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"8px 16px\",\n                            flex: 1,\n                            display: \"flex\",\n                            flexDirection: \"column\"\n                        },\n                        children: menuItems.map((section, sectionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: sectionIndex < menuItems.length - 1 ? \"24px\" : \"0\"\n                                },\n                                children: [\n                                    sectionIndex > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            height: \"1px\",\n                                            background: \"linear-gradient(90deg, transparent, \".concat(colors.sidebar.divider, \", transparent)\"),\n                                            margin: \"16px 12px 20px 12px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"10px\",\n                                            fontWeight: \"600\",\n                                            color: colors.sidebar.textMuted,\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"1px\",\n                                            marginBottom: \"10px\",\n                                            paddingLeft: \"12px\"\n                                        },\n                                        children: section.section\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\",\n                                            gap: \"2px\"\n                                        },\n                                        children: section.items.map((item)=>{\n                                            const active = isActive(item.href);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        padding: \"8px 12px\",\n                                                        borderRadius: \"8px\",\n                                                        background: active ? colors.sidebar.surface : \"transparent\",\n                                                        cursor: \"pointer\",\n                                                        transition: \"all 0.2s ease\",\n                                                        position: \"relative\"\n                                                    },\n                                                    onMouseEnter: (e)=>{\n                                                        if (!active) {\n                                                            e.currentTarget.style.background = colors.sidebar.hover;\n                                                        }\n                                                    },\n                                                    onMouseLeave: (e)=>{\n                                                        if (!active) {\n                                                            e.currentTarget.style.background = \"transparent\";\n                                                        }\n                                                    },\n                                                    children: [\n                                                        active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: \"absolute\",\n                                                                left: \"0\",\n                                                                top: \"50%\",\n                                                                transform: \"translateY(-50%)\",\n                                                                width: \"2px\",\n                                                                height: \"16px\",\n                                                                background: colors.sidebar.accent,\n                                                                borderRadius: \"0 2px 2px 0\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: \"16px\",\n                                                                height: \"16px\",\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                justifyContent: \"center\",\n                                                                marginRight: \"10px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 14,\n                                                                color: active ? colors.sidebar.accent : colors.sidebar.textSecondary,\n                                                                strokeWidth: 2\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                                fontSize: \"13px\",\n                                                                fontWeight: active ? \"500\" : \"400\",\n                                                                letterSpacing: \"0.1px\"\n                                                            },\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, item.href, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, section.section, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            margin: \"0 16px 16px 16px\",\n                            height: \"1px\",\n                            background: \"linear-gradient(90deg, transparent, \".concat(colors.sidebar.divider, \", transparent)\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"8px 16px 16px 16px\"\n                        },\n                        children: user && userData ? // Authenticated user - show profile\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"10px\",\n                                    padding: \"10px 12px\",\n                                    background: colors.sidebar.background,\n                                    borderRadius: \"14px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                    border: \"1px solid \".concat(colors.sidebar.border),\n                                    boxShadow: \"0 2px 8px \".concat(colors.sidebar.glow)\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.surface;\n                                    e.currentTarget.style.borderColor = colors.sidebar.accent + \"40\";\n                                    e.currentTarget.style.transform = \"translateY(-1px)\";\n                                    e.currentTarget.style.boxShadow = \"0 4px 16px \".concat(colors.sidebar.glow);\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.background;\n                                    e.currentTarget.style.borderColor = colors.sidebar.border;\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                    e.currentTarget.style.boxShadow = \"0 2px 8px \".concat(colors.sidebar.glow);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: userData.avatar ? \"transparent\" : \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\"),\n                                            borderRadius: \"10px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            overflow: \"hidden\",\n                                            border: \"2px solid \".concat(colors.sidebar.border)\n                                        },\n                                        children: [\n                                            userData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userData.avatar,\n                                                alt: userData.name,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"8px\"\n                                                },\n                                                onError: (e)=>{\n                                                    // Fallback to icon if image fails to load\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.parentElement.style.background = \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\");\n                                                    e.currentTarget.parentElement.innerHTML = '<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path><circle cx=\"12\" cy=\"7\" r=\"4\"></circle></svg>';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 16,\n                                                color: \"#FFFFFF\",\n                                                strokeWidth: 2\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            userData.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"10px\",\n                                                    height: \"10px\",\n                                                    background: \"#00FF88\",\n                                                    borderRadius: \"50%\",\n                                                    border: \"2px solid #FFFFFF\",\n                                                    boxShadow: \"0 0 6px rgba(0, 255, 136, 0.8)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    lineHeight: \"1.2\",\n                                                    marginBottom: \"2px\"\n                                                },\n                                                children: userData.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.textMuted,\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"500\",\n                                                    lineHeight: \"1.2\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            userData.plan,\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: colors.sidebar.accent\n                                                        },\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, undefined) : // Not authenticated - show sign in button\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>setShowAuthModal(true),\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                padding: \"8px 12px\",\n                                borderRadius: \"8px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\",\n                                background: \"transparent\"\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.background = colors.sidebar.hover;\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.background = \"transparent\";\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"16px\",\n                                        height: \"16px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        marginRight: \"10px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogIn, {\n                                        size: 14,\n                                        color: colors.sidebar.textSecondary,\n                                        strokeWidth: 2\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: colors.sidebar.textSecondary,\n                                        fontSize: \"13px\",\n                                        fontWeight: \"400\",\n                                        letterSpacing: \"0.1px\"\n                                    },\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.surface,\n                    borderRadius: \"24px\",\n                    height: \"calc(100vh - 32px)\",\n                    position: \"relative\",\n                    boxShadow: \"0 4px 20px rgba(255, 107, 53, 0.08)\",\n                    border: \"1px solid \".concat(colors.sidebar.border),\n                    overflow: \"hidden\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"32px\",\n                        height: \"100%\",\n                        overflow: \"auto\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 469,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"NDfMXAvUe5qStKhEmNPi31OHa30=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayoutSimple.tsx\n"));

/***/ })

});