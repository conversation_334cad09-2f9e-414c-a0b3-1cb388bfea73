"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "__barrel_optimize__?names=Bot!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!********************************************************************************************!*\
  !*** __barrel_optimize__?names=Bot!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \********************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Bot: function() { return /* reexport safe */ _icons_bot_js__WEBPACK_IMPORTED_MODULE_0__["default"]; }
/* harmony export */ });
/* harmony import */ var _icons_bot_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bot.js */ "./node_modules/lucide-react/dist/esm/icons/bot.js");



/***/ }),

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot!=!lucide-react */ \"__barrel_optimize__?names=Bot!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n// pages/tweet-center.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst TweetCenterPage = ()=>{\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showAgentEInput, setShowAgentEInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [agentEPrompt, setAgentEPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showOptionsDropdown, setShowOptionsDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [autoRunMode, setAutoRunMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        borderHover: \"#E8D5C4\",\n        surface: \"#FFFFFF\",\n        surfaceHover: \"#F9F7F4\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Real AI prediction based on content context\n    const generateContextualSuggestion = (text)=>{\n        if (text.length < 10) return \"\";\n        try {\n            var _text_split_pop;\n            // Simple prediction logic based on sentence patterns\n            const lastSentence = ((_text_split_pop = text.split(\".\").pop()) === null || _text_split_pop === void 0 ? void 0 : _text_split_pop.trim()) || text;\n            // If sentence seems incomplete, suggest completion\n            if (lastSentence.length > 0) {\n                // Sports context\n                if (lastSentence.toLowerCase().includes(\"sports\") || lastSentence.toLowerCase().includes(\"game\") || lastSentence.toLowerCase().includes(\"team\")) {\n                    const sportsSuggestions = [\n                        \" requires dedication and consistent practice\",\n                        \" teaches us valuable life lessons\",\n                        \" brings people together like nothing else\",\n                        \" is more than just competition\"\n                    ];\n                    return sportsSuggestions[Math.floor(Math.random() * sportsSuggestions.length)];\n                }\n                // Tech context\n                if (lastSentence.toLowerCase().includes(\"technology\") || lastSentence.toLowerCase().includes(\"coding\") || lastSentence.toLowerCase().includes(\"software\")) {\n                    const techSuggestions = [\n                        \" is evolving faster than ever before\",\n                        \" has the power to solve real problems\",\n                        \" requires continuous learning and adaptation\",\n                        \" should be accessible to everyone\"\n                    ];\n                    return techSuggestions[Math.floor(Math.random() * techSuggestions.length)];\n                }\n                // Business context\n                if (lastSentence.toLowerCase().includes(\"business\") || lastSentence.toLowerCase().includes(\"startup\") || lastSentence.toLowerCase().includes(\"entrepreneur\")) {\n                    const businessSuggestions = [\n                        \" is about solving problems for people\",\n                        \" requires patience and persistence\",\n                        \" success comes from understanding your customers\",\n                        \" failure is just feedback in disguise\"\n                    ];\n                    return businessSuggestions[Math.floor(Math.random() * businessSuggestions.length)];\n                }\n                // General sentence completion based on common patterns\n                if (lastSentence.endsWith(\"I think\") || lastSentence.endsWith(\"I believe\")) {\n                    return \" that consistency beats perfection every time\";\n                }\n                if (lastSentence.includes(\"The key to\")) {\n                    return \" success is taking action despite uncertainty\";\n                }\n                if (lastSentence.includes(\"What I learned\")) {\n                    return \" is that small steps lead to big changes\";\n                }\n                // Default contextual completions\n                const generalSuggestions = [\n                    \" and here's why that matters\",\n                    \" - let me explain\",\n                    \" in my experience\",\n                    \" based on what I've seen\",\n                    \" and the results speak for themselves\"\n                ];\n                return generalSuggestions[Math.floor(Math.random() * generalSuggestions.length)];\n            }\n            return \"\";\n        } catch (error) {\n            console.error(\"Error generating suggestion:\", error);\n            return \"\";\n        }\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    const autoFormat = ()=>{\n        if (content.length > 280) {\n            // Convert to thread format\n            const sentences = content.split(\". \");\n            const threads = [];\n            let currentThread = \"\";\n            sentences.forEach((sentence)=>{\n                if ((currentThread + sentence + \". \").length <= 280) {\n                    currentThread += sentence + \". \";\n                } else {\n                    if (currentThread) threads.push(currentThread.trim());\n                    currentThread = sentence + \". \";\n                }\n            });\n            if (currentThread) threads.push(currentThread.trim());\n            setContent(threads.join(\"\\n\\n\"));\n            setFormatMode(\"thread\");\n        }\n    };\n    const handleAgentESubmit = (e)=>{\n        e.preventDefault();\n        if (!agentEPrompt.trim()) return;\n        // Generate content based on the prompt\n        const generatedContent = generateContentFromPrompt(agentEPrompt);\n        setContent(generatedContent);\n        setAgentEPrompt(\"\");\n        setShowAgentEInput(false);\n    };\n    const generateContentFromPrompt = (prompt)=>{\n        const lowerPrompt = prompt.toLowerCase();\n        // Different content types based on prompt\n        if (lowerPrompt.includes(\"thread\") || lowerPrompt.includes(\"twitter thread\")) {\n            return \"Here's a thread about \".concat(prompt.replace(/thread|twitter thread/gi, \"\").trim(), \":\\n\\n1/ The key to understanding this topic is...\\n\\n2/ Most people think...\\n\\n3/ But here's what actually works...\");\n        }\n        if (lowerPrompt.includes(\"tips\") || lowerPrompt.includes(\"advice\")) {\n            return \"\".concat(prompt.charAt(0).toUpperCase() + prompt.slice(1), \":\\n\\n• Focus on the fundamentals first\\n• Consistency beats perfection\\n• Learn from others who've succeeded\\n• Take action despite uncertainty\");\n        }\n        if (lowerPrompt.includes(\"story\") || lowerPrompt.includes(\"experience\")) {\n            return \"Here's my experience with \".concat(prompt.replace(/story|experience/gi, \"\").trim(), \":\\n\\nIt started when I realized that most advice online was generic. I needed something that actually worked in the real world...\");\n        }\n        // Default content generation\n        return \"\".concat(prompt.charAt(0).toUpperCase() + prompt.slice(1), \".\\n\\nHere's what I've learned from years of experience: the biggest difference between success and failure isn't talent or luck—it's consistency.\\n\\nMost people give up right before they would have succeeded.\");\n    };\n    // Auto-run mode - automatically generates tweets\n    const generateAutoTweet = ()=>{\n        const topics = [\n            \"productivity tips for entrepreneurs\",\n            \"lessons learned from building a startup\",\n            \"the importance of consistency in business\",\n            \"how to stay motivated during tough times\",\n            \"building habits that stick\",\n            \"the power of compound growth\",\n            \"why most people give up too early\",\n            \"simple strategies for better focus\"\n        ];\n        const randomTopic = topics[Math.floor(Math.random() * topics.length)];\n        const generatedContent = generateContentFromPrompt(randomTopic);\n        setContent(generatedContent);\n    };\n    // Auto-run effect\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (autoRunMode && !content) {\n            const timer = setTimeout(()=>{\n                generateAutoTweet();\n            }, 1000);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        autoRunMode,\n        content\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.paper,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n        },\n        className: \"jsx-3f45dd3580662bfa\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    marginBottom: \"40px\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.4px\",\n                            fontFamily: \"SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowOptionsDropdown(!showOptionsDropdown),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"8px 16px\",\n                                    background: colors.surface,\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"12px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    color: colors.text.primary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    \"Options\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            transform: showOptionsDropdown ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                            transition: \"transform 0.2s ease\",\n                                            fontSize: \"12px\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: \"▼\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, undefined),\n                            showOptionsDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"100%\",\n                                    right: 0,\n                                    marginTop: \"8px\",\n                                    background: \"white\",\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"12px\",\n                                    boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.12)\",\n                                    padding: \"12px\",\n                                    minWidth: \"220px\",\n                                    zIndex: 1000\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            padding: \"8px 0\",\n                                            borderBottom: \"1px solid \".concat(colors.border)\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.primary\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: \"AI Predictions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setAiEnabled(!aiEnabled),\n                                                style: {\n                                                    width: \"40px\",\n                                                    height: \"20px\",\n                                                    borderRadius: \"10px\",\n                                                    border: \"none\",\n                                                    background: aiEnabled ? colors.primary : colors.border,\n                                                    position: \"relative\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"white\",\n                                                        position: \"absolute\",\n                                                        top: \"2px\",\n                                                        left: aiEnabled ? \"22px\" : \"2px\",\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    className: \"jsx-3f45dd3580662bfa\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            padding: \"8px 0\",\n                                            borderBottom: \"1px solid \".concat(colors.border)\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            color: colors.text.primary,\n                                                            fontWeight: \"500\"\n                                                        },\n                                                        className: \"jsx-3f45dd3580662bfa\",\n                                                        children: \"Auto-Run Mode ✨\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"12px\",\n                                                            color: colors.text.tertiary,\n                                                            marginTop: \"2px\"\n                                                        },\n                                                        className: \"jsx-3f45dd3580662bfa\",\n                                                        children: \"Automatically generates tweets\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setAutoRunMode(!autoRunMode);\n                                                    if (!autoRunMode) {\n                                                        setContent(\"\"); // Clear content to trigger auto-generation\n                                                    }\n                                                },\n                                                style: {\n                                                    width: \"40px\",\n                                                    height: \"20px\",\n                                                    borderRadius: \"10px\",\n                                                    border: \"none\",\n                                                    background: autoRunMode ? \"#10B981\" : colors.border,\n                                                    position: \"relative\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\",\n                                                    boxShadow: autoRunMode ? \"0 0 12px rgba(16, 185, 129, 0.4)\" : \"none\"\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"white\",\n                                                        position: \"absolute\",\n                                                        top: \"2px\",\n                                                        left: autoRunMode ? \"22px\" : \"2px\",\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    className: \"jsx-3f45dd3580662bfa\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            padding: \"8px 0\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.primary\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: \"Thread Mode\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setFormatMode(formatMode === \"thread\" ? \"single\" : \"thread\"),\n                                                style: {\n                                                    width: \"40px\",\n                                                    height: \"20px\",\n                                                    borderRadius: \"10px\",\n                                                    border: \"none\",\n                                                    background: formatMode === \"thread\" ? colors.primary : colors.border,\n                                                    position: \"relative\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                className: \"jsx-3f45dd3580662bfa\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"white\",\n                                                        position: \"absolute\",\n                                                        top: \"2px\",\n                                                        left: formatMode === \"thread\" ? \"22px\" : \"2px\",\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    className: \"jsx-3f45dd3580662bfa\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    background: \"transparent\",\n                    position: \"relative\",\n                    minHeight: \"500px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            position: \"relative\",\n                            padding: \"40px 60px\",\n                            minHeight: \"450px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            showSuggestion && aiEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"40px\",\n                                    left: \"60px\",\n                                    right: \"60px\",\n                                    bottom: \"40px\",\n                                    pointerEvents: \"none\",\n                                    zIndex: 1,\n                                    overflow: \"hidden\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"20px\",\n                                        lineHeight: \"1.7\",\n                                        color: \"transparent\",\n                                        whiteSpace: \"pre-wrap\",\n                                        wordWrap: \"break-word\",\n                                        letterSpacing: \"0.3px\",\n                                        fontWeight: \"400\"\n                                    },\n                                    className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                    children: [\n                                        content,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#9CA3AF\",\n                                                opacity: 0.6,\n                                                fontStyle: \"normal\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: aiSuggestion\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: content,\n                                onChange: (e)=>setContent(e.target.value),\n                                onKeyDown: handleTabPress,\n                                placeholder: aiEnabled ? \"Start writing...\" : \"What's on your mind?\",\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    minHeight: \"400px\",\n                                    padding: \"0\",\n                                    border: \"none\",\n                                    background: \"transparent\",\n                                    fontSize: \"20px\",\n                                    lineHeight: \"1.7\",\n                                    color: colors.text.primary,\n                                    resize: \"none\",\n                                    outline: \"none\",\n                                    letterSpacing: \"0.3px\",\n                                    position: \"relative\",\n                                    zIndex: 2,\n                                    fontWeight: \"400\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            padding: \"0 60px 40px\",\n                            marginTop: \"20px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"400\",\n                                    opacity: 0.7\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                children: [\n                                    content.length,\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setContent(\"\"),\n                                        style: {\n                                            padding: \"12px 24px\",\n                                            background: \"transparent\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            borderRadius: \"10px\",\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = colors.surfaceHover;\n                                            target.style.borderColor = colors.borderHover;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = \"transparent\";\n                                            target.style.borderColor = colors.border;\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Save Draft\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // Publish logic here\n                                            console.log(\"Publishing:\", content);\n                                        },\n                                        style: {\n                                            padding: \"12px 28px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            border: \"none\",\n                                            borderRadius: \"10px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(-1px)\";\n                                            target.style.boxShadow = \"0 6px 16px \".concat(colors.primary, \"40\");\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(0)\";\n                                            target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"30\");\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Publish\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, undefined),\n                    showAgentEInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"fixed\",\n                            bottom: \"24px\",\n                            left: \"50%\",\n                            transform: \"translateX(-50%)\",\n                            width: \"600px\",\n                            height: \"48px\",\n                            background: \"white\",\n                            borderRadius: \"24px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            padding: \"0 20px\",\n                            boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05)\",\n                            zIndex: 1000,\n                            border: \"2px solid \".concat(colors.primary)\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                size: 18,\n                                color: colors.primary,\n                                style: {\n                                    marginRight: \"12px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleAgentESubmit,\n                                style: {\n                                    flex: 1,\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: agentEPrompt,\n                                        onChange: (e)=>setAgentEPrompt(e.target.value),\n                                        placeholder: \"Ask Agent E to write something for you...\",\n                                        autoFocus: true,\n                                        style: {\n                                            flex: 1,\n                                            border: \"none\",\n                                            outline: \"none\",\n                                            fontSize: \"14px\",\n                                            color: colors.text.primary,\n                                            background: \"transparent\",\n                                            fontWeight: \"400\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        style: {\n                                            background: \"none\",\n                                            border: \"none\",\n                                            cursor: \"pointer\",\n                                            padding: \"4px\",\n                                            marginLeft: \"8px\"\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: \"↵\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAgentEInput(false),\n                                style: {\n                                    background: \"none\",\n                                    border: \"none\",\n                                    cursor: \"pointer\",\n                                    padding: \"4px\",\n                                    marginLeft: \"8px\",\n                                    color: colors.text.tertiary\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 417,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"3f45dd3580662bfa\",\n                children: \"@-webkit-keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);transform:scale(1.2)}}@-moz-keyframes pulse{0%,100%{opacity:.8;-moz-transform:scale(1);transform:scale(1)}50%{opacity:1;-moz-transform:scale(1.2);transform:scale(1.2)}}@-o-keyframes pulse{0%,100%{opacity:.8;-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-o-transform:scale(1.2);transform:scale(1.2)}}@keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-o-transform:scale(1.2);transform:scale(1.2)}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TweetCenterPage, \"7QuKRY1laqlm1+LZN0E84hfCivE=\");\n_c = TweetCenterPage;\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 655,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (TweetCenterPage);\nvar _c;\n$RefreshReg$(_c, \"TweetCenterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n"));

/***/ })

});