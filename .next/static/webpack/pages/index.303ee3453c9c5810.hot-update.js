"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/SidebarLayoutSimple.tsx":
/*!********************************************!*\
  !*** ./components/SidebarLayoutSimple.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthModal */ \"./components/AuthModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    var _user_user_metadata;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, profile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Fallback user data for when not authenticated or loading\n    const userData = {\n        name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"Alex Chen\",\n        email: (user === null || user === void 0 ? void 0 : user.email) || \"<EMAIL>\",\n        plan: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise\" : \"Free\",\n        avatar: (profile === null || profile === void 0 ? void 0 : profile.avatar_url) || null,\n        isOnline: (profile === null || profile === void 0 ? void 0 : profile.is_online) || false\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        background: \"#F5F1EB\",\n        surface: \"#FFFFFF\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        sidebar: {\n            background: \"#1C1612\",\n            surface: \"rgba(255, 107, 53, 0.04)\",\n            text: \"#F7F3EF\",\n            textSecondary: \"#D4B896\",\n            textMuted: \"#9A8066\",\n            accent: \"#FF6B35\",\n            accentSoft: \"#FF8A65\",\n            accentGlow: \"rgba(255, 107, 53, 0.2)\",\n            border: \"rgba(212, 184, 150, 0.12)\",\n            hover: \"rgba(255, 107, 53, 0.08)\",\n            divider: \"rgba(212, 184, 150, 0.15)\" // Warm divider\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageCircle\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.BarChart3\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Video\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: colors.background,\n            padding: \"18px\",\n            gap: \"18px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    background: colors.sidebar.background,\n                    height: \"calc(100vh - 36px)\",\n                    borderRadius: \"20px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: \"0 12px 40px rgba(28, 22, 18, 0.25), 0 4px 16px rgba(28, 22, 18, 0.15)\",\n                    border: \"1px solid \".concat(colors.sidebar.border),\n                    overflow: \"hidden\",\n                    position: \"relative\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            height: \"1px\",\n                            background: \"linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.3), transparent)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"24px 20px 20px 20px\",\n                            textAlign: \"center\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"42px\",\n                                color: colors.sidebar.accent,\n                                fontFamily: \"Brush Script MT, cursive\",\n                                fontWeight: \"400\",\n                                textShadow: \"0 2px 8px rgba(255, 107, 53, 0.3)\",\n                                letterSpacing: \"1px\",\n                                transform: \"rotate(-2deg)\",\n                                display: \"inline-block\"\n                            },\n                            children: \"Exie\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            margin: \"0 20px 16px 20px\",\n                            height: \"1px\",\n                            background: \"linear-gradient(90deg, transparent, \".concat(colors.sidebar.divider, \", transparent)\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"0 16px\",\n                            flex: 1,\n                            display: \"flex\",\n                            flexDirection: \"column\"\n                        },\n                        children: menuItems.map((item, index)=>{\n                            const active = isActive(item.href);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"14px 16px\",\n                                                borderRadius: \"16px\",\n                                                background: active ? colors.sidebar.surface : \"transparent\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                position: \"relative\",\n                                                marginBottom: \"2px\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                if (!active) {\n                                                    e.currentTarget.style.background = colors.sidebar.hover;\n                                                    e.currentTarget.style.transform = \"translateX(4px)\";\n                                                }\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                if (!active) {\n                                                    e.currentTarget.style.background = \"transparent\";\n                                                    e.currentTarget.style.transform = \"translateX(0)\";\n                                                }\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"0\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"4px\",\n                                                        height: \"24px\",\n                                                        background: \"linear-gradient(180deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\"),\n                                                        borderRadius: \"0 4px 4px 0\",\n                                                        boxShadow: \"0 0 12px \".concat(colors.sidebar.accentGlow)\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"24px\",\n                                                        height: \"24px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"14px\",\n                                                        position: \"relative\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                            size: 18,\n                                                            color: active ? colors.sidebar.accent : colors.sidebar.textSecondary,\n                                                            strokeWidth: active ? 2.5 : 2\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: \"absolute\",\n                                                                inset: \"-2px\",\n                                                                borderRadius: \"50%\",\n                                                                background: \"radial-gradient(circle, \".concat(colors.sidebar.accentGlow, \" 0%, transparent 70%)\"),\n                                                                zIndex: -1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                        fontSize: \"15px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        letterSpacing: \"0.3px\",\n                                                        transition: \"color 0.3s ease\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    index < menuItems.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            alignItems: \"center\",\n                                            margin: \"12px 0\",\n                                            height: \"1px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"4px\"\n                                            },\n                                            children: [\n                                                ...Array(3)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"6px\",\n                                                        height: \"1px\",\n                                                        background: colors.sidebar.textMuted,\n                                                        opacity: 0.4,\n                                                        borderRadius: \"1px\"\n                                                    }\n                                                }, i, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, item.href, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"12px\",\n                            borderTop: \"1px solid rgba(255, 255, 255, 0.3)\"\n                        },\n                        children: user ? // Authenticated user - show profile\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"10px\",\n                                    padding: \"10px\",\n                                    background: \"rgba(255, 255, 255, 0.15)\",\n                                    borderRadius: \"10px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.25)\";\n                                    e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.4)\";\n                                    e.currentTarget.style.transform = \"translateY(-1px)\";\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.15)\";\n                                    e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.2)\";\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"36px\",\n                                            height: \"36px\",\n                                            background: userData.avatar ? \"transparent\" : \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \" 0%, #FF8A65 100%)\"),\n                                            borderRadius: \"10px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            overflow: \"hidden\",\n                                            border: \"2px solid \".concat(colors.sidebar.border)\n                                        },\n                                        children: [\n                                            userData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userData.avatar,\n                                                alt: userData.name,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"8px\"\n                                                },\n                                                onError: (e)=>{\n                                                    // Fallback to icon if image fails to load\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.parentElement.style.background = \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \" 0%, #FF8A65 100%)\");\n                                                    e.currentTarget.parentElement.innerHTML = '<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path><circle cx=\"12\" cy=\"7\" r=\"4\"></circle></svg>';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 16,\n                                                color: \"#FFFFFF\",\n                                                strokeWidth: 2\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            userData.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"10px\",\n                                                    height: \"10px\",\n                                                    background: \"#00FF88\",\n                                                    borderRadius: \"50%\",\n                                                    border: \"2px solid #2A2A2A\",\n                                                    boxShadow: \"0 0 6px rgba(0, 255, 136, 0.8)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    lineHeight: \"1.2\"\n                                                },\n                                                children: userData.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"rgba(255, 255, 255, 0.8)\",\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"400\",\n                                                    lineHeight: \"1.2\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            userData.plan,\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, undefined) : // Not authenticated - show sign in button\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>setShowAuthModal(true),\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"10px\",\n                                padding: \"10px\",\n                                background: \"rgba(255, 255, 255, 0.15)\",\n                                borderRadius: \"10px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\",\n                                border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.25)\";\n                                e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.4)\";\n                                e.currentTarget.style.transform = \"translateY(-1px)\";\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.15)\";\n                                e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.2)\";\n                                e.currentTarget.style.transform = \"translateY(0)\";\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"28px\",\n                                        height: \"28px\",\n                                        background: \"rgba(255, 255, 255, 0.3)\",\n                                        borderRadius: \"8px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogIn, {\n                                        size: 14,\n                                        color: colors.sidebar.text\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"13px\",\n                                                fontWeight: \"600\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: \"rgba(255, 255, 255, 0.8)\",\n                                                fontSize: \"11px\",\n                                                fontWeight: \"400\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Access your account\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.surface,\n                    borderRadius: \"20px\",\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"40px\",\n                        height: \"100%\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                    lineNumber: 418,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 427,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"2fxYphseme7UlXU7fqnQRjVv+Ys=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayoutSimple.tsx\n"));

/***/ })

});