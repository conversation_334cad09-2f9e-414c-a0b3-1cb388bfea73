"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/SidebarLayoutSimple.tsx":
/*!********************************************!*\
  !*** ./components/SidebarLayoutSimple.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,Home,LogIn,MessageCircle,Search,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthModal */ \"./components/AuthModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    var _user_user_metadata, _user_email;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, profile, loading } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // User data - only use real data when authenticated\n    const userData = user ? {\n        name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || (user === null || user === void 0 ? void 0 : (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split(\"@\")[0]) || \"User\",\n        email: (user === null || user === void 0 ? void 0 : user.email) || \"\",\n        plan: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise\" : \"Free\",\n        avatar: (profile === null || profile === void 0 ? void 0 : profile.avatar_url) || null,\n        isOnline: (profile === null || profile === void 0 ? void 0 : profile.is_online) || false\n    } : null;\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        background: \"#F5F1EB\",\n        surface: \"#FFFFFF\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            surface: \"rgba(255, 107, 53, 0.05)\",\n            text: \"#2D1B14\",\n            textSecondary: \"#5D4037\",\n            textMuted: \"#8D6E63\",\n            accent: \"#FF6B35\",\n            accentSoft: \"#FF8A65\",\n            accentLight: \"#FFF7F4\",\n            border: \"rgba(255, 107, 53, 0.15)\",\n            hover: \"rgba(255, 107, 53, 0.08)\",\n            divider: \"rgba(255, 107, 53, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.25)\" // Orange glow\n        }\n    };\n    const menuItems = [\n        {\n            section: \"WORKSPACE\",\n            items: [\n                {\n                    href: \"/\",\n                    label: \"Briefing Room\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home\n                },\n                {\n                    href: \"/tweet-center\",\n                    label: \"Drafting Desk\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageCircle\n                },\n                {\n                    href: \"/schedule\",\n                    label: \"Content Scheduler\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Calendar\n                },\n                {\n                    href: \"/dashboard\",\n                    label: \"Growth Lab\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.BarChart3\n                },\n                {\n                    href: \"/meeting\",\n                    label: \"AI Meetings\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Video\n                }\n            ]\n        },\n        {\n            section: \"SETTINGS\",\n            items: [\n                {\n                    href: \"/settings\",\n                    label: \"Settings\",\n                    icon: _barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User\n                }\n            ]\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: colors.background,\n            padding: \"16px\",\n            gap: \"16px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    background: colors.sidebar.background,\n                    height: \"calc(100vh - 32px)\",\n                    borderRadius: \"24px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: \"0 8px 32px \".concat(colors.sidebar.glow, \", 0 2px 8px rgba(255, 107, 53, 0.1)\"),\n                    border: \"1px solid \".concat(colors.sidebar.border),\n                    overflow: \"hidden\",\n                    position: \"relative\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"24px 16px 20px 20px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"10px\",\n                            background: colors.sidebar.background\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"https://nlckamsrdiwkyyrxzntf.supabase.co/storage/v1/object/sign/logos/logoxe.png?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InN0b3JhZ2UtdXJsLXNpZ25pbmcta2V5X2RiNTE0YzE5LTlhNTQtNGZiNy1hMjY3LTJmNjY5ZDlhZjY1OCJ9.eyJ1cmwiOiJsb2dvcy9sb2dveGUucG5nIiwiaWF0IjoxNzQ4MjMxNDM1LCJleHAiOjE3NTA4MjM0MzV9.dw1yy3hjXvMy02IhFMKGw_-evgbmyYDuJ4m6HPP1Uec\",\n                                alt: \"Exie Logo\",\n                                style: {\n                                    height: \"28px\",\n                                    width: \"auto\",\n                                    objectFit: \"contain\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontFamily: \"Anek Tamil, sans-serif\",\n                                    fontSize: \"22px\",\n                                    fontWeight: \"600\",\n                                    color: colors.sidebar.text,\n                                    letterSpacing: \"-0.5px\",\n                                    lineHeight: \"1\"\n                                },\n                                children: \"Exie\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"0 16px 16px 16px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                display: \"flex\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Search, {\n                                    size: 14,\n                                    color: colors.sidebar.textMuted,\n                                    style: {\n                                        position: \"absolute\",\n                                        left: \"12px\",\n                                        zIndex: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search...\",\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"8px 12px 8px 36px\",\n                                        border: \"1px solid \".concat(colors.sidebar.border),\n                                        borderRadius: \"8px\",\n                                        background: colors.sidebar.surface,\n                                        color: colors.sidebar.text,\n                                        fontSize: \"12px\",\n                                        fontWeight: \"400\",\n                                        outline: \"none\",\n                                        transition: \"all 0.2s ease\"\n                                    },\n                                    onFocus: (e)=>{\n                                        e.target.style.borderColor = colors.sidebar.accent;\n                                        e.target.style.background = colors.sidebar.background;\n                                    },\n                                    onBlur: (e)=>{\n                                        e.target.style.borderColor = colors.sidebar.border;\n                                        e.target.style.background = colors.sidebar.surface;\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"8px 16px\",\n                            flex: 1,\n                            display: \"flex\",\n                            flexDirection: \"column\"\n                        },\n                        children: menuItems.map((section, sectionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: sectionIndex < menuItems.length - 1 ? \"24px\" : \"0\"\n                                },\n                                children: [\n                                    sectionIndex > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            height: \"1px\",\n                                            background: \"linear-gradient(90deg, transparent, \".concat(colors.sidebar.divider, \", transparent)\"),\n                                            margin: \"16px 12px 20px 12px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"10px\",\n                                            fontWeight: \"600\",\n                                            color: colors.sidebar.textMuted,\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"1px\",\n                                            marginBottom: \"10px\",\n                                            paddingLeft: \"12px\"\n                                        },\n                                        children: section.section\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\",\n                                            gap: \"2px\"\n                                        },\n                                        children: section.items.map((item)=>{\n                                            const active = isActive(item.href);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        padding: \"8px 12px\",\n                                                        borderRadius: \"8px\",\n                                                        background: active ? colors.sidebar.surface : \"transparent\",\n                                                        cursor: \"pointer\",\n                                                        transition: \"all 0.2s ease\",\n                                                        position: \"relative\"\n                                                    },\n                                                    onMouseEnter: (e)=>{\n                                                        if (!active) {\n                                                            e.currentTarget.style.background = colors.sidebar.hover;\n                                                        }\n                                                    },\n                                                    onMouseLeave: (e)=>{\n                                                        if (!active) {\n                                                            e.currentTarget.style.background = \"transparent\";\n                                                        }\n                                                    },\n                                                    children: [\n                                                        active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: \"absolute\",\n                                                                left: \"0\",\n                                                                top: \"50%\",\n                                                                transform: \"translateY(-50%)\",\n                                                                width: \"2px\",\n                                                                height: \"16px\",\n                                                                background: colors.sidebar.accent,\n                                                                borderRadius: \"0 2px 2px 0\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: \"16px\",\n                                                                height: \"16px\",\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                justifyContent: \"center\",\n                                                                marginRight: \"10px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 14,\n                                                                color: active ? colors.sidebar.accent : colors.sidebar.textSecondary,\n                                                                strokeWidth: 2\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                                fontSize: \"13px\",\n                                                                fontWeight: active ? \"500\" : \"400\",\n                                                                letterSpacing: \"0.1px\"\n                                                            },\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, item.href, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, section.section, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            margin: \"0 16px 16px 16px\",\n                            height: \"1px\",\n                            background: \"linear-gradient(90deg, transparent, \".concat(colors.sidebar.divider, \", transparent)\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"8px 16px 16px 16px\"\n                        },\n                        children: user && userData ? // Authenticated user - show profile\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"10px\",\n                                    padding: \"10px 12px\",\n                                    background: colors.sidebar.background,\n                                    borderRadius: \"14px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                    border: \"1px solid \".concat(colors.sidebar.border),\n                                    boxShadow: \"0 2px 8px \".concat(colors.sidebar.glow)\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.surface;\n                                    e.currentTarget.style.borderColor = colors.sidebar.accent + \"40\";\n                                    e.currentTarget.style.transform = \"translateY(-1px)\";\n                                    e.currentTarget.style.boxShadow = \"0 4px 16px \".concat(colors.sidebar.glow);\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = colors.sidebar.background;\n                                    e.currentTarget.style.borderColor = colors.sidebar.border;\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                    e.currentTarget.style.boxShadow = \"0 2px 8px \".concat(colors.sidebar.glow);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: userData.avatar ? \"transparent\" : \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\"),\n                                            borderRadius: \"10px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            overflow: \"hidden\",\n                                            border: \"2px solid \".concat(colors.sidebar.border)\n                                        },\n                                        children: [\n                                            userData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userData.avatar,\n                                                alt: userData.name,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"8px\"\n                                                },\n                                                onError: (e)=>{\n                                                    // Fallback to icon if image fails to load\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.parentElement.style.background = \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \", \").concat(colors.sidebar.accentSoft, \")\");\n                                                    e.currentTarget.parentElement.innerHTML = '<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path><circle cx=\"12\" cy=\"7\" r=\"4\"></circle></svg>';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 16,\n                                                color: \"#FFFFFF\",\n                                                strokeWidth: 2\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            userData.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"10px\",\n                                                    height: \"10px\",\n                                                    background: \"#00FF88\",\n                                                    borderRadius: \"50%\",\n                                                    border: \"2px solid #FFFFFF\",\n                                                    boxShadow: \"0 0 6px rgba(0, 255, 136, 0.8)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    lineHeight: \"1.2\",\n                                                    marginBottom: \"2px\"\n                                                },\n                                                children: userData.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.textMuted,\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"500\",\n                                                    lineHeight: \"1.2\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            userData.plan,\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: colors.sidebar.accent\n                                                        },\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, undefined) : // Not authenticated - show sign in button\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>setShowAuthModal(true),\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                padding: \"8px 12px\",\n                                borderRadius: \"8px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\",\n                                background: \"transparent\"\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.background = colors.sidebar.hover;\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.background = \"transparent\";\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"16px\",\n                                        height: \"16px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        marginRight: \"10px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Home_LogIn_MessageCircle_Search_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogIn, {\n                                        size: 14,\n                                        color: colors.sidebar.textSecondary,\n                                        strokeWidth: 2\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: colors.sidebar.textSecondary,\n                                        fontSize: \"13px\",\n                                        fontWeight: \"400\",\n                                        letterSpacing: \"0.1px\"\n                                    },\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.surface,\n                    borderRadius: \"24px\",\n                    height: \"calc(100vh - 32px)\",\n                    position: \"relative\",\n                    boxShadow: \"0 4px 20px rgba(255, 107, 53, 0.08)\",\n                    border: \"1px solid \".concat(colors.sidebar.border),\n                    overflow: \"hidden\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"32px\",\n                        height: \"100%\",\n                        overflow: \"auto\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 460,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"NDfMXAvUe5qStKhEmNPi31OHa30=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayoutSimple.tsx\n"));

/***/ })

});