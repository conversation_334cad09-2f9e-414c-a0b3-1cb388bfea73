"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/settings",{

/***/ "./pages/settings.tsx":
/*!****************************!*\
  !*** ./pages/settings.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!lucide-react */ \"__barrel_optimize__?names=Bell,Bot,Crown,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/supabase */ \"./lib/supabase.ts\");\n// pages/settings.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SettingsPage = ()=>{\n    _s();\n    const { user, profile, updateProfile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"agent-e\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Agent E Settings\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"My AI Project\");\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [targetAudience, setTargetAudience] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [brandVoice, setBrandVoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"professional\");\n    const [customPrompts, setCustomPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: \"Product Launch\",\n            prompt: \"Create engaging content for product launches with excitement and clear benefits\"\n        },\n        {\n            id: 2,\n            name: \"Educational\",\n            prompt: \"Write informative content that teaches and provides value to the audience\"\n        }\n    ]);\n    // Profile Settings\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [avatarUrl, setAvatarUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // X Integration Settings\n    const [xAccountConnected, setXAccountConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [xAccountInfo, setXAccountInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connectingX, setConnectingX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load settings from Supabase\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            var _user_user_metadata;\n            loadSettings();\n            loadXAccountStatus();\n            setFullName((profile === null || profile === void 0 ? void 0 : profile.full_name) || ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"\");\n            setEmail(user.email || \"\");\n            setAvatarUrl((profile === null || profile === void 0 ? void 0 : profile.avatar_url) || \"\");\n        }\n    }, [\n        user,\n        profile\n    ]);\n    // Handle URL parameters for success/error messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const urlParams = new URLSearchParams(window.location.search);\n        const success = urlParams.get(\"success\");\n        const error = urlParams.get(\"error\");\n        if (success === \"connected\") {\n            alert(\"X account connected successfully!\");\n            loadXAccountStatus(); // Refresh the status\n            // Clean up URL\n            window.history.replaceState({}, \"\", \"/settings?tab=integrations\");\n        } else if (error) {\n            const errorMessages = {\n                \"store_failed\": \"Failed to save X account connection. Please try again.\",\n                \"auth_failed\": \"X authorization failed. Please try again.\",\n                \"invalid_token\": \"Invalid authorization token. Please try again.\"\n            };\n            alert(errorMessages[error] || \"An error occurred. Please try again.\");\n            // Clean up URL\n            window.history.replaceState({}, \"\", \"/settings?tab=integrations\");\n        }\n    }, []);\n    const loadSettings = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"agent_e_settings\").select(\"*\").eq(\"user_id\", user.id).single();\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error loading settings:\", error);\n                return;\n            }\n            if (data) {\n                setProjectName(data.project_name || \"My AI Project\");\n                setProjectDescription(data.project_description || \"\");\n                setTargetAudience(data.target_audience || \"\");\n                setBrandVoice(data.brand_voice || \"professional\");\n                setCustomPrompts(data.custom_prompts || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading settings:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveAgentESettings = async ()=>{\n        if (!user) return;\n        setSaving(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"agent_e_settings\").upsert({\n                user_id: user.id,\n                project_name: projectName,\n                project_description: projectDescription,\n                target_audience: targetAudience,\n                brand_voice: brandVoice,\n                custom_prompts: customPrompts\n            });\n            if (error) {\n                console.error(\"Error saving settings:\", error);\n                alert(\"Error saving settings. Please try again.\");\n            } else {\n                alert(\"Settings saved successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            alert(\"Error saving settings. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const saveProfileSettings = async ()=>{\n        if (!user) {\n            alert(\"You must be logged in to update your profile.\");\n            return;\n        }\n        setSaving(true);\n        try {\n            // Try to update the profile\n            const { error } = await updateProfile({\n                full_name: fullName,\n                avatar_url: avatarUrl || null\n            });\n            if (error) {\n                var _error_message;\n                console.error(\"Error updating profile:\", error);\n                // If the table doesn't exist, try to create it first\n                if (error.code === \"42P01\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('relation \"user_profiles\" does not exist'))) {\n                    alert(\"Database setup required. Please run the database schema first, then try again.\");\n                } else {\n                    alert(\"Error updating profile: \".concat(error.message || \"Please try again.\"));\n                }\n            } else {\n                alert(\"Profile updated successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            alert(\"Error updating profile. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const loadXAccountStatus = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/account-status?userId=\".concat(user.id));\n            const data = await response.json();\n            console.log(\"X Account Status Response:\", {\n                status: response.status,\n                connected: data.connected,\n                accountInfo: data.accountInfo,\n                error: data.error\n            });\n            if (response.ok && data.connected) {\n                setXAccountConnected(true);\n                setXAccountInfo(data.accountInfo);\n            } else {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n            }\n        } catch (error) {\n            console.error(\"Error loading X account status:\", error);\n            setXAccountConnected(false);\n            setXAccountInfo(null);\n        }\n    };\n    const handleConnectX = async ()=>{\n        if (!user) {\n            alert(\"Please log in first\");\n            return;\n        }\n        setConnectingX(true);\n        try {\n            const response = await fetch(\"/api/x/auth-url\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // Redirect directly to X authorization\n                window.location.href = data.authUrl;\n            } else {\n                alert(data.error || \"Failed to initiate X connection\");\n                setConnectingX(false);\n            }\n        } catch (error) {\n            console.error(\"Error connecting to X:\", error);\n            alert(\"Failed to connect to X. Please try again.\");\n            setConnectingX(false);\n        }\n    };\n    const handleVerifyPin = async ()=>{\n        if (!user || !pin.trim() || !oauthToken || !oauthTokenSecret) return;\n        setVerifyingPin(true);\n        try {\n            const response = await fetch(\"/api/x/verify-pin\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    oauth_token: oauthToken,\n                    oauth_token_secret: oauthTokenSecret,\n                    pin: pin.trim(),\n                    userId: user.id\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // Clear localStorage tokens\n                localStorage.removeItem(\"pending_x_oauth_token\");\n                localStorage.removeItem(\"pending_x_oauth_secret\");\n                // Update state\n                setXAccountConnected(true);\n                setXAccountInfo(data.accountInfo);\n                setShowPinModal(false);\n                setPin(\"\");\n                setOauthToken(\"\");\n                setOauthTokenSecret(\"\");\n                // Reload account status to make sure it's saved properly\n                await loadXAccountStatus();\n                alert(\"X account connected successfully!\");\n            } else {\n                alert(data.error || \"Failed to verify PIN\");\n            }\n        } catch (error) {\n            console.error(\"Error verifying PIN:\", error);\n            alert(\"Failed to verify PIN. Please try again.\");\n        } finally{\n            setVerifyingPin(false);\n        }\n    };\n    const handleDisconnectX = async ()=>{\n        if (!user) return;\n        try {\n            const response = await fetch(\"/api/x/disconnect\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            if (response.ok) {\n                setXAccountConnected(false);\n                setXAccountInfo(null);\n                alert(\"X account disconnected successfully\");\n            } else {\n                const data = await response.json();\n                alert(data.error || \"Failed to disconnect X account\");\n            }\n        } catch (error) {\n            console.error(\"Error disconnecting X:\", error);\n            alert(\"Failed to disconnect X account. Please try again.\");\n        }\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        surface: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\"\n    };\n    const tabs = [\n        {\n            id: \"agent-e\",\n            label: \"Agent E\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot\n        },\n        {\n            id: \"account\",\n            label: \"Account\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User\n        },\n        {\n            id: \"integrations\",\n            label: \"Integrations\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap\n        },\n        {\n            id: \"notifications\",\n            label: \"Notifications\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bell\n        },\n        {\n            id: \"security\",\n            label: \"Security\",\n            icon: _barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Shield\n        }\n    ];\n    const handleSavePrompt = (id, newPrompt)=>{\n        setCustomPrompts((prev)=>prev.map((p)=>p.id === id ? {\n                    ...p,\n                    prompt: newPrompt\n                } : p));\n    };\n    const addNewPrompt = ()=>{\n        const newId = Math.max(...customPrompts.map((p)=>p.id)) + 1;\n        setCustomPrompts((prev)=>[\n                ...prev,\n                {\n                    id: newId,\n                    name: \"New Prompt\",\n                    prompt: \"\"\n                }\n            ]);\n    };\n    const deletePrompt = (id)=>{\n        setCustomPrompts((prev)=>prev.filter((p)=>p.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.background\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"40px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"8px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        children: \"Settings\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            margin: 0,\n                            fontSize: \"16px\"\n                        },\n                        children: \"Configure Agent E and manage your account preferences\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    gap: \"8px\",\n                    marginBottom: \"40px\",\n                    borderBottom: \"1px solid \".concat(colors.border),\n                    paddingBottom: \"0\"\n                },\n                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(tab.id),\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"8px\",\n                            padding: \"12px 20px\",\n                            background: activeTab === tab.id ? colors.surface : \"transparent\",\n                            border: activeTab === tab.id ? \"1px solid \".concat(colors.border) : \"1px solid transparent\",\n                            borderBottom: activeTab === tab.id ? \"1px solid \".concat(colors.surface) : \"1px solid transparent\",\n                            borderRadius: \"8px 8px 0 0\",\n                            fontSize: \"14px\",\n                            fontWeight: \"500\",\n                            color: activeTab === tab.id ? colors.text.primary : colors.text.secondary,\n                            cursor: \"pointer\",\n                            transition: \"all 0.2s ease\",\n                            marginBottom: \"-1px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, undefined),\n                            tab.label\n                        ]\n                    }, tab.id, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 368,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: colors.surface,\n                    borderRadius: \"12px\",\n                    padding: \"32px\",\n                    border: \"1px solid \".concat(colors.border),\n                    minHeight: \"500px\"\n                },\n                children: [\n                    activeTab === \"agent-e\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Agent E Configuration\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Project Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gridTemplateColumns: \"1fr 1fr\",\n                                            gap: \"20px\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: \"Project Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: projectName,\n                                                        onChange: (e)=>setProjectName(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.secondary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: \"Brand Voice\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: brandVoice,\n                                                        onChange: (e)=>setBrandVoice(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.background,\n                                                            outline: \"none\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"professional\",\n                                                                children: \"Professional\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"casual\",\n                                                                children: \"Casual & Friendly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"technical\",\n                                                                children: \"Technical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"creative\",\n                                                                children: \"Creative & Fun\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"authoritative\",\n                                                                children: \"Authoritative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"Project Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: projectDescription,\n                                                onChange: (e)=>setProjectDescription(e.target.value),\n                                                placeholder: \"Describe your project, product, or service so Agent E can create relevant content...\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    padding: \"12px 16px\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    background: colors.background,\n                                                    outline: \"none\",\n                                                    minHeight: \"100px\",\n                                                    resize: \"vertical\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"Target Audience\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: targetAudience,\n                                                onChange: (e)=>setTargetAudience(e.target.value),\n                                                placeholder: \"e.g., Tech entrepreneurs, Small business owners, Developers...\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    padding: \"12px 16px\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    background: colors.background,\n                                                    outline: \"none\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    margin: 0\n                                                },\n                                                children: \"Custom Prompts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewPrompt,\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"6px\",\n                                                    padding: \"8px 16px\",\n                                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    cursor: \"pointer\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Plus, {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Add Prompt\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    customPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                border: \"1px solid \".concat(colors.border),\n                                                borderRadius: \"8px\",\n                                                padding: \"16px\",\n                                                marginBottom: \"12px\",\n                                                background: colors.background\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        alignItems: \"center\",\n                                                        marginBottom: \"12px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: prompt.name,\n                                                            onChange: (e)=>{\n                                                                setCustomPrompts((prev)=>prev.map((p)=>p.id === prompt.id ? {\n                                                                            ...p,\n                                                                            name: e.target.value\n                                                                        } : p));\n                                                            },\n                                                            style: {\n                                                                background: \"transparent\",\n                                                                border: \"none\",\n                                                                fontSize: \"16px\",\n                                                                fontWeight: \"600\",\n                                                                color: colors.text.primary,\n                                                                outline: \"none\",\n                                                                flex: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deletePrompt(prompt.id),\n                                                            style: {\n                                                                background: \"none\",\n                                                                border: \"none\",\n                                                                color: colors.text.tertiary,\n                                                                cursor: \"pointer\",\n                                                                padding: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Trash2, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: prompt.prompt,\n                                                    onChange: (e)=>handleSavePrompt(prompt.id, e.target.value),\n                                                    placeholder: \"Enter your custom prompt for Agent E...\",\n                                                    style: {\n                                                        width: \"100%\",\n                                                        padding: \"12px\",\n                                                        border: \"1px solid \".concat(colors.border),\n                                                        borderRadius: \"6px\",\n                                                        fontSize: \"14px\",\n                                                        background: colors.surface,\n                                                        outline: \"none\",\n                                                        minHeight: \"80px\",\n                                                        resize: \"vertical\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, prompt.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveAgentESettings,\n                                disabled: saving || !user,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 24px\",\n                                    background: saving || !user ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                    boxShadow: saving || !user ? \"none\" : \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                    opacity: saving || !user ? 0.6 : 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Save, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    saving ? \"Saving...\" : \"Save Agent E Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab === \"integrations\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Integrations\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"12px\",\n                                    padding: \"24px\",\n                                    marginBottom: \"24px\",\n                                    background: colors.background\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                size: 24,\n                                                color: colors.primary\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"600\",\n                                                    margin: 0\n                                                },\n                                                children: \"X (Twitter) Account\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: \"Connect your X account to enable automated posting and content scheduling through our premium service.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    xAccountConnected && xAccountInfo ? // Connected State\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            padding: \"16px\",\n                                            background: \"#F0FDF4\",\n                                            border: \"1px solid #BBF7D0\",\n                                            borderRadius: \"8px\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\"\n                                                },\n                                                children: [\n                                                    xAccountInfo.profile_image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: xAccountInfo.profile_image_url,\n                                                        alt: \"Profile\",\n                                                        style: {\n                                                            width: \"40px\",\n                                                            height: \"40px\",\n                                                            borderRadius: \"50%\",\n                                                            objectFit: \"cover\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: \"#065F46\",\n                                                                    fontSize: \"16px\",\n                                                                    fontWeight: \"600\"\n                                                                },\n                                                                children: xAccountInfo.name || \"Connected Account\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 753,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: \"#047857\",\n                                                                    fontSize: \"14px\"\n                                                                },\n                                                                children: [\n                                                                    \"@\",\n                                                                    xAccountInfo.username\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    padding: \"6px 12px\",\n                                                    background: \"#10B981\",\n                                                    color: \"white\",\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"Connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 768,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 17\n                                    }, undefined) : // Not Connected State\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px\",\n                                            background: \"#FEF3C7\",\n                                            border: \"1px solid #FDE68A\",\n                                            borderRadius: \"8px\",\n                                            marginBottom: \"16px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"#92400E\",\n                                                    fontSize: \"14px\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: \"No X account connected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 789,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"#B45309\",\n                                                    fontSize: \"12px\"\n                                                },\n                                                children: \"Connect your account to start scheduling posts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            gap: \"12px\"\n                                        },\n                                        children: xAccountConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDisconnectX,\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"10px 20px\",\n                                                background: \"#FEE2E2\",\n                                                color: \"#DC2626\",\n                                                border: \"1px solid #FECACA\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                cursor: \"pointer\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 823,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Disconnect Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 807,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                console.log(\"Connect X button clicked\");\n                                                handleConnectX();\n                                            },\n                                            disabled: connectingX,\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                padding: \"12px 24px\",\n                                                background: connectingX ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                color: \"white\",\n                                                border: \"none\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                cursor: connectingX ? \"not-allowed\" : \"pointer\",\n                                                opacity: connectingX ? 0.6 : 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Twitter, {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 848,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                connectingX ? \"Connecting...\" : \"Connect X Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 805,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab === \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User, {\n                                        size: 24,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 868,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Account Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Profile Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gap: \"16px\",\n                                            maxWidth: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Full Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: fullName,\n                                                        onChange: (e)=>setFullName(e.target.value),\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 884,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Email Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        disabled: true,\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: \"#F9F9F9\",\n                                                            color: colors.text.secondary,\n                                                            cursor: \"not-allowed\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 910,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            color: colors.text.primary,\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            marginBottom: \"6px\"\n                                                        },\n                                                        children: \"Avatar URL (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 938,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"url\",\n                                                        value: avatarUrl,\n                                                        onChange: (e)=>setAvatarUrl(e.target.value),\n                                                        placeholder: \"https://example.com/your-avatar.jpg\",\n                                                        style: {\n                                                            width: \"100%\",\n                                                            padding: \"12px 16px\",\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"8px\",\n                                                            fontSize: \"14px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            fontSize: \"12px\",\n                                                            color: colors.text.tertiary,\n                                                            marginTop: \"4px\",\n                                                            marginBottom: \"0\"\n                                                        },\n                                                        children: \"Enter a URL to your profile picture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 962,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 937,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 883,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 873,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"32px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: \"Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 976,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \"15 0%, \").concat(colors.primaryLight, \"15 100%)\"),\n                                            border: \"1px solid \".concat(colors.primary, \"30\"),\n                                            borderRadius: \"12px\",\n                                            padding: \"20px\",\n                                            maxWidth: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\",\n                                                    marginBottom: \"12px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"32px\",\n                                                            height: \"32px\",\n                                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                            borderRadius: \"8px\",\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Crown, {\n                                                            size: 16,\n                                                            color: \"white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 998,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: colors.text.primary,\n                                                                    fontSize: \"16px\",\n                                                                    fontWeight: \"600\"\n                                                                },\n                                                                children: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro Plan\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise Plan\" : \"Free Plan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 1010,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: colors.text.secondary,\n                                                                    fontSize: \"14px\"\n                                                                },\n                                                                children: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"free\" ? \"No subscription\" : (profile === null || profile === void 0 ? void 0 : profile.subscription_status) === \"active\" ? \"$29/month • Active\" : \"Subscription inactive\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                                lineNumber: 1017,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 992,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    gap: \"12px\",\n                                                    marginTop: \"16px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"8px 16px\",\n                                                            background: colors.surface,\n                                                            color: colors.text.primary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        children: \"Manage Billing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 1031,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"8px 16px\",\n                                                            background: \"transparent\",\n                                                            color: colors.text.secondary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"500\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        children: \"Cancel Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                lineNumber: 1026,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 985,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 975,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveProfileSettings,\n                                disabled: saving || !user,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"12px 24px\",\n                                    background: saving || !user ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    cursor: saving || !user ? \"not-allowed\" : \"pointer\",\n                                    boxShadow: saving || !user ? \"none\" : \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                    opacity: saving || !user ? 0.6 : 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bot_Crown_Plus_Save_Shield_Trash2_Twitter_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Save, {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 1079,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    saving ? \"Saving...\" : \"Save Account Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1060,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 858,\n                        columnNumber: 11\n                    }, undefined),\n                    activeTab !== \"agent-e\" && activeTab !== \"integrations\" && activeTab !== \"account\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"60px 20px\",\n                            color: colors.text.secondary\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginBottom: \"12px\"\n                                },\n                                children: \"Coming Soon\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1092,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"This section is under development.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                lineNumber: 1093,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                        lineNumber: 1087,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 403,\n                columnNumber: 7\n            }, undefined),\n            showPinModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: \"rgba(0, 0, 0, 0.5)\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: colors.surface,\n                        borderRadius: \"16px\",\n                        padding: \"32px\",\n                        width: \"90%\",\n                        maxWidth: \"400px\",\n                        textAlign: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                color: colors.text.primary,\n                                fontSize: \"20px\",\n                                fontWeight: \"600\",\n                                marginBottom: \"16px\"\n                            },\n                            children: \"\\uD83D\\uDD17 Connect Your X Account\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1120,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#F0F9FF\",\n                                border: \"1px solid #BAE6FD\",\n                                borderRadius: \"8px\",\n                                padding: \"16px\",\n                                marginBottom: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: \"#0369A1\",\n                                        fontSize: \"14px\",\n                                        marginBottom: \"12px\",\n                                        fontWeight: \"600\"\n                                    },\n                                    children: \"\\uD83D\\uDCCB Instructions:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1136,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    style: {\n                                        color: \"#0369A1\",\n                                        fontSize: \"13px\",\n                                        lineHeight: \"1.5\",\n                                        margin: 0,\n                                        paddingLeft: \"20px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"A new tab will open with X authorization page\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1151,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Click \"Authorize app\" on the X page'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1152,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Copy the PIN code shown on X\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1153,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Come back to this page\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                                    lineNumber: 1154,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \" and enter the PIN below\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1154,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Click \"Connect Account\" to finish'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                            lineNumber: 1155,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1144,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#FEF3C7\",\n                                        border: \"1px solid #FDE68A\",\n                                        borderRadius: \"6px\",\n                                        padding: \"8px 12px\",\n                                        marginTop: \"12px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: \"#92400E\",\n                                            fontSize: \"12px\",\n                                            margin: 0,\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"\\uD83D\\uDCA1 This modal will stay open even if you refresh the page!\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                        lineNumber: 1165,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1158,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1129,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: colors.text.secondary,\n                                fontSize: \"14px\",\n                                marginBottom: \"20px\",\n                                textAlign: \"center\",\n                                fontWeight: \"600\"\n                            },\n                            children: \"Enter the PIN code from X:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1176,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: pin,\n                            onChange: (e)=>setPin(e.target.value),\n                            placeholder: \"Enter PIN code\",\n                            style: {\n                                width: \"100%\",\n                                padding: \"12px 16px\",\n                                border: \"1px solid \".concat(colors.border),\n                                borderRadius: \"8px\",\n                                fontSize: \"16px\",\n                                textAlign: \"center\",\n                                marginBottom: \"24px\",\n                                outline: \"none\"\n                            },\n                            maxLength: 7\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1186,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                gap: \"8px\",\n                                justifyContent: \"center\",\n                                flexWrap: \"wrap\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        // Clear localStorage tokens\n                                        localStorage.removeItem(\"pending_x_oauth_token\");\n                                        localStorage.removeItem(\"pending_x_oauth_secret\");\n                                        // Clear state\n                                        setShowPinModal(false);\n                                        setPin(\"\");\n                                        setOauthToken(\"\");\n                                        setOauthTokenSecret(\"\");\n                                    },\n                                    style: {\n                                        padding: \"12px 20px\",\n                                        background: colors.background,\n                                        color: colors.text.primary,\n                                        border: \"1px solid \".concat(colors.border),\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1205,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        // Re-open X authorization page\n                                        const authUrl = \"https://api.twitter.com/oauth/authorize?oauth_token=\".concat(oauthToken);\n                                        window.open(authUrl, \"_blank\");\n                                    },\n                                    disabled: !oauthToken,\n                                    style: {\n                                        padding: \"12px 20px\",\n                                        background: \"#1DA1F2\",\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: \"Open X Page\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1231,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleVerifyPin,\n                                    disabled: !pin.trim() || verifyingPin,\n                                    style: {\n                                        padding: \"12px 24px\",\n                                        background: !pin.trim() || verifyingPin ? colors.text.tertiary : \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        cursor: !pin.trim() || verifyingPin ? \"not-allowed\" : \"pointer\"\n                                    },\n                                    children: verifyingPin ? \"Verifying...\" : \"Connect Account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                                    lineNumber: 1252,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                            lineNumber: 1204,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                    lineNumber: 1112,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n                lineNumber: 1100,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 337,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SettingsPage, \"yIA732Jes0SuxAsQsMw7aGMWO2k=\", false, function() {\n    return [\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser\n    ];\n});\n_c = SettingsPage;\nSettingsPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/settings.tsx\",\n        lineNumber: 1280,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/settings.tsx\n"));

/***/ })

});