"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!lucide-react */ \"__barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_AgentEChatSimple__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/AgentEChatSimple */ \"./components/AgentEChatSimple.tsx\");\n// pages/tweet-center.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst TweetCenterPage = ()=>{\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showAgentE, setShowAgentE] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showAgentEInput, setShowAgentEInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [agentEPrompt, setAgentEPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        borderHover: \"#E8D5C4\",\n        surface: \"#FFFFFF\",\n        surfaceHover: \"#F9F7F4\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Real AI prediction based on content context\n    const generateContextualSuggestion = (text)=>{\n        if (text.length < 10) return \"\";\n        try {\n            var _text_split_pop;\n            // Simple prediction logic based on sentence patterns\n            const lastSentence = ((_text_split_pop = text.split(\".\").pop()) === null || _text_split_pop === void 0 ? void 0 : _text_split_pop.trim()) || text;\n            // If sentence seems incomplete, suggest completion\n            if (lastSentence.length > 0) {\n                // Sports context\n                if (lastSentence.toLowerCase().includes(\"sports\") || lastSentence.toLowerCase().includes(\"game\") || lastSentence.toLowerCase().includes(\"team\")) {\n                    const sportsSuggestions = [\n                        \" requires dedication and consistent practice\",\n                        \" teaches us valuable life lessons\",\n                        \" brings people together like nothing else\",\n                        \" is more than just competition\"\n                    ];\n                    return sportsSuggestions[Math.floor(Math.random() * sportsSuggestions.length)];\n                }\n                // Tech context\n                if (lastSentence.toLowerCase().includes(\"technology\") || lastSentence.toLowerCase().includes(\"coding\") || lastSentence.toLowerCase().includes(\"software\")) {\n                    const techSuggestions = [\n                        \" is evolving faster than ever before\",\n                        \" has the power to solve real problems\",\n                        \" requires continuous learning and adaptation\",\n                        \" should be accessible to everyone\"\n                    ];\n                    return techSuggestions[Math.floor(Math.random() * techSuggestions.length)];\n                }\n                // Business context\n                if (lastSentence.toLowerCase().includes(\"business\") || lastSentence.toLowerCase().includes(\"startup\") || lastSentence.toLowerCase().includes(\"entrepreneur\")) {\n                    const businessSuggestions = [\n                        \" is about solving problems for people\",\n                        \" requires patience and persistence\",\n                        \" success comes from understanding your customers\",\n                        \" failure is just feedback in disguise\"\n                    ];\n                    return businessSuggestions[Math.floor(Math.random() * businessSuggestions.length)];\n                }\n                // General sentence completion based on common patterns\n                if (lastSentence.endsWith(\"I think\") || lastSentence.endsWith(\"I believe\")) {\n                    return \" that consistency beats perfection every time\";\n                }\n                if (lastSentence.includes(\"The key to\")) {\n                    return \" success is taking action despite uncertainty\";\n                }\n                if (lastSentence.includes(\"What I learned\")) {\n                    return \" is that small steps lead to big changes\";\n                }\n                // Default contextual completions\n                const generalSuggestions = [\n                    \" and here's why that matters\",\n                    \" - let me explain\",\n                    \" in my experience\",\n                    \" based on what I've seen\",\n                    \" and the results speak for themselves\"\n                ];\n                return generalSuggestions[Math.floor(Math.random() * generalSuggestions.length)];\n            }\n            return \"\";\n        } catch (error) {\n            console.error(\"Error generating suggestion:\", error);\n            return \"\";\n        }\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    const autoFormat = ()=>{\n        if (content.length > 280) {\n            // Convert to thread format\n            const sentences = content.split(\". \");\n            const threads = [];\n            let currentThread = \"\";\n            sentences.forEach((sentence)=>{\n                if ((currentThread + sentence + \". \").length <= 280) {\n                    currentThread += sentence + \". \";\n                } else {\n                    if (currentThread) threads.push(currentThread.trim());\n                    currentThread = sentence + \". \";\n                }\n            });\n            if (currentThread) threads.push(currentThread.trim());\n            setContent(threads.join(\"\\n\\n\"));\n            setFormatMode(\"thread\");\n        }\n    };\n    const handleAgentESubmit = (e)=>{\n        e.preventDefault();\n        if (!agentEPrompt.trim()) return;\n        // Generate content based on the prompt\n        const generatedContent = generateContentFromPrompt(agentEPrompt);\n        setContent(generatedContent);\n        setAgentEPrompt(\"\");\n        setShowAgentEInput(false);\n    };\n    const generateContentFromPrompt = (prompt)=>{\n        const lowerPrompt = prompt.toLowerCase();\n        // Different content types based on prompt\n        if (lowerPrompt.includes(\"thread\") || lowerPrompt.includes(\"twitter thread\")) {\n            return \"Here's a thread about \".concat(prompt.replace(/thread|twitter thread/gi, \"\").trim(), \":\\n\\n1/ The key to understanding this topic is...\\n\\n2/ Most people think...\\n\\n3/ But here's what actually works...\");\n        }\n        if (lowerPrompt.includes(\"tips\") || lowerPrompt.includes(\"advice\")) {\n            return \"\".concat(prompt.charAt(0).toUpperCase() + prompt.slice(1), \":\\n\\n• Focus on the fundamentals first\\n• Consistency beats perfection\\n• Learn from others who've succeeded\\n• Take action despite uncertainty\");\n        }\n        if (lowerPrompt.includes(\"story\") || lowerPrompt.includes(\"experience\")) {\n            return \"Here's my experience with \".concat(prompt.replace(/story|experience/gi, \"\").trim(), \":\\n\\nIt started when I realized that most advice online was generic. I needed something that actually worked in the real world...\");\n        }\n        // Default content generation\n        return \"\".concat(prompt.charAt(0).toUpperCase() + prompt.slice(1), \".\\n\\nHere's what I've learned from years of experience: the biggest difference between success and failure isn't talent or luck—it's consistency.\\n\\nMost people give up right before they would have succeeded.\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.paper,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n        },\n        className: \"jsx-3f45dd3580662bfa\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"800px\",\n                    marginBottom: \"40px\",\n                    textAlign: \"center\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"12px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            gap: \"16px\",\n                            marginTop: \"24px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"6px 12px\",\n                                    background: aiEnabled ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(aiEnabled ? colors.primary : colors.border),\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onClick: ()=>setAiEnabled(!aiEnabled),\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Sparkles, {\n                                        size: 14,\n                                        color: aiEnabled ? colors.primary : colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: aiEnabled ? colors.primary : colors.text.tertiary\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: \"AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: autoFormat,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: colors.surface,\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"500\",\n                                    color: colors.text.secondary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Type, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Auto Format\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: formatMode === \"thread\" ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(formatMode === \"thread\" ? colors.primary : colors.border)\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    formatMode === \"thread\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__.AlignLeft, {\n                                        size: 14,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 40\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__.AlignCenter, {\n                                        size: 14,\n                                        color: colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 89\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: formatMode === \"thread\" ? colors.primary : colors.text.tertiary\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: formatMode === \"thread\" ? \"Thread\" : \"Single\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAgentE(true),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    border: \"none\",\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"600\",\n                                    color: \"white\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    boxShadow: \"0 2px 8px \".concat(colors.primary, \"30\")\n                                },\n                                onMouseEnter: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(-1px)\";\n                                    target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"40\");\n                                },\n                                onMouseLeave: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(0)\";\n                                    target.style.boxShadow = \"0 2px 8px \".concat(colors.primary, \"30\");\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Agent E\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    background: \"transparent\",\n                    position: \"relative\",\n                    minHeight: \"500px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            position: \"relative\",\n                            padding: \"40px 60px\",\n                            minHeight: \"450px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            showSuggestion && aiEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"40px\",\n                                    left: \"60px\",\n                                    right: \"60px\",\n                                    bottom: \"40px\",\n                                    pointerEvents: \"none\",\n                                    zIndex: 1,\n                                    overflow: \"hidden\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"20px\",\n                                        lineHeight: \"1.7\",\n                                        color: \"transparent\",\n                                        whiteSpace: \"pre-wrap\",\n                                        wordWrap: \"break-word\",\n                                        letterSpacing: \"0.3px\",\n                                        fontWeight: \"400\"\n                                    },\n                                    className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                    children: [\n                                        content,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#9CA3AF\",\n                                                opacity: 0.6,\n                                                fontStyle: \"normal\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: aiSuggestion\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: content,\n                                onChange: (e)=>setContent(e.target.value),\n                                onKeyDown: handleTabPress,\n                                placeholder: aiEnabled ? \"Start writing...\" : \"What's on your mind?\",\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    minHeight: \"400px\",\n                                    padding: \"0\",\n                                    border: \"none\",\n                                    background: \"transparent\",\n                                    fontSize: \"20px\",\n                                    lineHeight: \"1.7\",\n                                    color: colors.text.primary,\n                                    resize: \"none\",\n                                    outline: \"none\",\n                                    letterSpacing: \"0.3px\",\n                                    position: \"relative\",\n                                    zIndex: 2,\n                                    fontWeight: \"400\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            padding: \"0 60px 40px\",\n                            marginTop: \"20px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"400\",\n                                    opacity: 0.7\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                children: [\n                                    content.length,\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setContent(\"\"),\n                                        style: {\n                                            padding: \"12px 24px\",\n                                            background: \"transparent\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            borderRadius: \"10px\",\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = colors.surfaceHover;\n                                            target.style.borderColor = colors.borderHover;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = \"transparent\";\n                                            target.style.borderColor = colors.border;\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Save Draft\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // Publish logic here\n                                            console.log(\"Publishing:\", content);\n                                        },\n                                        style: {\n                                            padding: \"12px 28px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            border: \"none\",\n                                            borderRadius: \"10px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(-1px)\";\n                                            target.style.boxShadow = \"0 6px 16px \".concat(colors.primary, \"40\");\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(0)\";\n                                            target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"30\");\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Publish\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"fixed\",\n                            bottom: \"24px\",\n                            left: \"50%\",\n                            transform: \"translateX(-50%)\",\n                            width: \"600px\",\n                            height: \"48px\",\n                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                            borderRadius: \"24px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            padding: \"0 20px\",\n                            boxShadow: \"0 8px 32px \".concat(colors.primary, \"30, 0 2px 8px rgba(0, 0, 0, 0.1)\"),\n                            cursor: \"pointer\",\n                            transition: \"all 0.3s ease\",\n                            zIndex: 1000,\n                            border: \"1px solid \".concat(colors.primary, \"20\")\n                        },\n                        onClick: ()=>setShowAgentE(true),\n                        onMouseEnter: (e)=>{\n                            e.currentTarget.style.transform = \"translateX(-50%) translateY(-2px)\";\n                            e.currentTarget.style.boxShadow = \"0 12px 40px \".concat(colors.primary, \"40, 0 4px 12px rgba(0, 0, 0, 0.15)\");\n                        },\n                        onMouseLeave: (e)=>{\n                            e.currentTarget.style.transform = \"translateX(-50%) translateY(0)\";\n                            e.currentTarget.style.boxShadow = \"0 8px 32px \".concat(colors.primary, \"30, 0 2px 8px rgba(0, 0, 0, 0.1)\");\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot, {\n                                size: 18,\n                                color: \"white\",\n                                style: {\n                                    marginRight: \"12px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    color: \"white\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    flex: 1\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: \"Ask Agent E for help with your content...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: \"6px\",\n                                    height: \"6px\",\n                                    background: \"rgba(255, 255, 255, 0.8)\",\n                                    borderRadius: \"50%\",\n                                    animation: \"pulse 2s infinite\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentEChatSimple__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showAgentE,\n                onClose: ()=>setShowAgentE(false),\n                currentContent: content\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 533,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"3f45dd3580662bfa\",\n                children: \"@-webkit-keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);transform:scale(1.2)}}@-moz-keyframes pulse{0%,100%{opacity:.8;-moz-transform:scale(1);transform:scale(1)}50%{opacity:1;-moz-transform:scale(1.2);transform:scale(1.2)}}@-o-keyframes pulse{0%,100%{opacity:.8;-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-o-transform:scale(1.2);transform:scale(1.2)}}@keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-o-transform:scale(1.2);transform:scale(1.2)}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TweetCenterPage, \"wJrcYQKhK6mHhasX9nKwi3Fz+xY=\");\n_c = TweetCenterPage;\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 551,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (TweetCenterPage);\nvar _c;\n$RefreshReg$(_c, \"TweetCenterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n"));

/***/ })

});