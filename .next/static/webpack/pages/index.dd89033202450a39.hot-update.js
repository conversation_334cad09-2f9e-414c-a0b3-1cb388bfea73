"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/SidebarLayoutSimple.tsx":
/*!********************************************!*\
  !*** ./components/SidebarLayoutSimple.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Home,LogIn,MessageCircle,User,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/UserContext */ \"./contexts/UserContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthModal */ \"./components/AuthModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    var _user_user_metadata;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, profile } = (0,_contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Fallback user data for when not authenticated or loading\n    const userData = {\n        name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || \"Alex Chen\",\n        email: (user === null || user === void 0 ? void 0 : user.email) || \"<EMAIL>\",\n        plan: (profile === null || profile === void 0 ? void 0 : profile.plan) === \"pro\" ? \"Pro\" : (profile === null || profile === void 0 ? void 0 : profile.plan) === \"enterprise\" ? \"Enterprise\" : \"Free\",\n        avatar: (profile === null || profile === void 0 ? void 0 : profile.avatar_url) || null,\n        isOnline: (profile === null || profile === void 0 ? void 0 : profile.is_online) || false\n    };\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        background: \"#F5F1EB\",\n        surface: \"#FFFFFF\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        sidebar: {\n            background: \"#2A2A2A\",\n            surface: \"#333333\",\n            text: \"#E8E8E8\",\n            textSecondary: \"#B8B8B8\",\n            accent: \"#FF6B35\",\n            border: \"#404040\",\n            hover: \"#3A3A3A\" // Hover state\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageCircle\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.BarChart3\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Video\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: colors.background,\n            padding: \"16px\",\n            gap: \"16px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"220px\",\n                    background: colors.sidebar.background,\n                    height: \"calc(100vh - 32px)\",\n                    borderRadius: \"16px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08)\",\n                    border: \"1px solid \".concat(colors.sidebar.border),\n                    overflow: \"hidden\" // Prevent content overflow\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"20px 24px 16px 24px\",\n                            borderBottom: \"1px solid \".concat(colors.sidebar.border)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"12px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"32px\",\n                                        height: \"32px\",\n                                        background: \"linear-gradient(135deg, \".concat(colors.sidebar.accent, \" 0%, #FF8A65 100%)\"),\n                                        borderRadius: \"8px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        boxShadow: \"0 4px 12px rgba(255, 107, 53, 0.3)\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"18px\",\n                                            color: \"#FFFFFF\",\n                                            fontFamily: \"Georgia, serif\",\n                                            fontStyle: \"italic\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"ℰ\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"16px\",\n                                                fontWeight: \"700\",\n                                                fontFamily: \"Georgia, serif\",\n                                                fontStyle: \"italic\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Exie\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.textSecondary,\n                                                fontSize: \"11px\",\n                                                fontWeight: \"500\",\n                                                letterSpacing: \"0.5px\",\n                                                textTransform: \"uppercase\"\n                                            },\n                                            children: \"AI Assistant\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"20px 16px\",\n                            flex: 1,\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"2px\"\n                        },\n                        children: menuItems.map((item, index)=>{\n                            const active = isActive(item.href);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"12px 16px\",\n                                                borderRadius: \"12px\",\n                                                background: active ? colors.sidebar.surface : \"transparent\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                border: active ? \"1px solid \".concat(colors.sidebar.accent, \"20\") : \"1px solid transparent\",\n                                                position: \"relative\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                if (!active) {\n                                                    e.currentTarget.style.background = colors.sidebar.hover;\n                                                    e.currentTarget.style.transform = \"translateX(2px)\";\n                                                }\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                if (!active) {\n                                                    e.currentTarget.style.background = \"transparent\";\n                                                    e.currentTarget.style.transform = \"translateX(0)\";\n                                                }\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"0\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"3px\",\n                                                        height: \"20px\",\n                                                        background: colors.sidebar.accent,\n                                                        borderRadius: \"0 2px 2px 0\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"20px\",\n                                                        height: \"20px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"12px\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        size: 16,\n                                                        color: active ? colors.sidebar.accent : colors.sidebar.textSecondary,\n                                                        strokeWidth: active ? 2.5 : 2\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                        fontSize: \"14px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        letterSpacing: \"0.2px\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    index < menuItems.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            margin: \"8px 0\",\n                                            opacity: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"20px\",\n                                                height: \"1px\",\n                                                background: colors.sidebar.textSecondary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, item.href, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"12px\",\n                            borderTop: \"1px solid rgba(255, 255, 255, 0.3)\"\n                        },\n                        children: user ? // Authenticated user - show profile\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"10px\",\n                                    padding: \"10px\",\n                                    background: \"rgba(255, 255, 255, 0.15)\",\n                                    borderRadius: \"10px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.25)\";\n                                    e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.4)\";\n                                    e.currentTarget.style.transform = \"translateY(-1px)\";\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.15)\";\n                                    e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.2)\";\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"28px\",\n                                            height: \"28px\",\n                                            background: userData.avatar ? \"transparent\" : \"rgba(255, 255, 255, 0.3)\",\n                                            borderRadius: \"8px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            overflow: \"hidden\"\n                                        },\n                                        children: [\n                                            userData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userData.avatar,\n                                                alt: userData.name,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"8px\"\n                                                },\n                                                onError: (e)=>{\n                                                    // Fallback to icon if image fails to load\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.parentElement.style.background = \"rgba(255, 255, 255, 0.3)\";\n                                                    e.currentTarget.parentElement.innerHTML = '<svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"'.concat(colors.sidebar.text, '\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path><circle cx=\"12\" cy=\"7\" r=\"4\"></circle></svg>');\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                size: 14,\n                                                color: colors.sidebar.text\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            userData.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-2px\",\n                                                    right: \"-2px\",\n                                                    width: \"8px\",\n                                                    height: \"8px\",\n                                                    background: \"#00FF88\",\n                                                    borderRadius: \"50%\",\n                                                    border: \"2px solid rgba(255, 255, 255, 0.9)\",\n                                                    boxShadow: \"0 0 4px rgba(0, 255, 136, 0.6)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    lineHeight: \"1.2\"\n                                                },\n                                                children: userData.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: \"rgba(255, 255, 255, 0.8)\",\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"400\",\n                                                    lineHeight: \"1.2\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            userData.plan,\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, undefined) : // Not authenticated - show sign in button\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>setShowAuthModal(true),\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"10px\",\n                                padding: \"10px\",\n                                background: \"rgba(255, 255, 255, 0.15)\",\n                                borderRadius: \"10px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\",\n                                border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.25)\";\n                                e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.4)\";\n                                e.currentTarget.style.transform = \"translateY(-1px)\";\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.background = \"rgba(255, 255, 255, 0.15)\";\n                                e.currentTarget.style.borderColor = \"rgba(255, 255, 255, 0.2)\";\n                                e.currentTarget.style.transform = \"translateY(0)\";\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"28px\",\n                                        height: \"28px\",\n                                        background: \"rgba(255, 255, 255, 0.3)\",\n                                        borderRadius: \"8px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_LogIn_MessageCircle_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogIn, {\n                                        size: 14,\n                                        color: colors.sidebar.text\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"13px\",\n                                                fontWeight: \"600\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: \"rgba(255, 255, 255, 0.8)\",\n                                                fontSize: \"11px\",\n                                                fontWeight: \"400\",\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Access your account\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.surface,\n                    borderRadius: \"20px\",\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"40px\",\n                        height: \"100%\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayoutSimple.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"2fxYphseme7UlXU7fqnQRjVv+Ys=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _contexts_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./components/SidebarLayoutSimple.tsx\n"));

/***/ })

});