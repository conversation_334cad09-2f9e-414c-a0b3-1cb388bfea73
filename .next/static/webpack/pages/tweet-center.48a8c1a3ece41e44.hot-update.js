"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayoutSimple */ \"./components/SidebarLayoutSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!lucide-react */ \"__barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_AgentEChatSimple__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/AgentEChatSimple */ \"./components/AgentEChatSimple.tsx\");\n// pages/tweet-center.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst TweetCenterPage = ()=>{\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showAgentE, setShowAgentE] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showAgentEInput, setShowAgentEInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [agentEPrompt, setAgentEPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"single\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        borderHover: \"#E8D5C4\",\n        surface: \"#FFFFFF\",\n        surfaceHover: \"#F9F7F4\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Real AI prediction based on content context\n    const generateContextualSuggestion = (text)=>{\n        if (text.length < 10) return \"\";\n        try {\n            var _text_split_pop;\n            // Simple prediction logic based on sentence patterns\n            const lastSentence = ((_text_split_pop = text.split(\".\").pop()) === null || _text_split_pop === void 0 ? void 0 : _text_split_pop.trim()) || text;\n            // If sentence seems incomplete, suggest completion\n            if (lastSentence.length > 0) {\n                // Sports context\n                if (lastSentence.toLowerCase().includes(\"sports\") || lastSentence.toLowerCase().includes(\"game\") || lastSentence.toLowerCase().includes(\"team\")) {\n                    const sportsSuggestions = [\n                        \" requires dedication and consistent practice\",\n                        \" teaches us valuable life lessons\",\n                        \" brings people together like nothing else\",\n                        \" is more than just competition\"\n                    ];\n                    return sportsSuggestions[Math.floor(Math.random() * sportsSuggestions.length)];\n                }\n                // Tech context\n                if (lastSentence.toLowerCase().includes(\"technology\") || lastSentence.toLowerCase().includes(\"coding\") || lastSentence.toLowerCase().includes(\"software\")) {\n                    const techSuggestions = [\n                        \" is evolving faster than ever before\",\n                        \" has the power to solve real problems\",\n                        \" requires continuous learning and adaptation\",\n                        \" should be accessible to everyone\"\n                    ];\n                    return techSuggestions[Math.floor(Math.random() * techSuggestions.length)];\n                }\n                // Business context\n                if (lastSentence.toLowerCase().includes(\"business\") || lastSentence.toLowerCase().includes(\"startup\") || lastSentence.toLowerCase().includes(\"entrepreneur\")) {\n                    const businessSuggestions = [\n                        \" is about solving problems for people\",\n                        \" requires patience and persistence\",\n                        \" success comes from understanding your customers\",\n                        \" failure is just feedback in disguise\"\n                    ];\n                    return businessSuggestions[Math.floor(Math.random() * businessSuggestions.length)];\n                }\n                // General sentence completion based on common patterns\n                if (lastSentence.endsWith(\"I think\") || lastSentence.endsWith(\"I believe\")) {\n                    return \" that consistency beats perfection every time\";\n                }\n                if (lastSentence.includes(\"The key to\")) {\n                    return \" success is taking action despite uncertainty\";\n                }\n                if (lastSentence.includes(\"What I learned\")) {\n                    return \" is that small steps lead to big changes\";\n                }\n                // Default contextual completions\n                const generalSuggestions = [\n                    \" and here's why that matters\",\n                    \" - let me explain\",\n                    \" in my experience\",\n                    \" based on what I've seen\",\n                    \" and the results speak for themselves\"\n                ];\n                return generalSuggestions[Math.floor(Math.random() * generalSuggestions.length)];\n            }\n            return \"\";\n        } catch (error) {\n            console.error(\"Error generating suggestion:\", error);\n            return \"\";\n        }\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    const autoFormat = ()=>{\n        if (content.length > 280) {\n            // Convert to thread format\n            const sentences = content.split(\". \");\n            const threads = [];\n            let currentThread = \"\";\n            sentences.forEach((sentence)=>{\n                if ((currentThread + sentence + \". \").length <= 280) {\n                    currentThread += sentence + \". \";\n                } else {\n                    if (currentThread) threads.push(currentThread.trim());\n                    currentThread = sentence + \". \";\n                }\n            });\n            if (currentThread) threads.push(currentThread.trim());\n            setContent(threads.join(\"\\n\\n\"));\n            setFormatMode(\"thread\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.paper,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n        },\n        className: \"jsx-3f45dd3580662bfa\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"800px\",\n                    marginBottom: \"40px\",\n                    textAlign: \"center\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"12px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            gap: \"16px\",\n                            marginTop: \"24px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"6px 12px\",\n                                    background: aiEnabled ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(aiEnabled ? colors.primary : colors.border),\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onClick: ()=>setAiEnabled(!aiEnabled),\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Sparkles, {\n                                        size: 14,\n                                        color: aiEnabled ? colors.primary : colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: aiEnabled ? colors.primary : colors.text.tertiary\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: \"AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: autoFormat,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: colors.surface,\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"500\",\n                                    color: colors.text.secondary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Type, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Auto Format\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: formatMode === \"thread\" ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(formatMode === \"thread\" ? colors.primary : colors.border)\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    formatMode === \"thread\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__.AlignLeft, {\n                                        size: 14,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 40\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__.AlignCenter, {\n                                        size: 14,\n                                        color: colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 89\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: formatMode === \"thread\" ? colors.primary : colors.text.tertiary\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\",\n                                        children: formatMode === \"thread\" ? \"Thread\" : \"Single\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAgentE(true),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                    border: \"none\",\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"600\",\n                                    color: \"white\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    boxShadow: \"0 2px 8px \".concat(colors.primary, \"30\")\n                                },\n                                onMouseEnter: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(-1px)\";\n                                    target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"40\");\n                                },\n                                onMouseLeave: (e)=>{\n                                    const target = e.target;\n                                    target.style.transform = \"translateY(0)\";\n                                    target.style.boxShadow = \"0 2px 8px \".concat(colors.primary, \"30\");\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Agent E\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    background: \"transparent\",\n                    position: \"relative\",\n                    minHeight: \"500px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                className: \"jsx-3f45dd3580662bfa\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            position: \"relative\",\n                            padding: \"40px 60px\",\n                            minHeight: \"450px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            showSuggestion && aiEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"40px\",\n                                    left: \"60px\",\n                                    right: \"60px\",\n                                    bottom: \"40px\",\n                                    pointerEvents: \"none\",\n                                    zIndex: 1,\n                                    overflow: \"hidden\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"20px\",\n                                        lineHeight: \"1.7\",\n                                        color: \"transparent\",\n                                        whiteSpace: \"pre-wrap\",\n                                        wordWrap: \"break-word\",\n                                        letterSpacing: \"0.3px\",\n                                        fontWeight: \"400\"\n                                    },\n                                    className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                    children: [\n                                        content,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#9CA3AF\",\n                                                opacity: 0.6,\n                                                fontStyle: \"normal\"\n                                            },\n                                            className: \"jsx-3f45dd3580662bfa\",\n                                            children: aiSuggestion\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: content,\n                                onChange: (e)=>setContent(e.target.value),\n                                onKeyDown: handleTabPress,\n                                placeholder: aiEnabled ? \"Start writing...\" : \"What's on your mind?\",\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    minHeight: \"400px\",\n                                    padding: \"0\",\n                                    border: \"none\",\n                                    background: \"transparent\",\n                                    fontSize: \"20px\",\n                                    lineHeight: \"1.7\",\n                                    color: colors.text.primary,\n                                    resize: \"none\",\n                                    outline: \"none\",\n                                    letterSpacing: \"0.3px\",\n                                    position: \"relative\",\n                                    zIndex: 2,\n                                    fontWeight: \"400\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            padding: \"0 60px 40px\",\n                            marginTop: \"20px\"\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"400\",\n                                    opacity: 0.7\n                                },\n                                className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                children: [\n                                    content.length,\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setContent(\"\"),\n                                        style: {\n                                            padding: \"12px 24px\",\n                                            background: \"transparent\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            borderRadius: \"10px\",\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = colors.surfaceHover;\n                                            target.style.borderColor = colors.borderHover;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = \"transparent\";\n                                            target.style.borderColor = colors.border;\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Save Draft\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // Publish logic here\n                                            console.log(\"Publishing:\", content);\n                                        },\n                                        style: {\n                                            padding: \"12px 28px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            border: \"none\",\n                                            borderRadius: \"10px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"30\"),\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(-1px)\";\n                                            target.style.boxShadow = \"0 6px 16px \".concat(colors.primary, \"40\");\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(0)\";\n                                            target.style.boxShadow = \"0 4px 12px \".concat(colors.primary, \"30\");\n                                        },\n                                        className: \"jsx-3f45dd3580662bfa\" + \" \" + \"sf-pro\",\n                                        children: \"Publish\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"fixed\",\n                            bottom: \"24px\",\n                            left: \"50%\",\n                            transform: \"translateX(-50%)\",\n                            width: \"600px\",\n                            height: \"48px\",\n                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                            borderRadius: \"24px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            padding: \"0 20px\",\n                            boxShadow: \"0 8px 32px \".concat(colors.primary, \"30, 0 2px 8px rgba(0, 0, 0, 0.1)\"),\n                            cursor: \"pointer\",\n                            transition: \"all 0.3s ease\",\n                            zIndex: 1000,\n                            border: \"1px solid \".concat(colors.primary, \"20\")\n                        },\n                        onClick: ()=>setShowAgentE(true),\n                        onMouseEnter: (e)=>{\n                            e.currentTarget.style.transform = \"translateX(-50%) translateY(-2px)\";\n                            e.currentTarget.style.boxShadow = \"0 12px 40px \".concat(colors.primary, \"40, 0 4px 12px rgba(0, 0, 0, 0.15)\");\n                        },\n                        onMouseLeave: (e)=>{\n                            e.currentTarget.style.transform = \"translateX(-50%) translateY(0)\";\n                            e.currentTarget.style.boxShadow = \"0 8px 32px \".concat(colors.primary, \"30, 0 2px 8px rgba(0, 0, 0, 0.1)\");\n                        },\n                        className: \"jsx-3f45dd3580662bfa\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bot, {\n                                size: 18,\n                                color: \"white\",\n                                style: {\n                                    marginRight: \"12px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    color: \"white\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    flex: 1\n                                },\n                                className: \"jsx-3f45dd3580662bfa\",\n                                children: \"Ask Agent E for help with your content...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: \"6px\",\n                                    height: \"6px\",\n                                    background: \"rgba(255, 255, 255, 0.8)\",\n                                    borderRadius: \"50%\",\n                                    animation: \"pulse 2s infinite\"\n                                },\n                                className: \"jsx-3f45dd3580662bfa\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentEChatSimple__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showAgentE,\n                onClose: ()=>setShowAgentE(false),\n                currentContent: content\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 502,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"3f45dd3580662bfa\",\n                children: \"@-webkit-keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);transform:scale(1.2)}}@-moz-keyframes pulse{0%,100%{opacity:.8;-moz-transform:scale(1);transform:scale(1)}50%{opacity:1;-moz-transform:scale(1.2);transform:scale(1.2)}}@-o-keyframes pulse{0%,100%{opacity:.8;-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-o-transform:scale(1.2);transform:scale(1.2)}}@keyframes pulse{0%,100%{opacity:.8;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-o-transform:scale(1.2);transform:scale(1.2)}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TweetCenterPage, \"wJrcYQKhK6mHhasX9nKwi3Fz+xY=\");\n_c = TweetCenterPage;\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayoutSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 520,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (TweetCenterPage);\nvar _c;\n$RefreshReg$(_c, \"TweetCenterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n"));

/***/ })

});