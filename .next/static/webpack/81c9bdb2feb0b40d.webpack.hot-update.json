{"c": ["pages/dashboard", "pages/tweet-center", "pages/index", "pages/settings", "webpack"], "r": ["pages/dashboard", "pages/settings"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fdashboard.tsx&page=%2Fdashboard!", "./pages/dashboard.tsx", "./node_modules/lucide-react/dist/esm/icons/bell.js", "./node_modules/lucide-react/dist/esm/icons/crown.js", "./node_modules/lucide-react/dist/esm/icons/key.js", "./node_modules/lucide-react/dist/esm/icons/plus.js", "./node_modules/lucide-react/dist/esm/icons/save.js", "./node_modules/lucide-react/dist/esm/icons/shield.js", "./node_modules/lucide-react/dist/esm/icons/trash-2.js", "./node_modules/lucide-react/dist/esm/icons/twitter.js", "./node_modules/lucide-react/dist/esm/icons/zap.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fsettings.tsx&page=%2Fsettings!", "./pages/settings.tsx", "__barrel_optimize__?names=<PERSON>,<PERSON><PERSON>,Crown,Key,Plus,Save,Shield,Trash2,Twitter,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js"]}