import Link from 'next/link';
import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { Home, BarChart3, MessageCircle, Video, User, LogIn } from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import AuthModal from './AuthModal';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();
  const { user, profile } = useUser();
  const [showAuthModal, setShowAuthModal] = useState(false);

  // Fallback user data for when not authenticated or loading
  const userData = {
    name: profile?.full_name || user?.user_metadata?.full_name || 'Alex Chen',
    email: user?.email || '<EMAIL>',
    plan: profile?.plan === 'pro' ? 'Pro' : profile?.plan === 'enterprise' ? 'Enterprise' : 'Free',
    avatar: profile?.avatar_url || null,
    isOnline: profile?.is_online || false
  };

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    background: '#F5F1EB', // Chill beige background
    surface: '#FFFFFF',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    sidebar: {
      background: '#FFFFFF', // Clean white
      surface: 'rgba(255, 107, 53, 0.05)', // Subtle orange wash
      text: '#2D1B14', // Rich brown text
      textSecondary: '#5D4037', // Medium brown
      textMuted: '#8D6E63', // Light brown
      accent: '#FF6B35', // Vibrant orange
      accentSoft: '#FF8A65', // Softer orange
      accentLight: '#FFF7F4', // Very light orange background
      border: 'rgba(255, 107, 53, 0.15)', // Subtle orange border
      hover: 'rgba(255, 107, 53, 0.08)', // Light orange hover
      divider: 'rgba(255, 107, 53, 0.2)', // Orange divider
      glow: 'rgba(255, 107, 53, 0.25)' // Orange glow
    }
  };

  const menuItems = [
    { href: '/', label: 'Briefing Room', icon: Home },
    { href: '/tweet-center', label: 'Drafting Desk', icon: MessageCircle },
    { href: '/dashboard', label: 'Growth Lab', icon: BarChart3 },
    { href: '/meeting', label: 'AI Meetings', icon: Video }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <div style={{
      display: 'flex',
      minHeight: '100vh',
      background: colors.background,
      padding: '16px',
      gap: '16px'
    }}>
      <aside style={{
        width: '180px', // Compact but readable
        background: colors.sidebar.background,
        height: 'calc(100vh - 32px)',
        borderRadius: '24px',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: `0 8px 32px ${colors.sidebar.glow}, 0 2px 8px rgba(255, 107, 53, 0.1)`,
        border: `1px solid ${colors.sidebar.border}`,
        overflow: 'hidden',
        position: 'relative'
      }}>
        {/* Subtle top accent */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '2px',
          background: `linear-gradient(90deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft}, ${colors.sidebar.accent})`
        }} />

        {/* Logo Section */}
        <div style={{
          padding: '20px 16px 16px 16px',
          textAlign: 'center',
          background: colors.sidebar.accentLight
        }}>
          <div style={{
            fontSize: '32px',
            color: colors.sidebar.accent,
            fontFamily: 'Dancing Script, cursive',
            fontWeight: '600',
            letterSpacing: '1px',
            textShadow: `0 2px 4px ${colors.sidebar.glow}`,
            marginBottom: '4px'
          }}>
            Exie
          </div>
          <div style={{
            fontSize: '10px',
            color: colors.sidebar.textMuted,
            fontWeight: '500',
            letterSpacing: '1px',
            textTransform: 'uppercase'
          }}>
            AI Assistant
          </div>
        </div>

        {/* Navigation */}
        <nav style={{
          padding: '16px 12px',
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          gap: '4px'
        }}>
          {menuItems.map((item, index) => {
            const active = isActive(item.href);
            return (
              <div key={item.href}>
                <Link href={item.href} style={{ textDecoration: 'none' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '12px 16px',
                    borderRadius: '16px',
                    background: active ? colors.sidebar.surface : 'transparent',
                    cursor: 'pointer',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    position: 'relative',
                    border: active ? `1px solid ${colors.sidebar.border}` : '1px solid transparent'
                  }}
                  onMouseEnter={(e) => {
                    if (!active) {
                      e.currentTarget.style.background = colors.sidebar.hover;
                      e.currentTarget.style.transform = 'translateX(2px)';
                      e.currentTarget.style.borderColor = colors.sidebar.border;
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!active) {
                      e.currentTarget.style.background = 'transparent';
                      e.currentTarget.style.transform = 'translateX(0)';
                      e.currentTarget.style.borderColor = 'transparent';
                    }
                  }}
                  >
                    {/* Active indicator */}
                    {active && (
                      <div style={{
                        position: 'absolute',
                        left: '0',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        width: '3px',
                        height: '20px',
                        background: `linear-gradient(180deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft})`,
                        borderRadius: '0 3px 3px 0',
                        boxShadow: `0 0 8px ${colors.sidebar.glow}`
                      }} />
                    )}

                    {/* Icon */}
                    <div style={{
                      width: '20px',
                      height: '20px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: '12px'
                    }}>
                      <item.icon
                        size={16}
                        color={active ? colors.sidebar.accent : colors.sidebar.textSecondary}
                        strokeWidth={active ? 2.5 : 2}
                      />
                    </div>

                    {/* Label */}
                    <span style={{
                      color: active ? colors.sidebar.text : colors.sidebar.textSecondary,
                      fontSize: '14px',
                      fontWeight: active ? '600' : '500',
                      letterSpacing: '0.2px'
                    }}>
                      {item.label}
                    </span>
                  </div>
                </Link>

                {/* Elegant separator dashes */}
                {index < menuItems.length - 1 && (
                  <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    margin: '8px 0',
                    height: '1px'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '3px'
                    }}>
                      {[...Array(5)].map((_, i) => (
                        <div
                          key={i}
                          style={{
                            width: '4px',
                            height: '1px',
                            background: colors.sidebar.divider,
                            borderRadius: '1px'
                          }}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </nav>

        {/* Bottom divider */}
        <div style={{
          margin: '0 16px 16px 16px',
          height: '1px',
          background: `linear-gradient(90deg, transparent, ${colors.sidebar.divider}, transparent)`
        }} />

        {/* Account Section */}
        <div style={{
          padding: '12px 12px 16px 12px',
          background: colors.sidebar.accentLight
        }}>
          {user ? (
            // Authenticated user - show profile
            <Link href="/settings" style={{ textDecoration: 'none' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                padding: '10px 12px',
                background: colors.sidebar.background,
                borderRadius: '14px',
                cursor: 'pointer',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                border: `1px solid ${colors.sidebar.border}`,
                boxShadow: `0 2px 8px ${colors.sidebar.glow}`
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = colors.sidebar.surface;
                e.currentTarget.style.borderColor = colors.sidebar.accent + '40';
                e.currentTarget.style.transform = 'translateY(-1px)';
                e.currentTarget.style.boxShadow = `0 4px 16px ${colors.sidebar.glow}`;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = colors.sidebar.background;
                e.currentTarget.style.borderColor = colors.sidebar.border;
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = `0 2px 8px ${colors.sidebar.glow}`;
              }}
              >
                <div style={{
                  width: '32px',
                  height: '32px',
                  background: userData.avatar ? 'transparent' : `linear-gradient(135deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft})`,
                  borderRadius: '10px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                  overflow: 'hidden',
                  border: `2px solid ${colors.sidebar.border}`
                }}>
                  {userData.avatar ? (
                    <img
                      src={userData.avatar}
                      alt={userData.name}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        borderRadius: '8px'
                      }}
                      onError={(e) => {
                        // Fallback to icon if image fails to load
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.parentElement!.style.background = `linear-gradient(135deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft})`;
                        e.currentTarget.parentElement!.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>`;
                      }}
                    />
                  ) : (
                    <User size={16} color="#FFFFFF" strokeWidth={2} />
                  )}
                  {/* Online status dot - only show if user is online */}
                  {userData.isOnline && (
                    <div style={{
                      position: 'absolute',
                      bottom: '-1px',
                      right: '-1px',
                      width: '10px',
                      height: '10px',
                      background: '#00FF88',
                      borderRadius: '50%',
                      border: '2px solid #FFFFFF',
                      boxShadow: '0 0 6px rgba(0, 255, 136, 0.8)'
                    }} />
                  )}
                </div>
                <div style={{ flex: 1 }}>
                  <div style={{
                    color: colors.sidebar.text,
                    fontSize: '13px',
                    fontWeight: '600',
                    lineHeight: '1.2',
                    marginBottom: '2px'
                  }}>
                    {userData.name}
                  </div>
                  <div style={{
                    color: colors.sidebar.textMuted,
                    fontSize: '11px',
                    fontWeight: '500',
                    lineHeight: '1.2',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}>
                    <span>{userData.plan} Plan</span>
                    <span style={{ color: colors.sidebar.accent }}>•</span>
                    <span>Settings</span>
                  </div>
                </div>
              </div>
            </Link>
          ) : (
            // Not authenticated - show sign in button
            <div
              onClick={() => setShowAuthModal(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                padding: '10px 12px',
                background: colors.sidebar.background,
                borderRadius: '14px',
                cursor: 'pointer',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                border: `1px solid ${colors.sidebar.border}`,
                boxShadow: `0 2px 8px ${colors.sidebar.glow}`
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = colors.sidebar.surface;
                e.currentTarget.style.borderColor = colors.sidebar.accent + '40';
                e.currentTarget.style.transform = 'translateY(-1px)';
                e.currentTarget.style.boxShadow = `0 4px 16px ${colors.sidebar.glow}`;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = colors.sidebar.background;
                e.currentTarget.style.borderColor = colors.sidebar.border;
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = `0 2px 8px ${colors.sidebar.glow}`;
              }}
            >
              <div style={{
                width: '32px',
                height: '32px',
                background: `linear-gradient(135deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft})`,
                borderRadius: '10px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: `2px solid ${colors.sidebar.border}`
              }}>
                <LogIn size={16} color="#FFFFFF" strokeWidth={2} />
              </div>
              <div style={{ flex: 1 }}>
                <div style={{
                  color: colors.sidebar.text,
                  fontSize: '13px',
                  fontWeight: '600',
                  lineHeight: '1.2',
                  marginBottom: '2px'
                }}>
                  Sign In
                </div>
                <div style={{
                  color: colors.sidebar.textMuted,
                  fontSize: '11px',
                  fontWeight: '500',
                  lineHeight: '1.2'
                }}>
                  Access your account
                </div>
              </div>
            </div>
          )}
        </div>
      </aside>

      <main style={{
        flexGrow: 1,
        backgroundColor: colors.surface,
        borderRadius: '24px',
        height: 'calc(100vh - 32px)',
        position: 'relative',
        boxShadow: '0 4px 20px rgba(255, 107, 53, 0.08)',
        border: `1px solid ${colors.sidebar.border}`,
        overflow: 'hidden'
      }}>
        <div style={{
          padding: '32px',
          height: '100%',
          overflow: 'auto'
        }}>
          {children}
        </div>
      </main>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </div>
  );
};

export default SidebarLayout;
