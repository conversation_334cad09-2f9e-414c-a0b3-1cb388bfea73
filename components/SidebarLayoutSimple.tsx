import Link from 'next/link';
import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { Home, BarChart3, MessageCircle, Video, User, LogIn, Search } from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import AuthModal from './AuthModal';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();
  const { user, profile } = useUser();
  const [showAuthModal, setShowAuthModal] = useState(false);

  // Fallback user data for when not authenticated or loading
  const userData = {
    name: profile?.full_name || user?.user_metadata?.full_name || 'Alex Chen',
    email: user?.email || '<EMAIL>',
    plan: profile?.plan === 'pro' ? 'Pro' : profile?.plan === 'enterprise' ? 'Enterprise' : 'Free',
    avatar: profile?.avatar_url || null,
    isOnline: profile?.is_online || false
  };

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    background: '#F5F1EB', // Chill beige background
    surface: '#FFFFFF',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    sidebar: {
      background: '#FFFFFF', // Clean white
      surface: 'rgba(255, 107, 53, 0.05)', // Subtle orange wash
      text: '#2D1B14', // Rich brown text
      textSecondary: '#5D4037', // Medium brown
      textMuted: '#8D6E63', // Light brown
      accent: '#FF6B35', // Vibrant orange
      accentSoft: '#FF8A65', // Softer orange
      accentLight: '#FFF7F4', // Very light orange background
      border: 'rgba(255, 107, 53, 0.15)', // Subtle orange border
      hover: 'rgba(255, 107, 53, 0.08)', // Light orange hover
      divider: 'rgba(255, 107, 53, 0.2)', // Orange divider
      glow: 'rgba(255, 107, 53, 0.25)' // Orange glow
    }
  };

  const menuItems = [
    {
      section: 'WORKSPACE',
      items: [
        { href: '/', label: 'Briefing Room', icon: Home },
        { href: '/tweet-center', label: 'Drafting Desk', icon: MessageCircle },
        { href: '/dashboard', label: 'Growth Lab', icon: BarChart3 },
        { href: '/meeting', label: 'AI Meetings', icon: Video }
      ]
    },
    {
      section: 'SETTINGS',
      items: [
        { href: '/settings', label: 'Settings', icon: User }
      ]
    }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <div style={{
      display: 'flex',
      minHeight: '100vh',
      background: colors.background,
      padding: '16px',
      gap: '16px'
    }}>
      <aside style={{
        width: '200px', // Clean and spacious like reference
        background: colors.sidebar.background,
        height: 'calc(100vh - 32px)',
        borderRadius: '24px',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: `0 8px 32px ${colors.sidebar.glow}, 0 2px 8px rgba(255, 107, 53, 0.1)`,
        border: `1px solid ${colors.sidebar.border}`,
        overflow: 'hidden',
        position: 'relative'
      }}>
        {/* Logo Section */}
        <div style={{
          padding: '24px 16px 20px 20px',
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
          background: colors.sidebar.background
        }}>
          <img
            src="https://nlckamsrdiwkyyrxzntf.supabase.co/storage/v1/object/sign/logos/logoxe.png?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InN0b3JhZ2UtdXJsLXNpZ25pbmcta2V5X2RiNTE0YzE5LTlhNTQtNGZiNy1hMjY3LTJmNjY5ZDlhZjY1OCJ9.eyJ1cmwiOiJsb2dvcy9sb2dveGUucG5nIiwiaWF0IjoxNzQ4MjMxNDM1LCJleHAiOjE3NTA4MjM0MzV9.dw1yy3hjXvMy02IhFMKGw_-evgbmyYDuJ4m6HPP1Uec"
            alt="Exie Logo"
            style={{
              height: '28px',
              width: 'auto',
              objectFit: 'contain'
            }}
          />
          <span style={{
            fontFamily: 'Anek Tamil, sans-serif',
            fontSize: '22px',
            fontWeight: '600',
            color: colors.sidebar.text,
            letterSpacing: '-0.5px',
            lineHeight: '1'
          }}>
            Exie
          </span>
        </div>

        {/* Search Bar */}
        <div style={{
          padding: '0 16px 16px 16px'
        }}>
          <div style={{
            position: 'relative',
            display: 'flex',
            alignItems: 'center'
          }}>
            <Search
              size={14}
              color={colors.sidebar.textMuted}
              style={{
                position: 'absolute',
                left: '12px',
                zIndex: 1
              }}
            />
            <input
              type="text"
              placeholder="Search..."
              style={{
                width: '100%',
                padding: '8px 12px 8px 36px',
                border: `1px solid ${colors.sidebar.border}`,
                borderRadius: '8px',
                background: colors.sidebar.surface,
                color: colors.sidebar.text,
                fontSize: '12px',
                fontWeight: '400',
                outline: 'none',
                transition: 'all 0.2s ease'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = colors.sidebar.accent;
                e.target.style.background = colors.sidebar.background;
              }}
              onBlur={(e) => {
                e.target.style.borderColor = colors.sidebar.border;
                e.target.style.background = colors.sidebar.surface;
              }}
            />
          </div>
        </div>

        {/* Navigation */}
        <nav style={{
          padding: '8px 16px',
          flex: 1,
          display: 'flex',
          flexDirection: 'column'
        }}>
          {menuItems.map((section, sectionIndex) => (
            <div key={section.section} style={{ marginBottom: sectionIndex < menuItems.length - 1 ? '24px' : '0' }}>
              {/* Subtle divider before settings section */}
              {sectionIndex > 0 && (
                <div style={{
                  height: '1px',
                  background: `linear-gradient(90deg, transparent, ${colors.sidebar.divider}, transparent)`,
                  margin: '16px 12px 20px 12px'
                }} />
              )}

              {/* Section Header */}
              <div style={{
                fontSize: '10px',
                fontWeight: '600',
                color: colors.sidebar.textMuted,
                textTransform: 'uppercase',
                letterSpacing: '1px',
                marginBottom: '10px',
                paddingLeft: '12px'
              }}>
                {section.section}
              </div>

              {/* Section Items */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                {section.items.map((item) => {
                  const active = isActive(item.href);
                  return (
                    <Link key={item.href} href={item.href} style={{ textDecoration: 'none' }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '8px 12px',
                        borderRadius: '8px',
                        background: active ? colors.sidebar.surface : 'transparent',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        position: 'relative'
                      }}
                      onMouseEnter={(e) => {
                        if (!active) {
                          e.currentTarget.style.background = colors.sidebar.hover;
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!active) {
                          e.currentTarget.style.background = 'transparent';
                        }
                      }}
                      >
                        {/* Active indicator */}
                        {active && (
                          <div style={{
                            position: 'absolute',
                            left: '0',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            width: '2px',
                            height: '16px',
                            background: colors.sidebar.accent,
                            borderRadius: '0 2px 2px 0'
                          }} />
                        )}

                        {/* Icon */}
                        <div style={{
                          width: '16px',
                          height: '16px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginRight: '10px'
                        }}>
                          <item.icon
                            size={14}
                            color={active ? colors.sidebar.accent : colors.sidebar.textSecondary}
                            strokeWidth={2}
                          />
                        </div>

                        {/* Label */}
                        <span style={{
                          color: active ? colors.sidebar.text : colors.sidebar.textSecondary,
                          fontSize: '13px',
                          fontWeight: active ? '500' : '400',
                          letterSpacing: '0.1px'
                        }}>
                          {item.label}
                        </span>
                      </div>
                    </Link>
                  );
                })}
              </div>
            </div>
          ))}
        </nav>

        {/* Bottom divider */}
        <div style={{
          margin: '0 16px 16px 16px',
          height: '1px',
          background: `linear-gradient(90deg, transparent, ${colors.sidebar.divider}, transparent)`
        }} />

        {/* Account Section */}
        <div style={{
          padding: '8px 16px 16px 16px'
        }}>
          {user ? (
            // Authenticated user - show profile
            <Link href="/settings" style={{ textDecoration: 'none' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                padding: '10px 12px',
                background: colors.sidebar.background,
                borderRadius: '14px',
                cursor: 'pointer',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                border: `1px solid ${colors.sidebar.border}`,
                boxShadow: `0 2px 8px ${colors.sidebar.glow}`
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = colors.sidebar.surface;
                e.currentTarget.style.borderColor = colors.sidebar.accent + '40';
                e.currentTarget.style.transform = 'translateY(-1px)';
                e.currentTarget.style.boxShadow = `0 4px 16px ${colors.sidebar.glow}`;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = colors.sidebar.background;
                e.currentTarget.style.borderColor = colors.sidebar.border;
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = `0 2px 8px ${colors.sidebar.glow}`;
              }}
              >
                <div style={{
                  width: '32px',
                  height: '32px',
                  background: userData.avatar ? 'transparent' : `linear-gradient(135deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft})`,
                  borderRadius: '10px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                  overflow: 'hidden',
                  border: `2px solid ${colors.sidebar.border}`
                }}>
                  {userData.avatar ? (
                    <img
                      src={userData.avatar}
                      alt={userData.name}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        borderRadius: '8px'
                      }}
                      onError={(e) => {
                        // Fallback to icon if image fails to load
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.parentElement!.style.background = `linear-gradient(135deg, ${colors.sidebar.accent}, ${colors.sidebar.accentSoft})`;
                        e.currentTarget.parentElement!.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>`;
                      }}
                    />
                  ) : (
                    <User size={16} color="#FFFFFF" strokeWidth={2} />
                  )}
                  {/* Online status dot - only show if user is online */}
                  {userData.isOnline && (
                    <div style={{
                      position: 'absolute',
                      bottom: '-1px',
                      right: '-1px',
                      width: '10px',
                      height: '10px',
                      background: '#00FF88',
                      borderRadius: '50%',
                      border: '2px solid #FFFFFF',
                      boxShadow: '0 0 6px rgba(0, 255, 136, 0.8)'
                    }} />
                  )}
                </div>
                <div style={{ flex: 1 }}>
                  <div style={{
                    color: colors.sidebar.text,
                    fontSize: '13px',
                    fontWeight: '600',
                    lineHeight: '1.2',
                    marginBottom: '2px'
                  }}>
                    {userData.name}
                  </div>
                  <div style={{
                    color: colors.sidebar.textMuted,
                    fontSize: '11px',
                    fontWeight: '500',
                    lineHeight: '1.2',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}>
                    <span>{userData.plan} Plan</span>
                    <span style={{ color: colors.sidebar.accent }}>•</span>
                    <span>Settings</span>
                  </div>
                </div>
              </div>
            </Link>
          ) : (
            // Not authenticated - show sign in button
            <div
              onClick={() => setShowAuthModal(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '8px 12px',
                borderRadius: '8px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                background: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = colors.sidebar.hover;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'transparent';
              }}
            >
              {/* Icon */}
              <div style={{
                width: '16px',
                height: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '10px'
              }}>
                <LogIn
                  size={14}
                  color={colors.sidebar.textSecondary}
                  strokeWidth={2}
                />
              </div>

              {/* Label */}
              <span style={{
                color: colors.sidebar.textSecondary,
                fontSize: '13px',
                fontWeight: '400',
                letterSpacing: '0.1px'
              }}>
                Sign In
              </span>
            </div>
          )}
        </div>
      </aside>

      <main style={{
        flexGrow: 1,
        backgroundColor: colors.surface,
        borderRadius: '24px',
        height: 'calc(100vh - 32px)',
        position: 'relative',
        boxShadow: '0 4px 20px rgba(255, 107, 53, 0.08)',
        border: `1px solid ${colors.sidebar.border}`,
        overflow: 'hidden'
      }}>
        <div style={{
          padding: '32px',
          height: '100%',
          overflow: 'auto'
        }}>
          {children}
        </div>
      </main>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </div>
  );
};

export default SidebarLayout;
