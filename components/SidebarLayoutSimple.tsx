import Link from 'next/link';
import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { Home, BarChart3, MessageCircle, Video, User, LogIn, Search, Calendar, Bot, Zap, Bell, Shield } from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import AuthModal from './AuthModal';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();
  const { user, profile, loading } = useUser();
  const [showAuthModal, setShowAuthModal] = useState(false);

  // Debug logging
  console.log('Sidebar render:', {
    user: !!user,
    userEmail: user?.email,
    profile: !!profile,
    profileName: profile?.full_name,
    loading
  });

  // User data - only use real data when authenticated
  const userData = user ? {
    name: profile?.full_name || user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'User',
    email: user?.email || '',
    plan: profile?.plan === 'pro' ? 'Pro' : profile?.plan === 'enterprise' ? 'Enterprise' : 'Free',
    avatar: profile?.avatar_url || null,
    isOnline: profile?.is_online || false
  } : null;

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    background: '#F5F1EB', // Chill beige background
    surface: '#FFFFFF',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    sidebar: {
      background: '#FFFFFF', // Clean white
      surface: 'rgba(255, 107, 53, 0.05)', // Subtle orange wash
      text: '#2D1B14', // Rich brown text
      textSecondary: '#5D4037', // Medium brown
      textMuted: '#8D6E63', // Light brown
      accent: '#FF6B35', // Vibrant orange
      accentSoft: '#FF8A65', // Softer orange
      accentLight: '#FFF7F4', // Very light orange background
      border: 'rgba(255, 107, 53, 0.15)', // Subtle orange border
      hover: 'rgba(255, 107, 53, 0.08)', // Light orange hover
      divider: 'rgba(255, 107, 53, 0.2)', // Orange divider
      glow: 'rgba(255, 107, 53, 0.25)' // Orange glow
    }
  };

  const menuItems = [
    {
      section: 'WORKSPACE',
      items: [
        { href: '/', label: 'Briefing Room', icon: Home },
        { href: '/tweet-center', label: 'Drafting Desk', icon: MessageCircle },
        { href: '/schedule', label: 'Content Scheduler', icon: Calendar },
        { href: '/dashboard', label: 'Growth Lab', icon: BarChart3 },
        { href: '/meeting', label: 'AI Meetings', icon: Video }
      ]
    },
    {
      section: 'SETTINGS',
      items: [
        { href: '/settings?tab=agent-e', label: 'Agent E', icon: Bot },
        { href: '/settings?tab=account', label: 'Account', icon: User },
        { href: '/settings?tab=integrations', label: 'Integrations', icon: Zap },
        { href: '/settings?tab=notifications', label: 'Notifications', icon: Bell },
        { href: '/settings?tab=security', label: 'Security', icon: Shield }
      ]
    }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <div style={{
      display: 'flex',
      minHeight: '100vh',
      background: colors.background,
      padding: '16px',
      gap: '16px'
    }}>
      <aside style={{
        width: '200px', // Clean and spacious like reference
        background: colors.sidebar.background,
        height: 'calc(100vh - 32px)',
        borderRadius: '24px',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: `0 8px 32px ${colors.sidebar.glow}, 0 2px 8px rgba(255, 107, 53, 0.1)`,
        border: `1px solid ${colors.sidebar.border}`,
        overflow: 'hidden',
        position: 'relative'
      }}>
        {/* Logo Section */}
        <div style={{
          padding: '32px 20px 24px 20px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          background: colors.sidebar.background
        }}>
          <img
            src="https://nlckamsrdiwkyyrxzntf.supabase.co/storage/v1/object/sign/logos/elogos.png?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InN0b3JhZ2UtdXJsLXNpZ25pbmcta2V5X2RiNTE0YzE5LTlhNTQtNGZiNy1hMjY3LTJmNjY5ZDlhZjY1OCJ9.eyJ1cmwiOiJsb2dvcy9lbG9nb3MucG5nIiwiaWF0IjoxNzQ4Mjk2NDIyLCJleHAiOjE3Nzk4MzI0MjJ9.FJlERvbHYRsm-4XpUyFKY1_xnFV988GB6X9M3vMarjE"
            alt="Exie Logo"
            style={{
              height: '42px',
              width: 'auto',
              objectFit: 'contain'
            }}
          />
        </div>

        {/* Search Bar */}
        <div style={{
          padding: '0 16px 16px 16px'
        }}>
          <div style={{
            position: 'relative',
            display: 'flex',
            alignItems: 'center'
          }}>
            <Search
              size={14}
              color={colors.sidebar.textMuted}
              style={{
                position: 'absolute',
                left: '12px',
                zIndex: 1
              }}
            />
            <input
              type="text"
              placeholder="Search..."
              style={{
                width: '100%',
                padding: '8px 12px 8px 36px',
                border: `1px solid ${colors.sidebar.border}`,
                borderRadius: '8px',
                background: colors.sidebar.surface,
                color: colors.sidebar.text,
                fontSize: '12px',
                fontWeight: '400',
                outline: 'none',
                transition: 'all 0.2s ease'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = colors.sidebar.accent;
                e.target.style.background = colors.sidebar.background;
              }}
              onBlur={(e) => {
                e.target.style.borderColor = colors.sidebar.border;
                e.target.style.background = colors.sidebar.surface;
              }}
            />
          </div>
        </div>

        {/* Navigation */}
        <nav style={{
          padding: '8px 16px',
          flex: 1,
          display: 'flex',
          flexDirection: 'column'
        }}>
          {menuItems.map((section, sectionIndex) => (
            <div key={section.section} style={{ marginBottom: sectionIndex < menuItems.length - 1 ? '24px' : '0' }}>
              {/* Subtle divider before settings section */}
              {sectionIndex > 0 && (
                <div style={{
                  height: '1px',
                  background: `linear-gradient(90deg, transparent, ${colors.sidebar.divider}, transparent)`,
                  margin: '16px 12px 20px 12px'
                }} />
              )}

              {/* Section Header */}
              <div style={{
                fontSize: '10px',
                fontWeight: '600',
                color: colors.sidebar.textMuted,
                textTransform: 'uppercase',
                letterSpacing: '1px',
                marginBottom: '10px',
                paddingLeft: '12px'
              }}>
                {section.section}
              </div>

              {/* Section Items */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                {section.items.map((item) => {
                  const active = isActive(item.href);
                  return (
                    <Link key={item.href} href={item.href} style={{ textDecoration: 'none' }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '8px 12px',
                        borderRadius: '8px',
                        background: active ? colors.sidebar.surface : 'transparent',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        position: 'relative'
                      }}
                      onMouseEnter={(e) => {
                        if (!active) {
                          e.currentTarget.style.background = colors.sidebar.hover;
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!active) {
                          e.currentTarget.style.background = 'transparent';
                        }
                      }}
                      >
                        {/* Active indicator */}
                        {active && (
                          <div style={{
                            position: 'absolute',
                            left: '0',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            width: '2px',
                            height: '16px',
                            background: colors.sidebar.accent,
                            borderRadius: '0 2px 2px 0'
                          }} />
                        )}

                        {/* Icon */}
                        <div style={{
                          width: '16px',
                          height: '16px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginRight: '10px'
                        }}>
                          <item.icon
                            size={14}
                            color={active ? colors.sidebar.accent : colors.sidebar.textSecondary}
                            strokeWidth={2}
                          />
                        </div>

                        {/* Label */}
                        <span style={{
                          color: active ? colors.sidebar.text : colors.sidebar.textSecondary,
                          fontSize: '13px',
                          fontWeight: active ? '500' : '400',
                          letterSpacing: '0.1px'
                        }}>
                          {item.label}
                        </span>
                      </div>
                    </Link>
                  );
                })}
              </div>
            </div>
          ))}
        </nav>

        {/* Bottom divider */}
        <div style={{
          margin: '0 16px 16px 16px',
          height: '1px',
          background: `linear-gradient(90deg, transparent, ${colors.sidebar.divider}, transparent)`
        }} />

        {/* Account Section */}
        <div style={{
          padding: '12px 16px 20px 16px'
        }}>
          {user && userData ? (
            // Authenticated user - show profile
            <Link href="/settings" style={{ textDecoration: 'none' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                padding: '12px',
                background: 'transparent',
                borderRadius: '12px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                border: 'none'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = colors.sidebar.hover;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'transparent';
              }}
              >
                <div style={{
                  width: '32px',
                  height: '32px',
                  background: userData.avatar ? 'transparent' : '#F3F4F6',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                  overflow: 'hidden'
                }}>
                  {userData.avatar ? (
                    <img
                      src={userData.avatar}
                      alt={userData.name}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        borderRadius: '50%'
                      }}
                      onError={(e) => {
                        // Fallback to icon if image fails to load
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.parentElement!.style.background = '#F3F4F6';
                        e.currentTarget.parentElement!.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#9CA3AF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>`;
                      }}
                    />
                  ) : (
                    <User size={16} color="#9CA3AF" strokeWidth={1.5} />
                  )}
                  {/* Clean green dot */}
                  <div style={{
                    position: 'absolute',
                    bottom: '-2px',
                    right: '-2px',
                    width: '10px',
                    height: '10px',
                    background: '#10B981',
                    borderRadius: '50%',
                    border: '2px solid white'
                  }} />
                </div>
                <div style={{ flex: 1, marginLeft: '4px' }}>
                  <div style={{
                    color: colors.sidebar.text,
                    fontSize: '14px',
                    fontWeight: '500',
                    lineHeight: '1.3',
                    marginBottom: '1px'
                  }}>
                    {userData.name}
                  </div>
                  <div style={{
                    color: colors.sidebar.textMuted,
                    fontSize: '12px',
                    fontWeight: '400',
                    lineHeight: '1.2'
                  }}>
                    {userData.plan} Plan
                  </div>
                </div>
              </div>
            </Link>
          ) : (
            // Not authenticated - show sign in button
            <div
              onClick={() => setShowAuthModal(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '8px 12px',
                borderRadius: '8px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                background: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = colors.sidebar.hover;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'transparent';
              }}
            >
              {/* Icon */}
              <div style={{
                width: '16px',
                height: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '10px'
              }}>
                <LogIn
                  size={14}
                  color={colors.sidebar.textSecondary}
                  strokeWidth={2}
                />
              </div>

              {/* Label */}
              <span style={{
                color: colors.sidebar.textSecondary,
                fontSize: '13px',
                fontWeight: '400',
                letterSpacing: '0.1px'
              }}>
                Sign In
              </span>
            </div>
          )}
        </div>
      </aside>

      <main style={{
        flexGrow: 1,
        backgroundColor: colors.surface,
        borderRadius: '24px',
        height: 'calc(100vh - 32px)',
        position: 'relative',
        boxShadow: '0 4px 20px rgba(255, 107, 53, 0.08)',
        border: `1px solid ${colors.sidebar.border}`,
        overflow: 'hidden'
      }}>
        <div style={{
          padding: '32px',
          height: '100%',
          overflow: 'auto'
        }}>
          {children}
        </div>
      </main>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </div>
  );
};

export default SidebarLayout;
