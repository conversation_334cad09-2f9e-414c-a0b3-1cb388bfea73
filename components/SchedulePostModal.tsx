import React, { useState, useEffect } from 'react';
import { X, Calendar, Clock, Send, Twitter, ExternalLink } from 'lucide-react';
import { useUser } from '../contexts/UserContext';

interface SchedulePostModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPostScheduled: () => void;
}

const SchedulePostModal: React.FC<SchedulePostModalProps> = ({ isOpen, onClose, onPostScheduled }) => {
  const { user } = useUser();
  const [content, setContent] = useState('');
  const [scheduledDate, setScheduledDate] = useState('');
  const [scheduledTime, setScheduledTime] = useState('');
  const [isPosting, setIsPosting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [xAccountConnected, setXAccountConnected] = useState(false);
  const [checkingConnection, setCheckingConnection] = useState(true);

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    surface: '#FFFFFF',
    background: '#FFF8F3',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    border: '#F5E6D3'
  };

  // Check X account connection when modal opens
  useEffect(() => {
    if (isOpen && user) {
      checkXAccountConnection();
    }
  }, [isOpen, user]);

  const checkXAccountConnection = async () => {
    if (!user) return;

    setCheckingConnection(true);
    try {
      const response = await fetch(`/api/x/account-status?userId=${user.id}`);
      const data = await response.json();
      setXAccountConnected(data.connected);
    } catch (error) {
      console.error('Error checking X account connection:', error);
      setXAccountConnected(false);
    } finally {
      setCheckingConnection(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !content.trim() || !scheduledDate || !scheduledTime) return;

    setIsPosting(true);
    setError(null);

    try {
      const scheduledDateTime = new Date(`${scheduledDate}T${scheduledTime}`);

      const response = await fetch('/api/twitter/post', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: content.trim(),
          userId: user.id,
          scheduledTime: scheduledDateTime.toISOString()
        }),
      });

      const data = await response.json();

      if (response.ok) {
        onPostScheduled();
        onClose();
        setContent('');
        setScheduledDate('');
        setScheduledTime('');
      } else {
        setError(data.error || 'Failed to schedule post');
      }
    } catch (err) {
      setError('Failed to schedule post');
      console.error('Error scheduling post:', err);
    } finally {
      setIsPosting(false);
    }
  };

  const handlePostNow = async () => {
    if (!user || !content.trim()) return;

    setIsPosting(true);
    setError(null);

    try {
      const response = await fetch('/api/twitter/post', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: content.trim(),
          userId: user.id
        }),
      });

      const data = await response.json();

      if (response.ok) {
        onPostScheduled();
        onClose();
        setContent('');
        setScheduledDate('');
        setScheduledTime('');
      } else {
        setError(data.error || 'Failed to post');
      }
    } catch (err) {
      setError('Failed to post');
      console.error('Error posting:', err);
    } finally {
      setIsPosting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        background: colors.surface,
        borderRadius: '16px',
        padding: '24px',
        width: '90%',
        maxWidth: '500px',
        maxHeight: '90vh',
        overflow: 'auto',
        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px'
        }}>
          <h2 style={{
            color: colors.text.primary,
            fontSize: '20px',
            fontWeight: '600',
            margin: 0
          }}>
            Schedule Post
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: '4px',
              borderRadius: '4px'
            }}
          >
            <X size={20} color={colors.text.tertiary} />
          </button>
        </div>

        {error && (
          <div style={{
            padding: '12px',
            background: '#FEE2E2',
            border: '1px solid #FECACA',
            borderRadius: '8px',
            color: '#DC2626',
            fontSize: '14px',
            marginBottom: '16px'
          }}>
            {error}
          </div>
        )}

        {checkingConnection ? (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            color: colors.text.secondary
          }}>
            Checking X account connection...
          </div>
        ) : !xAccountConnected ? (
          <div style={{
            textAlign: 'center',
            padding: '40px'
          }}>
            <Twitter size={48} color={colors.text.tertiary} style={{ marginBottom: '16px' }} />
            <h3 style={{
              color: colors.text.primary,
              fontSize: '18px',
              fontWeight: '600',
              marginBottom: '8px'
            }}>
              Connect Your X Account
            </h3>
            <p style={{
              color: colors.text.secondary,
              fontSize: '14px',
              marginBottom: '20px',
              lineHeight: '1.5'
            }}>
              You need to connect your X (Twitter) account before you can schedule posts.
            </p>
            <button
              onClick={() => window.location.href = '/settings?tab=integrations'}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '12px 24px',
                background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                margin: '0 auto'
              }}
            >
              <Twitter size={16} />
              Go to Integrations
              <ExternalLink size={14} />
            </button>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
          {/* Content */}
          <div style={{ marginBottom: '16px' }}>
            <label style={{
              display: 'block',
              color: colors.text.primary,
              fontSize: '14px',
              fontWeight: '600',
              marginBottom: '8px'
            }}>
              Content
            </label>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="What's happening?"
              style={{
                width: '100%',
                padding: '12px',
                border: `1px solid ${colors.border}`,
                borderRadius: '8px',
                fontSize: '14px',
                minHeight: '100px',
                resize: 'vertical',
                outline: 'none'
              }}
              maxLength={280}
            />
            <div style={{
              textAlign: 'right',
              fontSize: '12px',
              color: colors.text.tertiary,
              marginTop: '4px'
            }}>
              {content.length}/280
            </div>
          </div>

          {/* Date and Time */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '12px',
            marginBottom: '20px'
          }}>
            <div>
              <label style={{
                display: 'block',
                color: colors.text.primary,
                fontSize: '14px',
                fontWeight: '600',
                marginBottom: '8px'
              }}>
                <Calendar size={14} style={{ marginRight: '4px', verticalAlign: 'middle' }} />
                Date
              </label>
              <input
                type="date"
                value={scheduledDate}
                onChange={(e) => setScheduledDate(e.target.value)}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  fontSize: '14px',
                  outline: 'none'
                }}
              />
            </div>
            <div>
              <label style={{
                display: 'block',
                color: colors.text.primary,
                fontSize: '14px',
                fontWeight: '600',
                marginBottom: '8px'
              }}>
                <Clock size={14} style={{ marginRight: '4px', verticalAlign: 'middle' }} />
                Time
              </label>
              <input
                type="time"
                value={scheduledTime}
                onChange={(e) => setScheduledTime(e.target.value)}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  fontSize: '14px',
                  outline: 'none'
                }}
              />
            </div>
          </div>

          {/* Buttons */}
          <div style={{
            display: 'flex',
            gap: '12px',
            justifyContent: 'flex-end'
          }}>
            <button
              type="button"
              onClick={handlePostNow}
              disabled={!content.trim() || isPosting}
              style={{
                padding: '12px 20px',
                background: colors.background,
                color: colors.text.primary,
                border: `1px solid ${colors.border}`,
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: !content.trim() || isPosting ? 'not-allowed' : 'pointer',
                opacity: !content.trim() || isPosting ? 0.6 : 1,
                display: 'flex',
                alignItems: 'center',
                gap: '6px'
              }}
            >
              <Send size={14} />
              Post Now
            </button>
            <button
              type="submit"
              disabled={!content.trim() || !scheduledDate || !scheduledTime || isPosting}
              style={{
                padding: '12px 20px',
                background: (!content.trim() || !scheduledDate || !scheduledTime || isPosting)
                  ? colors.text.tertiary
                  : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: (!content.trim() || !scheduledDate || !scheduledTime || isPosting) ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '6px'
              }}
            >
              <Calendar size={14} />
              {isPosting ? 'Scheduling...' : 'Schedule Post'}
            </button>
          </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default SchedulePostModal;
