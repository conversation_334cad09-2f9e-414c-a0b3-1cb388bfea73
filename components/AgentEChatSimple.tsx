import React, { useState, useRef, useEffect } from 'react';
import { Bo<PERSON>, Send, Loader, CheckCircle, X } from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'agent';
  content: string;
  timestamp: Date;
  status?: 'sending' | 'sent' | 'posted';
}

interface AgentEChatProps {
  isOpen: boolean;
  onClose: () => void;
  currentContent: string;
  userId?: string;
}

const AgentEChatSimple: React.FC<AgentEChatProps> = ({ isOpen, onClose, currentContent, userId }) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'agent',
      content: "Hi! I'm Agent E. I can help you post, schedule, or improve your content. What would you like me to do?",
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [userSettings, setUserSettings] = useState<any>(null);
  const [postedTweets, setPostedTweets] = useState<any[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    surface: '#FFFFFF',
    background: '#FFF8F3',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037'
    },
    border: '#F5E6D3'
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load user settings and posted tweets when component opens
  useEffect(() => {
    if (isOpen && userId) {
      loadUserData();
    }
  }, [isOpen, userId]);

  const loadUserData = async () => {
    if (!userId) return;

    try {
      // Load user settings
      const settingsResponse = await fetch(`/api/agent-e/settings?userId=${userId}`);
      if (settingsResponse.ok) {
        const settingsData = await settingsResponse.json();
        setUserSettings(settingsData.settings);
      }

      // Load posted tweets
      const tweetsResponse = await fetch(`/api/twitter/posted?userId=${userId}`);
      if (tweetsResponse.ok) {
        const tweetsData = await tweetsResponse.json();
        setPostedTweets(tweetsData.postedContent || []);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const handleAgentAction = async (userMessage: string): Promise<string> => {
    const lowerMessage = userMessage.toLowerCase();

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Get user context for personalized responses
    const projectName = userSettings?.project_name || 'your project';
    const brandVoice = userSettings?.brand_voice || 'professional';
    const targetAudience = userSettings?.target_audience || 'your audience';
    const recentTweets = postedTweets.slice(0, 3).map(tweet => tweet.content);

    if (lowerMessage.includes('post now') || lowerMessage.includes('publish')) {
      return `✅ Posted to X successfully! Your content "${currentContent.substring(0, 50)}..." is now live and reaching ${targetAudience}!`;
    }

    if (lowerMessage.includes('schedule')) {
      return `⏰ Scheduled your post for 9:00 AM tomorrow. Based on your ${brandVoice} brand voice, I'll post it at the optimal time for ${targetAudience} engagement.`;
    }

    if (lowerMessage.includes('improve') || lowerMessage.includes('better')) {
      let improvedContent = currentContent;

      // Apply brand voice
      if (brandVoice === 'casual') {
        improvedContent += ' 🚀 What do you think?';
      } else if (brandVoice === 'professional') {
        improvedContent += ' #innovation #growth';
      } else if (brandVoice === 'friendly') {
        improvedContent += ' 😊 Hope this helps!';
      }

      return `✨ Here's an improved version for ${projectName} with your ${brandVoice} voice:\n\n"${improvedContent}"\n\nI've tailored it for ${targetAudience} and matched your brand voice!`;
    }

    if (lowerMessage.includes('thread')) {
      return `🧵 I can break this into a 3-part thread for ${projectName}. Based on your previous posts, I'll maintain your ${brandVoice} voice and target ${targetAudience}. Should I create the thread structure?`;
    }

    if (lowerMessage.includes('analyze') || lowerMessage.includes('performance')) {
      if (recentTweets.length > 0) {
        return `📊 Based on your recent ${recentTweets.length} posts, I notice you often write about similar themes. Your ${brandVoice} voice is consistent. Would you like me to suggest new content angles for ${targetAudience}?`;
      } else {
        return `📊 I don't see any recent posts yet. Once you start posting, I can analyze your content performance and suggest improvements for ${targetAudience}.`;
      }
    }

    if (lowerMessage.includes('ideas') || lowerMessage.includes('content')) {
      const customPrompts = userSettings?.custom_prompts || [];
      if (customPrompts.length > 0) {
        const randomPrompt = customPrompts[Math.floor(Math.random() * customPrompts.length)];
        return `💡 Based on your custom prompt "${randomPrompt.name}", here's an idea for ${projectName}:\n\n"${randomPrompt.prompt}"\n\nShould I create content using this approach for ${targetAudience}?`;
      } else {
        return `💡 I can generate content ideas for ${projectName}! Set up custom prompts in Settings to get personalized suggestions for ${targetAudience}.`;
      }
    }

    // Personalized default response
    const settingsInfo = userSettings ? `I know about ${projectName} and your ${brandVoice} brand voice for ${targetAudience}.` : 'Connect your settings for personalized help!';

    return `I can help you with ${projectName}:\n• Post now to X\n• Schedule for later\n• Improve your content\n• Create a thread\n• Analyze performance\n• Generate content ideas\n\n${settingsInfo}\n\nWhat would you like to do?`;
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      const agentResponse = await handleAgentAction(inputValue);

      const agentMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: agentResponse,
        timestamp: new Date(),
        status: inputValue.toLowerCase().includes('post now') ? 'posted' : 'sent'
      };

      setMessages(prev => [...prev, agentMessage]);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const quickActions = [
    { label: '🚀 Post Now', action: 'post now' },
    { label: '⏰ Schedule', action: 'schedule this' },
    { label: '✨ Improve', action: 'improve this' },
    { label: '🧵 Thread', action: 'make thread' }
  ];

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      width: '400px',
      height: '500px',
      background: colors.surface,
      borderRadius: '16px',
      boxShadow: '0 12px 48px rgba(0, 0, 0, 0.15)',
      border: `1px solid ${colors.border}`,
      display: 'flex',
      flexDirection: 'column',
      zIndex: 10000,
      overflow: 'hidden'
    }}>
      {/* Header */}
      <div style={{
        padding: '16px 20px',
        borderBottom: `1px solid ${colors.border}`,
        background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Bot size={20} color="white" />
          <div>
            <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600' }}>Agent E</h3>
            <p style={{ margin: 0, fontSize: '12px', opacity: 0.8 }}>AI Assistant</p>
          </div>
        </div>
        <button
          onClick={onClose}
          style={{
            background: 'none',
            border: 'none',
            color: 'white',
            cursor: 'pointer',
            padding: '4px'
          }}
        >
          <X size={18} />
        </button>
      </div>

      {/* Messages */}
      <div style={{
        flex: 1,
        padding: '16px',
        overflowY: 'auto',
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }}>
        {messages.map((message) => (
          <div
            key={message.id}
            style={{
              display: 'flex',
              justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start'
            }}
          >
            <div style={{
              maxWidth: '80%',
              padding: '10px 14px',
              borderRadius: message.type === 'user' ? '16px 16px 4px 16px' : '16px 16px 16px 4px',
              background: message.type === 'user'
                ? `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`
                : colors.background,
              color: message.type === 'user' ? 'white' : colors.text.primary,
              fontSize: '14px',
              lineHeight: '1.4',
              whiteSpace: 'pre-wrap'
            }}>
              {message.content}
              {message.status === 'posted' && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  marginTop: '6px',
                  fontSize: '12px',
                  opacity: 0.8
                }}>
                  <CheckCircle size={12} />
                  <span>Posted</span>
                </div>
              )}
            </div>
          </div>
        ))}

        {isLoading && (
          <div style={{ display: 'flex', justifyContent: 'flex-start' }}>
            <div style={{
              padding: '10px 14px',
              borderRadius: '16px 16px 16px 4px',
              background: colors.background,
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <Loader size={14} style={{ animation: 'spin 1s linear infinite' }} color={colors.primary} />
              <span style={{ fontSize: '14px', color: colors.text.secondary }}>
                Agent E is working...
              </span>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Quick Actions */}
      <div style={{
        padding: '12px 16px 0',
        borderTop: `1px solid ${colors.border}`
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(2, 1fr)',
          gap: '8px',
          marginBottom: '12px'
        }}>
          {quickActions.map((button, index) => (
            <button
              key={index}
              onClick={() => setInputValue(button.action)}
              style={{
                padding: '8px 12px',
                background: colors.background,
                border: `1px solid ${colors.border}`,
                borderRadius: '8px',
                fontSize: '12px',
                color: colors.text.secondary,
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.background = colors.primary + '20';
                target.style.borderColor = colors.primary;
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.background = colors.background;
                target.style.borderColor = colors.border;
              }}
            >
              {button.label}
            </button>
          ))}
        </div>
      </div>

      {/* Input */}
      <div style={{ padding: '0 16px 16px' }}>
        <div style={{ display: 'flex', gap: '8px', alignItems: 'flex-end' }}>
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask Agent E to help with your content..."
            style={{
              flex: 1,
              padding: '10px 12px',
              border: `1px solid ${colors.border}`,
              borderRadius: '12px',
              fontSize: '14px',
              background: colors.surface,
              outline: 'none'
            }}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            style={{
              padding: '10px',
              background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
              color: 'white',
              border: 'none',
              borderRadius: '12px',
              cursor: inputValue.trim() && !isLoading ? 'pointer' : 'not-allowed',
              opacity: inputValue.trim() && !isLoading ? 1 : 0.5
            }}
          >
            <Send size={16} />
          </button>
        </div>
      </div>

      <style jsx>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default AgentEChatSimple;
