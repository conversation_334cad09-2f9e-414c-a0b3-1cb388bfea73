import { Twitter<PERSON><PERSON> } from '..';
import * as dotenv from 'dotenv';
dotenv.config({ path: __dirname + '/../../.env' });
/** User OAuth 1.0a client */
export function getUserClient() {
    return new TwitterApi({
        appKey: process.env.CONSUMER_TOKEN,
        appSecret: process.env.CONSUMER_SECRET,
        accessToken: process.env.OAUTH_TOKEN,
        accessSecret: process.env.OAUTH_SECRET,
    });
}
export function getUserKeys() {
    return {
        appKey: process.env.CONSUMER_TOKEN,
        appSecret: process.env.CONSUMER_SECRET,
        accessToken: process.env.OAUTH_TOKEN,
        accessSecret: process.env.OAUTH_SECRET,
    };
}
export async function sleepTest(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
/** User-unlogged OAuth 1.0a client */
export function getRequestClient() {
    return new Twitter<PERSON><PERSON>({
        appKey: process.env.CONSUMER_TOKEN,
        appSecret: process.env.CONSUMER_SECRET,
    });
}
export function getRequestKeys() {
    return {
        appKey: process.env.CONSUMER_TOKEN,
        appSecret: process.env.CONSUMER_SECRET,
    };
}
// Test auth 1.0a flow
export function getAuthLink(callback) {
    return getRequestClient().generateAuthLink(callback);
}
export async function getAccessClient(verifier) {
    const requestClient = new TwitterApi({
        appKey: process.env.CONSUMER_TOKEN,
        appSecret: process.env.CONSUMER_SECRET,
        accessToken: process.env.OAUTH_TOKEN,
        accessSecret: process.env.OAUTH_SECRET,
    });
    const { client } = await requestClient.login(verifier);
    return client;
}
/** App OAuth 2.0 client */
export function getAppClient() {
    let requestClient;
    if (process.env.BEARER_TOKEN) {
        requestClient = new TwitterApi(process.env.BEARER_TOKEN);
        return Promise.resolve(requestClient);
    }
    else {
        requestClient = new TwitterApi({
            appKey: process.env.CONSUMER_TOKEN,
            appSecret: process.env.CONSUMER_SECRET,
        });
        return requestClient.appLogin();
    }
}
