import { TypeOrArrayOf } from '../shared.types';
import { TTweetv2MediaField, TTweetv2TweetField, TTweetv2UserField } from './tweet.v2.types';
import { ApiV2Includes, ReferencedTweetV2 } from './tweet.definition.v2';
import { DataMetaAndIncludeV2, PaginableCountMetaV2 } from './shared.v2.types';
export declare type TDMEventV2Field = 'id' | 'text' | 'event_type' | 'created_at' | 'dm_conversation_id' | 'sender_id' | 'participant_ids' | 'referenced_tweets' | 'attachments';
export declare type TDMEventV2Expansion = 'attachments.media_keys' | 'referenced_tweets.id' | 'sender_id' | 'participant_ids';
export declare type TDMEventV2EventType = 'MessageCreate' | 'ParticipantsJoin' | 'ParticipantsLeave';
export interface GetDMEventV2Params {
    'dm_event.fields': TypeOrArrayOf<TDMEventV2Field> | string;
    event_types: TypeOrArrayOf<TDMEventV2EventType> | string;
    expansions: TypeOrArrayOf<TDMEventV2Expansion> | string;
    max_results: number;
    'media.fields': TypeOrArrayOf<TTweetv2MediaField> | string;
    pagination_token: string;
    'tweet.fields': TypeOrArrayOf<TTweetv2TweetField> | string;
    'user.fields': TypeOrArrayOf<TTweetv2UserField> | string;
}
export declare type GetDMEventV2Result = DataMetaAndIncludeV2<DMEventV2[], PaginableCountMetaV2, ApiV2Includes>;
export interface PostDMInConversationParams {
    attachments?: [{
        media_id: string;
    }];
    text?: string;
}
export interface CreateDMConversationParams {
    conversation_type: 'Group';
    participant_ids: string[];
    message: PostDMInConversationParams;
}
export interface PostDMInConversationResult {
    dm_conversation_id: string;
    dm_event_id: string;
}
export interface BaseDMEventV2 {
    id: string;
    created_at?: string;
    sender_id?: string;
    dm_conversation_id?: string;
    attachments?: DMEventAttachmentV2;
    referenced_tweets?: ReferencedTweetV2[];
    participant_ids?: string[];
}
export interface DMEventAttachmentV2 {
    media_keys: string[];
}
export declare type DMEventV2 = ({
    event_type: 'MessageCreate';
    text: string;
} & BaseDMEventV2) | ({
    event_type: Extract<TDMEventV2EventType, 'ParticipantsJoin' | 'ParticipantsLeave'>;
} & BaseDMEventV2);
