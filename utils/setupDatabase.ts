import { supabase } from '../lib/supabase';

export const checkDatabaseSetup = async () => {
  try {
    // Try to query the user_profiles table
    const { data, error } = await supabase
      .from('user_profiles')
      .select('id')
      .limit(1);

    if (error) {
      if (error.code === '42P01' || error.message?.includes('relation "user_profiles" does not exist')) {
        return {
          isSetup: false,
          error: 'Database tables not found. Please run the setup SQL in your Supabase dashboard.',
          needsSetup: true
        };
      }
      return {
        isSetup: false,
        error: error.message,
        needsSetup: false
      };
    }

    return {
      isSetup: true,
      error: null,
      needsSetup: false
    };
  } catch (error) {
    return {
      isSetup: false,
      error: 'Failed to check database setup',
      needsSetup: false
    };
  }
};

export const createUserProfileIfNotExists = async (userId: string, userData: any) => {
  try {
    // First check if profile exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      throw fetchError;
    }

    if (existingProfile) {
      return { data: existingProfile, error: null };
    }

    // Create new profile
    const { data: newProfile, error: createError } = await supabase
      .from('user_profiles')
      .insert({
        user_id: userId,
        full_name: userData.full_name || 'User',
        plan: 'free',
        subscription_status: 'inactive',
        is_online: true
      })
      .select()
      .single();

    return { data: newProfile, error: createError };
  } catch (error) {
    return { data: null, error };
  }
};
